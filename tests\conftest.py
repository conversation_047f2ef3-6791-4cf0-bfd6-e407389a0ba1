"""Conftest for all tests in the Olympus project.

References
----------
https://docs.pytest.org/en/7.1.x/reference/fixtures.html#conftest-py-sharing-fixtures-across-multiple-files
"""

import pytest
from pytest_mock import Mo<PERSON><PERSON>ix<PERSON>

from olympus_common.utils import <PERSON>ton
from tests.utils import dummy_environ


def pytest_addoption(parser):
    """Add custom command-line options to pytest.

    References
    ----------
    https://docs.pytest.org/en/7.1.x/reference/reference.html#pytest.hookspec.pytest_addoption
    """
    parser.addoption("--run-e2e", action="store_true", default=False, help="Run end-to-end tests")


def pytest_collection_modifyitems(config, items):
    """Modify the collection of test items to run only the selected tests.

    When --run-e2e is passed, only the tests marked with the "e2e" marker will be run.
    When --run-e2e is not passed, only the tests not marked with the "e2e" marker will be run.
    """
    run_e2e_option = config.getoption("--run-e2e")
    skip_e2e = pytest.mark.skip(reason="need --run-e2e option to run")
    selected_items = []
    for item in items:
        if "e2e" in item.keywords:
            if run_e2e_option:
                selected_items.append(item)
            else:
                item.add_marker(skip_e2e)
        else:
            if not run_e2e_option:
                selected_items.append(item)

    # Pytest expects the items list to be modified in-place
    items[:] = selected_items


@pytest.fixture(autouse=True)
def _reset_singleton():
    """Reset Singleton's instance-map.

    This is required because tests are ran in one process and Singleton would keep already initialized objects in
    memory. By resetting _instances, each test can initialize their own instance of a Singleton.
    """
    Singleton._instances = {}


@pytest.fixture(autouse=True)
def _mock_env(mocker: MockerFixture):
    """Mock os.environ for every test in this test suite.

    References
    ----------
    https://docs.pytest.org/en/6.2.x/fixture.html#autouse-fixtures-fixtures-you-don-t-have-to-request
    """
    mocker.patch.dict("os.environ", dummy_environ(), clear=True)
