---
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  labels:
    app: core-api-app-#{appEnv}#
  name: a2110-core-api-route-#{appEnv}#
  namespace: a2110-olympus-monitoring
spec:
  host: a2110-core-api-#{appEnv}#.#{openshiftEnv}#
  to:
    kind: Service
    name: a2110-core-api-service-#{appEnv}#
    weight: 100
  port:
    targetPort: http
  wildcardPolicy: None
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
