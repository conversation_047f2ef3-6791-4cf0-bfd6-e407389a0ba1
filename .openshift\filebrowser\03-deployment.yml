---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: filebrowser-#{appEnv}#
  namespace: a2110-olympus-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: filebrowser-#{appEnv}#
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: filebrowser-#{appEnv}#
    spec:
      containers:
        - args:
            - --root=/data
            - --address=0.0.0.0
            - --database=/config/filebrowser.db
            - --log=stdout
          image: artifactory.msnet.railb.be/docker/hurlenko/filebrowser
          livenessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 15
          name: filebrowser
          ports:
            - containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 200m
              memory: 64Mi
            requests:
              cpu: 100m
              memory: 32Mi
          volumeMounts:
            - mountPath: /data/
              name: a2110-olympus
            - mountPath: /config
              name: vol-config
      volumes:
        - name: a2110-olympus
          persistentVolumeClaim:
            claimName: a2110-olympus-volume-claim-#{appEnv}#
        - name: vol-config
          persistentVolumeClaim:
            claimName: pvc-filebrowser-vol-config-#{appEnv}#
