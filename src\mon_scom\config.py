"""Configuration module for mon-scom."""

from dataclasses import dataclass

from olympus_common.config import DatabaseKafkaConsumerServiceConfig
from olympus_common.dataclass import env_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseKafkaConsumerServiceConfig, metaclass=Singleton):
    """Represent the configuration for the SCOM DD."""

    app_env: str = env_field("APP_ENV", default="dev")


config = Config()
