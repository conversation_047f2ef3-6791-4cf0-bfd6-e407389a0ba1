"""Manage jobs for the synchronisation with UCMDB."""

import logging

from icinga_objects.model import ci_relations, cis, metrics
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType


@trace_scan(MeasureType.CUSTOM.value)
def cis_job(ci_type: str) -> None:
    """Synchronise CIs."""
    logging.info(f"-- Starting CI's job for {ci_type} --")
    c = cis.CIs(ci_type)
    c.sync_with_icinga()
    logging.info(f"-- Finished CI's job for {ci_type} --")


@trace_scan(MeasureType.CUSTOM.value)
def metrics_job(source: str) -> None:
    """Add metrics in Icinga."""
    logging.info(f"Starting metrics job for {source}")
    gmt = metrics.GroupMetric(source)
    gmt.sync_with_icinga()
    logging.info(f"Finished metrics job for {source}")


@trace_scan(MeasureType.CUSTOM.value)
def relation_job() -> None:
    """Synchronise Relations."""
    logging.info("-- Starting relation job --")
    relation = ci_relations.CIRelations()
    relation.sync_with_icinga()
    logging.info("-- Finished relation job --")
