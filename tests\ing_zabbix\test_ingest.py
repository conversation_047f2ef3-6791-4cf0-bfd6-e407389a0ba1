"""Tests for the ingest module."""

import json
from pathlib import Path

from ing_zabbix.ingest import run


def test_ingest():
    """Test the ingest function.

    The data was obtained by running the application (before using application.run_forever) and saving the data to a
    file.
    Unless a bug is found in this data, this test can be used as a regression test. (ie: if the inner working of `run`
    is changed, this test must still pass)
    # TODO: Filter the data to be more unique, this will make the test more robust adnd fast.
    """
    ingest_data_path = Path(__file__).parent / "data" / "ingest_since_1713391261.json"
    ingest_data_1 = json.loads(ingest_data_path.read_text())

    hostresult_data_path = Path(__file__).parent / "data" / "hostresult_since_1713391261.json"
    hostresult_data = json.loads(hostresult_data_path.read_text())

    result_data_path = Path(__file__).parent / "data" / "result_since_1713391261.json"
    result_data = json.loads(result_data_path.read_text())

    events = ingest_data_1["result"]
    hosts = hostresult_data["result"]

    result = run(events, hosts)

    result_serialized = json.dumps(result)

    result_json = json.loads(result_serialized)

    assert result_json == result_data
