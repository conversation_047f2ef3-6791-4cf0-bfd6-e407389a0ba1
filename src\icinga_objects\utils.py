"""Module to provide generic utilities to an application."""

import ast
import hashlib
from typing import Any, Callable, Union

import numpy as np
import pandas as pd

from olympus_common.icinga import compute_host_group, compute_service_group, encode_string


def columns_to_list(column_name: pd.Series):
    """Convert a series into a list for the aggregation."""
    unique_series = column_name.unique()
    return unique_series.tolist()


def delete_nan_none(value: Any) -> list:
    """Check if the value is a single NaN or None."""
    if isinstance(value, list) and len(value) == 1:
        if value[0] in [np.nan, None]:
            return []
    return value


def aggregated_df(df: pd.DataFrame) -> pd.DataFrame:
    """Aggregate the dataframe based on the columns."""
    if not df.empty and has_dashboard_field(df):
        aggregation_actions: dict[str, Union[Callable[[pd.Series], list[Any]], str]] = {
            col: columns_to_list if col in ["dashboardName", "top_level", "linked_environments"] else "first"
            for col in list(df.columns)
        }
        # apply aggregation
        aggregated_cis_to_add = df.groupby("floc_id").agg(aggregation_actions).reset_index(drop=True)
        return aggregated_cis_to_add
    else:
        return df


def to_add(
    df_ucmdb: pd.DataFrame,
    df_icinga: pd.DataFrame,
    merge_columns: list | None = None,
) -> pd.DataFrame:
    """Compute new CIs to be added from two dataframes, one represents ucmdb data and the other icinga data.

    Parameters
    ----------
    df_ucmdb : pd.DataFrame
        UCMDB dataframe.
    df_icinga : pd.DataFrame
        Icinga Dataframe.
    merge_columns : list | None
        Merge columns. Can be None.

    Returns
    -------
    pd.DataFrame
        The dataframe to add.
    """
    if merge_columns is None:
        merge_columns = set_merge_columns(df_ucmdb)

    if not df_icinga.empty:
        merged = df_ucmdb.merge(df_icinga, how="outer", on=merge_columns, indicator=True)
        cis_to_add = merged[merged["_merge"] == "left_only"]
        cis_to_add = cis_to_add.filter(regex=r"^(?!.*_y)")
        cis_to_add = cis_to_add.rename(columns=lambda x: x.replace("_x", ""))
        cis_to_add = cis_to_add.drop(columns=["_merge"])
        return cis_to_add

    else:
        return df_ucmdb


def to_update(
    df_ucmdb: pd.DataFrame,
    df_icinga: pd.DataFrame,
    fields_to_ignore: list | None = None,
    merge_columns: list | None = None,
) -> pd.DataFrame:
    """Compute new CIs to be updated from two dataframes, one represent ucmdb data and the other icinga data.

    Notes
    -----
    -- **columns_to_ignore:**
        - **LastSync0n:** this field is ignored for the calculation of updates because
    the UCMDB does a synchronization every day and I would not want Icinga to make updates every time.
        - **lastModifiedOn:** this field is not always filled in and often contains biased data.
        - **location:** this field contains a dictionary with a certain order coming from the UCMDB, icinga changes this
            order by default which leads to an error in the calculation of the data to be updated.

        - **barcode**: this field is managed as a real by UCMDB, but as an integer by Icinga.
    -- **columns not ignore**:
        - **complianceIR**: this field isn't ignore, but sometimes it's updated because icinga add a bullet on the
            "True" boolean.
        - **complianceIC**: this field isn't ignore, but sometimes it's updated because icinga add a bullet on the
            "True" boolean.

    Parameters
    ----------
    fields_to_hash: list
    df_ucmdb : pd.DataFrame
        UCMDB dataframe.
    df_icinga : pd.DataFrame
        Icinga Dataframe.
    fields_to_ignore : list | None
        columns to ignore.
    merge_columns : list | None
        merge columns.


    Returns
    -------
    pd.DataFrame
        The dataframe to update.
    """
    if merge_columns is None:
        merge_columns = set_merge_columns(df_ucmdb)

    df_ucmdb = drop_columns(df_ucmdb, fields_to_ignore)
    df_icinga = drop_columns(df_icinga, fields_to_ignore)

    if not df_icinga.empty:
        merged = df_ucmdb.merge(df_icinga, how="inner", on=merge_columns, indicator=True)
        merged = merged[merged["_merge"] == "both"]
        # ucmdb
        df_ucmdb = merged.filter(regex=r"^(?!.*_y)")
        df_ucmdb = df_ucmdb.rename(columns=lambda x: x.replace("_x", ""))
        df_ucmdb = df_ucmdb.drop(["_merge"], axis=1)
        df_ucmdb = df_ucmdb.fillna("")
        df_ucmdb = df_ucmdb.astype(str)

        # icinga
        df_icinga = merged.filter(regex=r"^(?!.*_x)")
        df_icinga = df_icinga.rename(columns=lambda x: x.replace("_y", ""))
        df_icinga = df_icinga.drop(["_merge"], axis=1)
        df_icinga = df_icinga.fillna("")
        df_icinga = df_icinga.astype(str)
        merged = df_ucmdb.merge(df_icinga, on=list(df_ucmdb), indicator=True, how="left")
        cis_to_update = merged[merged["_merge"] == "left_only"]
        cis_to_update = cis_to_update.drop(columns=["_merge"])
        restore_list_columns(cis_to_update)
        return cis_to_update
    else:
        return pd.DataFrame()


def to_del(df_ucmdb: pd.DataFrame, df_icinga: pd.DataFrame, merge_columns: list | None = None) -> pd.DataFrame:
    """Compute new CIs to be deleted from two dataframes, one represent ucmdb data and the other icinga data.

    Parameters
    ----------
    df_ucmdb : pd.DataFrame
        UCMDB dataframe.
    df_icinga : pd.DataFrame
        Icinga Dataframe.
    merge_columns : list | None
        merge columns.

    Returns
    -------
    pd.DataFrame
        The dataframe to delete.
    """
    if merge_columns is None:
        merge_columns = set_merge_columns(df_ucmdb)
    if not df_icinga.empty:
        merged = df_ucmdb.merge(df_icinga, how="outer", on=merge_columns, indicator=True)
        cis_to_del = merged[merged["_merge"] == "right_only"]
        cis_to_del = cis_to_del.filter(regex=r"^(?!.*_x)")
        cis_to_del = cis_to_del.rename(columns=lambda x: x.replace("_y", ""))
        cis_to_del = cis_to_del.drop(columns=["_merge"])
        return cis_to_del
    else:
        return pd.DataFrame()


def ensure_list(value: str | list) -> list:
    """Ensure value is a list.

    Parameters
    ----------
    value : str | list
        The value to ensure.

    Returns
    -------
    list
        The list of value.
    """
    return [value] if isinstance(value, str) else value


def has_dashboard_field(df: pd.DataFrame) -> bool:
    """Check if the dashboard field is present in the dataframe."""
    return (
        True
        if "dashboardName" in list(df.columns)
        or "top_level" in list(df.columns)
        or "linked_environment" in list(df.columns)
        else False
    )


def set_merge_columns(df_ucmdb: pd.DataFrame) -> list:
    """Set merge column between ucmdb and icinga data.

    Parameters
    ----------
    df_ucmdb : pd.DataFrame
        Initial dataframe.

    Returns
    -------
    list
        Columns to merge.
    """
    return ["floc_id"] if "floc_id" in list(df_ucmdb.columns) else ["identification"]


def drop_columns(df: pd.DataFrame, columns_to_drop: list[str] | None) -> pd.DataFrame:
    """Drop specifics columns in a df.

    Parameters
    ----------
    df : pd.DataFrame
        Initial dataframe.
    columns_to_drop : list[str] | None
        Columns to drop

    Returns
    -------
    pd.DataFrame
        Updated Dataframe
    """
    if columns_to_drop:
        to_drop = [columns for columns in columns_to_drop if columns in list(df.columns)]
        return df.drop(to_drop, axis=1)
    return df


def replace_str(df: pd.DataFrame, col: str, to_replace: str, value: str = "") -> pd.DataFrame:
    """Replace a str in the column a dataframe.

    Parameters
    ----------
    df : pd.DataFrame
        Initial dataframe.
    col : str
        Column to modify.
    to_replace : str
        String to replace.
    value : str
        New string.

    Returns
    -------
    pd.DataFrame
        Updated Dataframe
    """
    df[col] = df[col].str.replace(to_replace, value)
    return df


def format_host_data(df: pd.DataFrame) -> pd.DataFrame:
    """Format the host data for Icinga.

    Parameters
    ----------
    df : pd.DataFrame
        Initial dataframe.

    Returns
    -------
    pd.DataFrame
        The formatted data to send to Icinga.
    """
    df = df.add_prefix("vars.")
    df["check_command"] = "passive"
    df = df.fillna("")
    df["display_name"] = df.apply(lambda x: x["vars.identification"] or x["vars.floc_id"], axis=1)
    df["groups"] = df[["vars.ciType", "vars.floc_class"]].apply(
        lambda x: compute_host_group(x["vars.ciType"], x["vars.floc_class"]), axis=1
    )
    if "vars.ci_uuid" in list(df.columns):
        df = df.drop(columns=["vars.ci_uuid"])
    return df


def format_service_data(df: pd.DataFrame) -> pd.DataFrame:
    """Format the service data for icinga."""
    df = df[
        [
            "identification",
            "floc_id",
            "source",
            "metric_type",
            "metric_name",
            "action_class",
            "metric_description",
            "instructions",
        ]
    ]
    df = df.add_prefix("vars.")
    df = handle_escaping_df(df)
    df["display_name"] = df["vars.metric_name"].str.lower()
    df["groups"] = df[["vars.source", "vars.action_class"]].apply(
        lambda x: compute_service_group(x["vars.source"], x["vars.action_class"]), axis=1
    )
    df["notes"] = df["vars.instructions"]
    df["check_command"] = "passive"
    return df


def handle_escaping_df(df: pd.DataFrame, symbol: str = "$") -> pd.DataFrame:
    """Handle the escaping in the dataframe."""
    if "vars.instructions" in list(df.columns) and df["vars.instructions"].str.contains(symbol).any():
        url_encoded_symbol = encode_string(symbol)
        df["vars.instructions"] = df["vars.instructions"].str.replace(symbol, url_encoded_symbol)
        df["vars.metric_description"] = df["vars.metric_description"].str.replace(symbol, url_encoded_symbol)
    return df


def sort_dataframe_field(field_to_sort: str | list[str] | None) -> str | list[str] | None:
    """Sort the list in the provided field."""
    if not field_to_sort:
        return field_to_sort

    if isinstance(field_to_sort, str):
        deserialized_field = ast.literal_eval(field_to_sort)
    elif isinstance(field_to_sort, list):
        deserialized_field = field_to_sort
    else:
        return None

    # Eliminate the None and empty string elements from the list
    deserialized_field = [x for x in deserialized_field if x]

    return sorted(deserialized_field)


def restore_list_columns(df: pd.DataFrame) -> None:
    """Restore some columns from str to list."""
    for column in ["dashboardName", "top_level", "linked_environments"]:
        if column in list(df.columns):
            df[column] = df[column].apply(lambda x: ast.literal_eval(x) if x else [])
    return


def sort_list_columns(df: pd.DataFrame) -> None:
    """Sort list in a column."""
    for column in ["dashboardName", "top_level", "linked_environments"]:
        if column in list(df.columns):
            df[column] = df[column].apply(lambda x: sort_dataframe_field(x))
    return


def hash_string(s: str) -> str:
    """Hash a str."""
    return hashlib.sha256(s.encode("utf-8")).hexdigest()


def hash_df_columns(columns_to_hash: list, df: pd.DataFrame) -> pd.DataFrame:
    """Hash a list of df columns and set it in new hash columns."""
    for col in columns_to_hash:
        if col in list(df.columns):
            df[f"{col}_hash"] = df[col].apply(lambda x: hash_string(str(x)))
    return df
