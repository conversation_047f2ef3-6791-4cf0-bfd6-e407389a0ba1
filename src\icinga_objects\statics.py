"""Statics for icinga-objects."""

SOURCES = [
    # Validated
    "Zabbix",
    "WebSphere-MQ",
    "Struxurware",
    "Stonebranch",
    "SolMan",
    "SCADA",
    "PEM",
    "HA",
    "DCMS",
    "AIRCO",
    "Local6",
    "vRealize",
    "SCOM",
    "OCC",
    "OpenShift",
    "FTP",
    "GSX_Monitor",
    "prometheus",
    "Netact",
    "Olympus",
    "CA_Spectrum",  # too many metrics for the openshift pod
    "DataLines",
    "DICA",
    # Not validated
    # "C-NMS",
    # "EMMA",
    # "Milestone",
    # "Monyog",
    # "NFM-P",  #too many metrics for the openshift pod
    # "OpenShift",
    # "Ping-Probe",
    # "SynchroGillam",
    # "Tivoli-Netcool",
    # "Tivoli-ISM",
    # "Tivoli-RRT",
    # "Tivoli-ITM",
    # "Tivoli-ITCAM",
    # "ADVA",
    # "DICA",
    # "eValid",
    # "FiberMonitoring",
    # "KP-Monitor",
    # "LucentOMS",
    # "MAPIC"
    # "Perf-iT",
    # "SkyWalker",
]

CI_TYPE = [
    "Cluster",
    "Application",
    "ApplicationService",
    "ComputerSystem",
    "Database",
    "Environment",
    "ResourcePool",
    "SoftwareServer",
    "SubService",
    "AdminDomain",
    "ExtApplication",
    "CloudRegion",
    "CloudService",
    "Product",
    "Equipment",
    # "Document",
]

COLUMNS_HIERARCHY_RENAMING = {
    "application": "top_level",
    "dashboard": "dashboardName",
    "environment": "linked_environments",
    "ci_identification": "identification",
    "ci_floc_id": "floc_id",
    "ci_floc_class": "floc_class",
}

col_to_ignore = ["missing_host", "ci_uuid", "comment", "ci_type"]
col_to_hash = ["top_level", "dashboardName", "linked_environments"]
