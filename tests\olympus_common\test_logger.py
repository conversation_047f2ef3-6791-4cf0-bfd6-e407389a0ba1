import logging
from pathlib import Path

from olympus_common import defaults, logger
from olympus_common.config import LoggerConfig


def test_get_default_logger_debug(tmp_path: Path):
    """Test that the logger is created without a Filename when debug is set."""
    logger_config = LoggerConfig(logs_folder=tmp_path)
    default_logger = defaults.get_logger(True, logger_config, setup=True)

    logging.warning("Test log")
    assert not default_logger.filename


def test_get_default_logger_no_debug(tmp_path: Path):
    """Test that the logger is created with a TruncatingFileHandler when no debug is set.

    We also ensure that there is content in the the tmp_path / default_logger.filename file.
    """
    logger_config = LoggerConfig(logs_folder=tmp_path)
    default_logger = defaults.get_logger(False, logger_config, setup=True)

    logging.warning("Test log")
    assert default_logger.filename
    root_logger = logging.getLogger()
    assert any(isinstance(handler, logger.TruncatingFileHandler) for handler in root_logger.handlers)
    assert (tmp_path / default_logger.filename).read_text()  # Ensure there is content in the file.
