"""Configuration module for the sbom-tools."""

from dataclasses import dataclass
from pathlib import Path

from olympus_common.config import BaseServiceConfig, ExternalAPIConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(BaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for sbom-tools."""

    api_config: ExternalAPIConfig = dataclass_field(ExternalAPIConfig)
    application_name: str = env_field("APPLICATION_NAME", default="a2110-olympus")
    environment_name: str = env_field("ENVIRONMENT_NAME")
    release_name: str = env_field("RELEASE_NAME")
    sbom_output_path: Path = env_field("SBOM_OUTPUT_PATH", astype=Path, default="sbom.json")


config = Config()  # Create the singleton instance at import time.
