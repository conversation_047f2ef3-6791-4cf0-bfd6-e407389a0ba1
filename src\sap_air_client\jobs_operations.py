"""Helper functions for job operations in SAP AIR client."""

import logging
from http import HTT<PERSON>tatus
from typing import Optional

from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from olympus_common import enums, icinga
from olympus_common.db import Alarm, AlarmAction, AlarmRelease, Incident, UserAction, get_alarm_field
from olympus_common.elastic_apm import CaptureSpan, trace_scan
from olympus_common.enums import MeasureType, ReleaseStatus
from olympus_common.utils import now_naive
from sap_air_client.api_service.air import air
from sap_air_client.config import config
from sap_air_client.models import AlarmReleaseLink, SapDataToUpdate, SapEntityIncident
from sap_air_client.statics import EMPTY_AIR_DATA, EMPTY_SAP_INCIDENT_DATA, EMPTY_SAP_RELEASE_DATA
from sap_air_client.utils import (
    construct_service_name,
    create_and_link_release,
    update_backend_alarm_incident_link,
    update_icinga_sap_release,
)


@CaptureSpan(MeasureType.CUSTOM.value)
def process_release_link(
    session: Session,
    alarm_id: int,
    alarm: Alarm,
    release_data: dict,
    comment: Optional[str],
    external_release_id: Optional[str],
    user_id: Optional[str],
    user_comment: Optional[str],
    metric_type: Optional[str],
    metric_name: Optional[str],
) -> None:
    """Process the linking of a release to an alarm.

    This function handles:
    1. Creating and linking the release in the database
    2. Persisting the user action
    3. Updating the UI if needed
    4. Updating Icinga with the release data

    Args:
        session: The database session
        alarm_id: The alarm ID
        alarm: The alarm object
        release_data: The release data from SAP
        comment: The comment to add to the release
        external_release_id: The external release ID
        user_id: The user ID
        user_comment: The user comment
        metric_type: The metric type for Icinga
        metric_name: The metric name for Icinga
    """
    create_and_link_release(
        session=session,
        alarm_id=alarm_id,
        release_data=release_data,
        comment=comment,
        external_release_id=external_release_id,
        ci_id=alarm.ci_id,
        floc_id=get_alarm_field(alarm, "floc_id"),
        user_id=user_id,
    )

    persist_user_action(alarm_id, "link_comment", user_id, user_comment, session)

    if release_data["UserStatus"] == ReleaseStatus.COMPLETED.value:
        alarm.resend_to_ui(session)

    update_icinga_sap_release(
        host_name=get_alarm_field(alarm, "floc_id") or alarm.ci_id,
        metric_type=metric_type,
        metric_name=metric_name,
        release_data=release_data,
    )

    session.commit()


@trace_scan(MeasureType.CUSTOM.value)
def persist_user_action(
    alarm_id: int,
    action_type: str,
    user_id: str | None,
    user_comment: str | None,
    session: Session,
    is_first_ack: bool = False,
) -> None:
    """Persist the user action in the database.

    Notes
    -----
    The session is never committed in this function, it should be done in the caller's function.
    """
    user_action = UserAction(
        action_type=action_type,
        user_id=user_id,
        comment=user_comment,
    )
    user_action_record = user_action.insert_object(session)

    if user_action_record is None:
        response = "persist_user_action - Error while inserting user action in the database."
        logging.error(response)
        raise ValueError(response)

    if not alarm_id:
        return

    alarm_action = AlarmAction(alarm_id=alarm_id, action_id=user_action_record.id, is_first_ack=is_first_ack)
    alarm_action_record = alarm_action.insert_object(session)

    if alarm_action_record is None:
        response = "persist_user_action - Error while inserting alarm action in the database."
        logging.error(response)
        raise ValueError(response)


@CaptureSpan(MeasureType.CUSTOM.value)
def process_incident_creation(
    session: Session,
    alarm_id: int,
    alarm: Alarm,
    new_sap_entity: SapEntityIncident,
    entity: SapEntityIncident,
    client: icinga.IcingaClient,
) -> tuple[bool, Optional[JSONResponse], Optional[Incident]]:
    """Process the incident creation in database and UI.

    Args:
        session: The database session
        alarm_id: The alarm ID
        alarm: The alarm object
        new_sap_entity: The new SAP entity
        entity: The original entity
    """
    # Insert the incident in the database

    if not new_sap_entity.qmnum:
        error_msg = f"create_incident - Failed: Empty SAP incident ID on {new_sap_entity.qmnum}"
        logging.error(error_msg)
        return (
            False,
            JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg}),
            None,
        )

    incident = Incident(
        sap_id=new_sap_entity.qmnum,
        sap_status=new_sap_entity.stat_ord_usr_seq,
        user_id=entity.user_id,
    )
    incident_record = incident.insert_object(session)
    if not incident_record:
        error_msg = f"create_incident - Failed: While inserting incident in the database on {incident.sap_id}."
        logging.error(error_msg)
        return (
            False,
            JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg}),
            None,
        )

    incident_id = incident_record.id

    update_backend_alarm_incident_link(
        sap_incident_id=new_sap_entity.qmnum,
        incident_id=incident_id,
        alarm=alarm,
        is_origin_alarm=True,
        add_link=True,
        session=session,
        sap_status=incident_record.sap_status,
        user_id=entity.user_id,
    )

    persist_user_action(
        alarm_id, "create", entity.user_id, entity.user_comment, session, is_first_ack=entity.is_first_ack
    )

    # Update The front-end with icinga api
    data_to_update = new_sap_entity.data_to_update()
    data_to_update.tt_main_alarm = enums.SapMainAlarm.YES.value
    client.update(
        object_type=enums.IcingaObjectType.SERVICE.value,
        name=icinga.encode_string(
            construct_service_name(entity.floc_id or alarm.ci_id, entity.metric_type, entity.metric_name)
        ),
        attrs={"attrs": icinga.format_icinga_attrs(module_name="Sap", attrs=data_to_update.create())},
    )

    return True, None, incident_record


@CaptureSpan(MeasureType.CUSTOM.value)
async def run_air_procedure(
    session: Session,
    entity: SapEntityIncident,
    new_sap_entity: SapEntityIncident,
    incident_id: int,
) -> None:
    """Run AIR procedure for the incident.

    Args:
        session: The database session
        entity: The original entity
        new_sap_entity: The new SAP entity
        incident_id: The incident ID
    """
    air_entity = entity.to_air()
    air_entity.sap_incident_id = new_sap_entity.qmnum
    air_resp = await air.run_procedure(air_entity)

    air_resp["EventID"] = air_resp.pop("eventID", 0)
    air_resp["ExecutionID"] = air_resp.pop("executionID", 0)
    air_base_url = f"{config.air.url_con.split('/')[0]}//{config.air.url_con.split('/')[2]}"
    air_base_url = air_base_url.replace("-api", "")

    if air_resp["EventID"] != 0 and air_resp["ExecutionID"] != 0:
        air_resp["Link"] = f"{air_base_url}/events/{air_resp['EventID']}?executionID={air_resp['ExecutionID']}"
        air_resp["Status"] = "Created"
    else:
        air_resp["Status"] = "Failed"

    air.update_in_icinga(
        service_name=None,
        objet_type=enums.IcingaObjectType.SERVICE.value,
        floc_id=entity.floc_id,
        metric_type=entity.metric_type,
        metric_name=entity.metric_name,
        data=air_resp,
    )

    Incident.update_air_data(
        id=incident_id,
        execution_id=air_resp["ExecutionID"],
        instruction_id=air_resp["EventID"],
        air_status=air_resp["Status"],
        session=session,
    )


@CaptureSpan(MeasureType.CUSTOM.value)
def process_unlink_incident(
    entity: SapDataToUpdate, session: Session, alarm_id: int, alarm: Alarm, client: icinga.IcingaClient
) -> tuple[bool, Optional[JSONResponse]]:
    """Process the unlinking of an incident."""
    if not entity.qmnum:
        error_msg = f"unlink_incident - Failed: Empty SAP incident ID on {entity.qmnum}"
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg})

    update_backend_alarm_incident_link(
        sap_incident_id=entity.qmnum,
        incident_id=None,
        alarm=alarm,
        is_origin_alarm=False,
        add_link=False,
        session=session,
        user_id=entity.user_id,
    )
    # Persist the user action in the database
    persist_user_action(alarm_id, "unlink_comment", entity.user_id, entity.user_comment, session)
    # Update in Icinga to reset SAP and AIR data
    sap_dict = icinga.format_icinga_attrs(module_name="Sap", attrs=EMPTY_SAP_INCIDENT_DATA)
    air_dict = icinga.format_icinga_attrs(module_name="AIReactivity", attrs=EMPTY_AIR_DATA)
    client.update(
        object_type=enums.IcingaObjectType.SERVICE.value,
        name=icinga.encode_string(
            input_string=construct_service_name(
                host_name=entity.floc_id or alarm.ci_id,
                metric_type=entity.metric_type,
                metric_name=entity.metric_name,
            )
        ),
        attrs={"attrs": sap_dict | air_dict},
    )
    return True, None


def process_unlink_release(
    entity: AlarmReleaseLink,
    session: Session,
    alarm_id: int,
    alarm: Alarm,
    client: icinga.IcingaClient,
    alarm_release: AlarmRelease,
) -> None:
    """Process the unlinking of a release."""
    # Disable the alarm - release link
    alarm_release.unlink_user_id = entity.user_id
    alarm_release.unlink_time = now_naive()
    alarm_release.disable(session)

    # Persist the user action in the database
    persist_user_action(alarm_id, "unlink_comment", entity.user_id, entity.user_comment, session)

    # Re-send the alarm to the UI
    alarm.resend_to_ui(session)

    # update icinga
    sap_dict = icinga.format_icinga_attrs(module_name="Sap", attrs=EMPTY_SAP_RELEASE_DATA)
    client.update(
        object_type=enums.IcingaObjectType.SERVICE.value,
        name=icinga.encode_string(
            construct_service_name(
                get_alarm_field(alarm, "floc_id") or alarm.ci_id, entity.metric_type, entity.metric_name
            )
        ),
        attrs={"attrs": sap_dict},
    )
