"""Detail design implementation for mon-netact."""

import enum
import logging
import re
import string
from datetime import datetime, timedelta

import numpy as np
import pandas as pd
from sqlalchemy.orm import Session

from mon_netact import statics
from olympus_common import db, enums
from olympus_common import pd as olympus_pd
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(original: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Apply all transformations to the data and insert it.

    The logic here is outlined in netact-dd.
    """
    agent_id = db.Agent.get_agent_id_from_name("NetAct", session)
    df = original.reindex(columns=statics.COLUMNS)  # Ensure columns are present
    df = df.replace({pd.NA: "", np.nan: ""})  # Ensure all nan and NA values are empty strings
    df = _discard_alarms(df)
    if df.empty:
        return df  # _discard_alarms may have removed all rows, short circuit.
    transformed = _transform(df, agent_id)
    return transformed


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _discard_alarms(df: pd.DataFrame) -> pd.DataFrame:
    """Discard alarms.

    ACK_CHANGED alarms are dropped because they are sent right after a clear.
    This would cause the alarm to be re-raised.

    Unknown alarms are dropped because they do not contain any alarm-information.
    """
    df["_event_type"] = df.apply(_event_type, axis=1)
    return df.loc[~df["_event_type"].isin([EventType.UNKNOWN, EventType.ACK_CHANGED])]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(original_df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    logging.debug("Starting _transform.")

    df = olympus_pd.clean_raw_dataframe(original_df, drop_if_all_nan=statics.DROP_IF_ALL_NAN)

    # Required to be in the dataframe for other time values
    df["raise_time"] = df.apply(_raise_time, axis=1)

    # Hardcoded values
    df["agent_id"] = agent_id
    df["handle_time"] = olympus_utils.now_naive()
    df["manager"] = _manager()
    df["action_class"] = _scope()
    df["top_level"] = _top_level()

    # Applied values
    df["metric_type"] = df.apply(_metrics, axis=1)
    df["ci_id"] = df.apply(_ci_id, axis=1)
    df["node"] = df.apply(_node, axis=1)
    df["node_alias"] = df.apply(_node_alias, axis=1)
    df["metric_name"] = df.apply(_metric_name, axis=1)
    df["summary"] = df.apply(_summary, axis=1)
    df["severity"] = df.apply(_severity, axis=1)
    df["event_type"] = df.apply(_clear_level, axis=1)
    df["clear_type"] = df.apply(_clear_type, axis=1)
    df["clear_time"] = df.apply(_clear_time, axis=1)
    df["event_id"] = df.apply(_event_id, axis=1)
    df["wake_up_time"] = df.apply(_wake_up_time, axis=1)

    logging.debug("finished _transform")
    return df


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime:
    """Return the raise_time."""
    match _event_type(row):
        case EventType.NEW:
            date_str = row["SNMPv2-SMI::enterprises.28458.********.1.6"]
        case EventType.ACK_CHANGED:
            date_str = row["SNMPv2-SMI::enterprises.28458.********.1.2"]
        case EventType.CLEARED:
            date_str = row["SNMPv2-SMI::enterprises.28458.********.1.8"]
        case _:
            date_str = row["SNMPv2-SMI::enterprises.28458.1.26.2.1.6.3"]
    return _parse_dt(date_str)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _parse_dt(date_str: str) -> datetime:
    """Parse the date string.

    NetAct dates use an exotic format: '2025-1-10,0:57:17.0,+1:0'
    dateutil.parser does not (always) successfully parse, nor does datetime.strptime.
    So we manually parse these dates to a naive datetime object.

    Notes
    -----
    Once python3.12 is used, we could try to use the new %:z format specifier.
    An issue was logged at dateutil's github to address the incorrect parsing.

    References
    ----------
    https://github.com/dateutil/dateutil/issues/1409
    """
    d, t, tz = date_str.split(",")
    tzdelta = _convert_tz(tz)
    dt = datetime.strptime(f"{d} {t}", "%Y-%m-%d %H:%M:%S.%f") - tzdelta
    return dt


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _convert_tz(tzstring: str) -> timedelta:
    """Convert the timezone string to a timedelta object.

    Supported format is '+1:0' or '+1:0:0'. Negative variants are also supported.
    """
    tzsplit = tzstring.split(":")
    tzlist = list(map(float, tzsplit))
    tzseconds = tzlist[2] if len(tzlist) > 2 else 0
    tzdelta = timedelta(hours=tzlist[0], minutes=tzlist[1], seconds=tzseconds)
    return tzdelta


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the row's manager."""
    return "mon-netact"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the row's scope."""
    return enums.Scope.TE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Return the row's ci_id.

    We remove some strings from the node to get the ci_id.
    """
    if _is_heartbeat(row):
        return "NetAct_HB"

    if _is_alarm_summary(row):
        return "NetAct_AlarmSummary"

    ci_id = _node(row)
    split_strings = ["BTS", "PCM", "FUN", "LAPD", "BRM"]
    for split_string in split_strings:
        ci_id = ci_id.split(f"/{split_string}")[0]
    return ci_id


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Return the row's node."""
    if _is_heartbeat(row):
        return "NetAct_Heartbeat"

    if _is_alarm_summary(row):
        return "NetAct_AlarmSummary"

    node: str = row["SNMPv2-SMI::enterprises.28458.1.26.2.1.6.5"]
    node = node.removeprefix("PLMN-PLMN/")
    node = node.upper()
    return node


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str:
    """Return the row's node_alias."""
    return _node(row)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metrics(row: pd.Series) -> str:
    """Return the metrics."""
    if _is_heartbeat(row) or _is_alarm_summary(row):
        return "/ApplicationEvent/"
    return "/HardwareEvent/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Return the row's metric name.

    @MonitoredElementName is constructed as one out of the following cases :
    1. Default
    $FullSpecificProblem_[first | or : separated field of $NV_ADDITIONAL_TEXT]
    2. If $FullSpecificProblem = 7404, 7405, 7406, 10005, 10024
    $FullSpecificProblem_[first | or : separated field of $NV_ADDITIONAL_TEXT]
    3. If $FullSpecificProblem = 7600, 7601, 7602, 7604, 7606, 7607, 7608
    $FullSpecificProblem_$userAdditionalInformation
    4. If $FullSpecificProblem = 1078
    if [first space separated field of $additionalInformation] = "00BE"
    then $FullSpecificProblem_[first space separated field of $additionalInformation]
    else $FullSpecificProblem_[first | or : separated field of $NV_ADDITIONAL_TEXT]
    5. If $FullSpecificProblem = 20006
    $FullSpecificProblem_[first | : or ( separated field of $NV_ADDITIONAL_TEXT]

    @MonitoredElementName is harmonized as follows in the following order :
    1. All characters are converted into upper case.
    2. Trailing and leading white spaces are removed
    3. # sequences are removed
    4. Every sequence of white space characters is replaced by a single _ (underscore)
    5. SPEC_PROBLEM:_ is removed
    6. _IS_PERFORMED_DUE_TO is removed
    7. Keep only the characters before _NOT_ID
    8. Maximum length of @MonitoredElementName (cut off) : 80
    9. Trailing non-alphanumerics are removed

    """
    if _is_heartbeat(row):
        return "Heartbeat"

    if _is_alarm_summary(row):
        return "AlarmSummary"

    full_specific_problem = _full_specific_problem(row)
    additional_text = _additional_text(row)
    if not full_specific_problem or not additional_text:
        return ""
    nv_additional_text = _nv_additional_text(full_specific_problem, additional_text)
    metric_name = None

    if full_specific_problem.code in ["7600", "7601", "7602", "7604", "7606", "7607", "7608"]:
        metric_name = f"{full_specific_problem.code}_{additional_text.user_additional_information}"

    first_space_separated_field = additional_text.additional_information.split(" ")[0]
    if full_specific_problem.code == "1078" and first_space_separated_field == "00BE":
        metric_name = f"{full_specific_problem.code}_{first_space_separated_field}"  # 1078_00BE

    if full_specific_problem.code == "20006":
        pattern = r"^([^|:\(]*)"  # Match everything before the first |, : or (
        extracted_text = _extract_regex(pattern, nv_additional_text)
        metric_name = f"{full_specific_problem.code}_{extracted_text}"

    if not metric_name:
        pattern = r"^([^|:]*)"  # Match everything before the first | or :
        extracted_text = _extract_regex(pattern, nv_additional_text)
        metric_name = f"{full_specific_problem.code}_{extracted_text}"

    metric_name = metric_name.strip()
    metric_name = re.sub(r"#+", "", metric_name)
    metric_name = re.sub(r" +", "_", metric_name)
    metric_name = metric_name.upper()
    metric_name = metric_name.replace("SPEC_PROBLEM:_", "")
    metric_name = metric_name.replace("_IS_PERFORMED_DUE_TO", "")
    metric_name = metric_name.split("_NOT_ID")[0]
    metric_name = metric_name.rstrip(string.punctuation + string.whitespace)
    return metric_name[:80]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _extract_regex(pattern: str, text: str) -> str:
    """Extract the text using the pattern.

    If the pattern does not match, return the text as is.
    """
    pattern_compiled = re.compile(pattern)
    match = pattern_compiled.match(text)
    if match:
        return match.group(1)
    else:
        return text


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Return the row's summary.

    A phrase that summarizes the important alarm information: Name of the alarm and the proabable cause information
    (if the alarms delivers a code different than 0).
    The field format is:
    •	$FullSpecificProblem + $NV_ADDITIONAL_TEXT + " : " + $NV_PROBABLE_CAUSE

    Remove 'Not_ID: [0-9]+.*' from Summary.

    Add GPRS in front of Summary for-alarms having CI-ID FING-GGSN1, FING-GGSN2, FLEXINS-394404 or FLEXINS-394407
    """
    if _is_alarm_summary(row):
        event_time = row["SNMPv2-SMI::enterprises.28458.1.26.2.1.6.3"]
        sync_state = row["SNMPv2-SMI::enterprises.28458.1.26.2.1.6.2"]
        not_synced_nes = row["SNMPv2-SMI::enterprises.28458.1.26.2.1.6.4"]
        return f"{event_time=}, {sync_state=}, {not_synced_nes=}"

    full_specific_problem = _full_specific_problem(row)
    additional_text = _additional_text(row)
    if not full_specific_problem or not additional_text:
        return ""

    nv_additional_text = _nv_additional_text(full_specific_problem, additional_text)

    summary = f"{full_specific_problem.code} {nv_additional_text} "
    probable_cause = str(row["SNMPv2-SMI::enterprises.28458.********.1.14"])
    if probable_cause and probable_cause != "0" and probable_cause in statics.PROBABLE_CAUSE_MAP:
        probable_cause_text = statics.PROBABLE_CAUSE_MAP[probable_cause]
        summary += f" : {probable_cause_text}"

    summary = summary.split("Not_ID:")[0]

    ci_id = _ci_id(row)
    if ci_id in ["FING-GGSN1", "FING-GGSN2", "FLEXINS-394404", "FLEXINS-394407"]:
        summary = f"GPRS {summary}"
    return summary


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> None | datetime:
    """Return the row's clear_time."""
    if _clear_level(row) == enums.AlarmType.RESOLUTION.value:
        return row["raise_time"]
    return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level() -> str:
    """Return the row's top_level.

    Initially it is populated at probe level as A1392.
    It will be overwritten at enrichment, if data is present in the CMDB,
    with the concatenated list of applications that are potentially impacted by the alarm.
    """
    return "A1392"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Return the row's severity.

    For the SNMP nbiPerceivedSeverity  it is as follows :
    nbiPerceivedSeverity ::= TEXTUAL-CONVENTION
    STATUS   current
    DESCRIPTION
                "It represents the severity of the alarm."
    SYNTAX   INTEGER {
                    critical (1),
                    major (2),
                    minor (3),
                    warning (4),
                    clear (5),
                    indeterminate (6) }

    IF $FullSpecificProblem=3446
    THEN @Severity=5

    Problem alarms that have severity Indeterminate (@Severity=1) will be set as "Non Actionable"

    Note: The received value can be a string, integer or float. We convert it to an int then to a string for
    consistency.
    """
    if _event_type(row) is EventType.CLEARED:
        return enums.Severity.CLEARED.value

    perceived_severity = str(row["SNMPv2-SMI::enterprises.28458.********.1.13"])
    if not perceived_severity:
        return enums.Severity.INDETERMINATE.value

    perceived_severity = str(int(float(perceived_severity)))

    full_specific_problem = _full_specific_problem(row)
    if full_specific_problem and full_specific_problem.code == "3446":
        return enums.Severity.CRITICAL.value

    severity_map = {
        "1": enums.Severity.CRITICAL.value,
        "2": enums.Severity.MAJOR.value,
        "3": enums.Severity.MINOR.value,
        "4": enums.Severity.WARNING.value,
        "5": enums.Severity.CLEARED.value,
        "6": enums.Severity.INDETERMINATE.value,
    }
    return severity_map[perceived_severity]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime:
    delay = _delay(row)
    return _raise_time(row) + timedelta(0, delay)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_level(row: pd.Series) -> str:
    """Return the row's clear_level."""
    if _is_heartbeat(row):
        return enums.AlarmType.HEARTBEAT.value

    if _is_alarm_summary(row):
        if row["SNMPv2-SMI::enterprises.28458.1.26.2.1.6.2"] != "1":
            # Problem if the synchronisation state is not 1. 2 and 3 mean partial de-sync and full de-sync respectively.
            return enums.AlarmType.PROBLEM.value
        else:
            return enums.AlarmType.RESOLUTION.value

    if _severity(row) == enums.Severity.CLEARED.value:
        return enums.AlarmType.RESOLUTION.value
    else:
        return enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type(row: pd.Series) -> str:
    """Return the row's clear_type."""
    if _is_heartbeat(row):
        return enums.ClearType.MANUALLY.value
    else:
        return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_id(row: pd.Series) -> str:
    """Return the row's event_id."""
    return row["SNMPv2-SMI::enterprises.28458.********.1.5"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _delay(row: pd.Series) -> int:
    """Return the row's custom delay.

    The delay is looked up in statics.ACTIONABLE and defaults to 0.
    """
    if _is_heartbeat(row) or _is_alarm_summary(row):
        return 0

    metric_name = _metric_name(row)
    problem_code = metric_name.split("_")[0]
    try:
        return statics.ACTIONABLE_DELAYS[problem_code]
    except KeyError:
        return statics.ACTIONABLE_DELAYS.get(metric_name, 0)


class EventType(enum.Enum):
    """Represent the event type of the alarm."""

    UNKNOWN = "x0"
    NEW = "x1"
    ACK_CHANGED = "x2"
    CHANGED = "x3"
    COMMENT = "x4"
    CLEARED = "x5"
    HEARTBEAT = "Heartbeat"
    ALARM_SUMMARY = "AlarmSummary"


class AdditionalText:
    """Represent the additional text of the alarm."""

    def __init__(self, value: str):
        value_split = value.split("|")
        self.additional_information = value_split[0]
        self.user_additional_information = value_split[1]
        self.diagnostic_information = value_split[2]
        self.value = value


class FullSpecificProblem:
    """Represent the full specific problem of the alarm."""

    def __init__(self, value: str):
        value_split = value.split("|")
        self.code = value_split[0]
        self.description = value_split[1]
        self.full_problem = "_".join(value_split)

    def __str__(self):
        """Return the full problem."""
        return self.full_problem


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_type(row: pd.Series) -> EventType:
    """Return the row's event_type."""
    match row["SNMPv2-MIB::snmpTrapOID.0"]:
        case "SNMPv2-SMI::enterprises.28458.********.1.2":
            return EventType.HEARTBEAT
        case "SNMPv2-SMI::enterprises.28458.********.1.1":
            return EventType.NEW
        case "SNMPv2-SMI::enterprises.28458.********.1.2":
            return EventType.ACK_CHANGED
        case "SNMPv2-SMI::enterprises.28458.********.1.3":
            return EventType.CHANGED
        case "SNMPv2-SMI::enterprises.28458.********.1.4":
            return EventType.COMMENT
        case "SNMPv2-SMI::enterprises.28458.********.1.5":
            return EventType.CLEARED
        case "SNMPv2-SMI::enterprises.28458.********.2.2":
            return EventType.ALARM_SUMMARY
        case _:
            return EventType.UNKNOWN


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _is_heartbeat(row: pd.Series) -> bool:
    """Return whether or not the row is a heartbeat event."""
    return _event_type(row) is EventType.HEARTBEAT


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _is_alarm_summary(row: pd.Series) -> bool:
    """Return whether or not the row is an alarm_summary event."""
    return _event_type(row) is EventType.ALARM_SUMMARY


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_text(row: pd.Series) -> AdditionalText | None:
    """Return the additional text."""
    value = row["SNMPv2-SMI::enterprises.28458.********.1.4"]
    if not value:
        return None
    return AdditionalText(value)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _full_specific_problem(row: pd.Series) -> FullSpecificProblem | None:
    """Return the full specific problem."""
    value = row["SNMPv2-SMI::enterprises.28458.********.1.16"]
    if not value:
        return None
    return FullSpecificProblem(value)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _nv_additional_text(full_specific_problem: FullSpecificProblem, additional_text: AdditionalText) -> str:
    """Return the NV_ADDITIONAL_TEXT.

    This joins some of the additional text fields together.
    """
    nv_additional_text_list = [
        full_specific_problem.description,
        additional_text.additional_information,
        additional_text.user_additional_information,
        additional_text.diagnostic_information,
    ]
    return "|".join(nv_additional_text_list)
