"""Module to manage the data model for Core API."""

from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field


class AlarmInfoCriteria(BaseModel, populate_by_name=True):
    """Represent the Alarm info criteria.

    These criteria are the following, and they can all be combined:
    Location address (with a possibility to do a LIKE)
    A-code (i.e. top level includes the provided A-code)
    CI
    Agent name (UCMDB)
    Metric type
    Metric name
    Alarm ID
    Active only Y/N

    Notes
    -----
    `json_schema_extra` is used to provide metadata, on which our db module can rely:
    - operator: This is used to allow for "like" operators in SQL queries
    - models: This is used to determine to what model(s) the field belongs.
            If no model is specified, Alarm is assumed.
            If multiple models are specified, a coalesce operation is performed.
    """

    location_address: str | None = Field(None, json_schema_extra={"operator": "like", "models": ["Enrichment"]})
    top_level: str | None = Field(
        None, alias="a_code", json_schema_extra={"operator": "like", "models": ["Alarm", "Enrichment"]}
    )
    ci_id: str | list[str] | None = Field(None, json_schema_extra={"operator": "in"})
    name: str | list[str] | None = Field(
        None, alias="agent_name", json_schema_extra={"operator": "in", "models": ["Agent"]}
    )
    metric_type: str | list[str] | None = Field(None, json_schema_extra={"operator": "in"})
    metric_name: str | list[str] | None = Field(None, json_schema_extra={"operator": "in"})
    id: str | list[str] | None = Field(None, alias="alarm_id", json_schema_extra={"operator": "in"})
    options: dict[str, Any] | None = None


class AlarmInfoResponse(BaseModel):
    """Represent the alarm info's response.

    Alarm ID
    CI ID
    floc ID
    Agent name (UCMDB)
    Metric type
    Metric name
    Actionable
    Raise time
    Clear time
    Last occurrence time
    Severity
    Summary
    Linked SAP ID
    Location attribute
    Location address
    Location type
    Top level
    Is active
    """

    alarm_id: int
    ci_id: str | None
    floc_id: str | None
    agent_name: str | None
    metric_type: str | None
    metric_name: str | None
    actionable: bool | None
    raise_time: datetime | None
    clear_time: datetime | None
    last_occurrence_time: datetime | None
    severity: int | None
    summary: str | None
    linked_sap_id: str | None
    location_attribute: str | None
    location_address: str | None
    location_type: str | None
    top_level: str | None
    is_active: bool | None
