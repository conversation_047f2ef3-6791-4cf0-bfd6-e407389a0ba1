kind: Route
apiVersion: route.openshift.io/v1
metadata:
  labels:
    app: mon-big-data-#{appEnv}#
  name: a2110-mon-big-data-route-#{appEnv}#
  namespace: a2110-olympus-monitoring
  annotations:
    # When running using a http asgi/wsgi server, remember to update that timeout as well.
    haproxy.router.openshift.io/timeout: 60s
spec:
  host: a2110-mon-big-data-#{appEnv}#.#{openshiftEnv}#
  to:
    kind: Service
    name: mon-big-data-service-#{appEnv}#
    weight: 100
  port:
    targetPort: http
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
