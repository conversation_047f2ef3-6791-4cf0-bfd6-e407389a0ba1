# Olympus common
Common python modules for the olympus project.

## Application class
Olympus common provides an `Application` class. Client-apps can create an instance of this class and decorate their
detail-design method with `Application().run_forever`, this will run the decorated function in a while true loop and
executes default behaviour like getting data.
Sometimes a detail-design is ran as a cronjob; for this reason the `Application().run_once` decorator was created. This
will have the same behaviour as `Application().run_forever` without the while true loop.

`Application` has a `datareader` attribute. A `DataReader` should implement `read_data`, `success` and `error`.  
`read_data` will be called to read the data as the name implies. The `success` method will be called when
`Application()._run` completes the decorated function without errors. When an error is raised from the decorated
function, the method `error` will be called.  
The `DataReader` class is an abstract interface, so it is designed to be implemented/extended.  
Refer to `src/olympus_common/datareaders.py` to find the available concrete implementations.

`Application` has a `datawriter` attribute. A `DataWriter` should implement `success` and `error`.  
The `success` method will be called when `Application._run` completes the decorated function without errors. 
When an error is raised from the decorated function, the method `error` will be called.  
The `DataWriter` class is an abstract interface, so it is designed to be implemented/extended.  
Refer to `src/olympus_common/datawriters.py` to find the available concrete implementations.

## Examples
### Use `Application.run_forever` with your custom DD:
```python
# src/custom_dd/main.py
"""
Steps:
- Create an _init_app function which returns an Application instance.
- Call this function and store in the `app` variable
- Decorate your main function with app.run_forever
- Run your app using poetry `poetry run runservice custom_dd`
"""
import logging

from olympus_common.core import Application
from olympus_common.datareaders import DatabaseReader
from olympus_common.datawriters import DatabaseWriter
from olympus_common.logging import Logger

from custom_dd import dd
from custom_dd.config import config

def _init_app() -> Application:
    datareader = DatabaseReader()
    loglevel = logging.DEBUG if config.debug else logging.INFO
    if config.openshift:
        logger = Logger(folder=config.logs_folder, level=loglevel)
    else:
        logger = Logger(filename=None, level=loglevel)
    datawriter = DatabaseWriter("custom_agent", ignore_pkey_conflicts=True)
    return Application(datareader=datareader, datawriter=datawriter, logger=logger)


app = _init_app()


@app.run_forever(sleep_time=config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """This is your custom function."""
    # This function should only transform the raw data.
    # app.datawriter's and app.datareader's handlers will be called once this function returns.
    transformed = dd.run(data)
    return transformed

```

### Implement and use your own DataReader:
```python
# src/custom_dd/datareaders.py
from dataclasses import dataclass

from olympus_common.datareaders import DataReader


@dataclass
class MockedReader(DataReader):
    """Represent a MockedReader.

    This concrete DataReader returns static dummy data and prints on success and error.
    """

    filepath: Path

    def read_data(self) -> list[dict]:
        """Read data from `self.filepath`."""
        item = {"key1": "value1"}
        return [item]

    def success(self, data: list[dict]) -> None:
        """Print data on success."""
        print(self, "success", data)

    def error(self, data: list[dict], exc: Exception) -> None:
        """Print data on error."""
        print(self, "error", data, exc)
```
```python
# src/custom_dd/main.py
...
from custom_dd.datareaders import MockedReader


def _init_app() -> Application:
    # Use the new datareader as the Application's datareader
    datareader = MockedReader(filepath=Path.cwd() / "dummydata.json")
    ...
    return Application(datareader=datareader, datawriter=datawriter, config=appconfig, logger=logger)
...
```

### Implement and use your own DataWriter:
```python
# src/custom_dd/datawriters.py
from dataclasses import dataclass

from olympus_common.datawriters import DataReader


@dataclass
class MockedWriter(DataWriter):
    """Represent a MockedWriter.

    This concrete DataWriter prints on success and error.
    """

    def success(self, results: list[dict]) -> None:
        """Print data on success."""
        print(self, "success", results)

    def error(self, data: list[dict], exc: Exception) -> None:
        """Print data on error."""
        print(self, "error", data, exc)

```
```python
# src/custom_dd/main.py
...
from custom_dd.datawriter import MockedWriter


def _init_app() -> Application:
    # Use the new datawriter as the Application's datawriter
    datawriter = MockedWriter()
    ...
    return Application(datareader=datareader, datawriter=datawriter, config=appconfig, logger=logger)
...
```

### Create a frozen Singleton object as Config.
All fields which should be taken from the env must use `env_field` as field_definition.  
All fields which need calculation or are static should be decorated with `@property`, for simple value you can use 
direct assignment.
```python
# src/custom_dd/config.py
from dataclasses import dataclass
from pathlib import Path

from dotenv import load_dotenv
from olympus_common.dataclasses import dataclass_field, env_field
from olympus_common.db import DatabaseConfig
from olympus_common.utils import Singleton, strtobool

load_dotenv()


@dataclass(frozen=True)
class DDConfig(metaclass=Singleton):
    """Represent the configuration for the Custom DD."""

    sleep_time: int = env_field("SLEEP_TIME", astype=int, default="15")
    debug: bool = env_field("DEBUG", astype=strtobool)
    openshift: bool = env_field("OPENSHIFT", astype=strtobool)
    db: DatabaseConfig = dataclass_field(DatabaseConfig)
    snooze_delay: int = 120  # delay for setting an alarm to actionable (in seconds).
    
    @property
    def checkpoints_folder(self) -> Path:
        return Path("/data/checkpoints") if self.openshift else Path(__file__).parent / "checkpoints"

    @property
    def logs_folder(self) -> Path:
        return Path("/data/logs") if self.openshift else Path(__file__).parent / "logs"

    @property
    def columns_renaming(self) -> dict[str, str]:
        return {
            "status": "status",
            ...
        }


config = DDConfig()  # Initialize the singleton at import time.

```
```python
# src/custom_dd/main.py
...
from custom_dd.config import config


def _init_app() -> Application:
    # You can now use DDConfig anywhere either by executing `DDConfig()` or by importing `custom_dd.config`
    print(config.db.host)
    print(config.snooze_time)
    print(config.debug)
    ...
...
```

## Next up:
Olympus' Alarm model should be updated to not have any SnoozeAlarm table, but rather have a field in the Alarm table
which indicates whether or not the alarm is snoozed. This would simplify the insertion of the Alarms greatly.

Next steps with regards to the Alarm model should be discussed within the team. Having one table for unique alarms and
another table that documents their occurrences would avoid a lot of data duplication.
