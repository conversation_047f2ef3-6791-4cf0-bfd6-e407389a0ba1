"""Utils module for mon-local6."""

import pandas as pd


def is_airco_message(row: pd.Series) -> bool:
    """Return True if the condition defining the message is from AIRCO is met."""
    tag: str = row["tag"]
    return tag.startswith("A1574")


def is_dcms_message(row: pd.Series) -> bool:
    """Return True if the condition defining the message is from DCMS is met."""
    tag: str = row["tag"]
    return tag.startswith("A2103-") or tag.startswith("A2137-")


def is_ha_message(row: pd.Series) -> bool:
    """Return True if the provided message is a HA message."""
    return row["channel"] == "OPTIC_HA"


def is_mq_message(row: pd.Series) -> bool:
    """Return True if the condition defining the message is from WebSphere MQ is met."""
    return "-A926-" in row["tag"] or "-A1937-" in row["tag"]


def is_scada_message(row: pd.Series) -> bool:
    """Return True if the condition defining the message is from SCADA is met."""
    tag: str = row["tag"]
    return tag.startswith("A1724")


def is_ftp_message(row: pd.Series) -> bool:
    """Return True if the condition defining the message is from FTP is met."""
    tag: str = row["tag"]
    return tag.startswith("A1315")


def is_local6_message(row: pd.Series) -> bool:
    """Return True if none of the other supported conditions are met."""
    return not (
        is_airco_message(row)
        or is_dcms_message(row)
        or is_ha_message(row)
        or is_mq_message(row)
        or is_scada_message(row)
        or is_ftp_message(row)
    )


def is_mq_heartbeat(row: pd.Series) -> bool:
    """Return True if the condition for a WebSphere MQ heartbeat is met."""
    return is_mq_message(row) and row["metric"] in ["mqhb", "vernemqhb", "nginxhb"]


def is_dcms_heartbeat(row: pd.Series) -> bool:
    """Return True if the condition for a DCMS heartbeat is met."""
    return is_dcms_message(row) and row["metric"] == "Heartbeat"


def is_scada_heartbeat(row: pd.Series) -> bool:
    """Return True if the condition for a SCADA heartbeat is met."""
    return is_scada_message(row) and row["ci_id"] in ["BNS_HEARTBEAT_SCADA_OPTIC", "SCADA_HB"]


def is_ha_heartbeat(row: pd.Series) -> bool:
    """Return True if the provided message is a HA heartbeat."""
    if not is_ha_message(row):
        return False

    metric_name: str = row["metric"]

    if row["tag"] in ["arte2", "a1112", "a1250"] and metric_name.endswith("heartbeat"):
        return True

    if row["syslog_severity"] == "info" and metric_name.endswith("heartbeat"):
        # Not specified in the OPTIC DD. But these should be considered as heartbeats. Otherwise, we'll consider them
        # as clears, and create a great number of useless alarms.
        return True

    return False


def is_ftp_heartbeat(row: pd.Series) -> bool:
    """Return True if the condition for a FTP heartbeat is met."""
    return is_ftp_message(row) and row["metric"] == "Heartbeat"


def is_local6_heartbeat(row: pd.Series) -> bool:
    """Return True if the provided message is a HA heartbeat."""
    if not is_local6_message(row):
        return False

    if row["ci_id"] in ["mailgw_hb", "a1617-mail_gw-prod"]:
        return True
    elif startswith_endswith(row["tag"], "A1617-", "-SyslogHB"):
        return True
    elif startswith_endswith(row["tag"], "A1617-", "-SNMP_HB"):
        return True

    return False


def startswith_endswith(field: str, start: str, end: str) -> bool:
    """Check if the given field start and end with the given strings.

    Parameters
    ----------
    field : str
        The string that need to be checked.
    start : str
        The string that should be at the beginning of the tested field.
    end : str
        The string that should be at the end of the tested field.

    Returns
    -------
    bool
        True if both conditions are validated.
    """
    return field.startswith(start) and field.endswith(end)


def is_airco_message_vectorized(df: pd.DataFrame) -> pd.Series:
    """Return a boolean Series indicating which rows are AIRCO messages."""
    return df["tag"].str.startswith("A1574")


def is_dcms_message_vectorized(df: pd.DataFrame) -> pd.Series:
    """Return a boolean Series indicating which rows are DCMS messages."""
    return df["tag"].str.startswith("A2103-") | df["tag"].str.startswith("A2137-")


def is_ha_message_vectorized(df: pd.DataFrame) -> pd.Series:
    """Return a boolean Series indicating which rows are HA messages."""
    return df["channel"] == "OPTIC_HA"


def is_mq_message_vectorized(df: pd.DataFrame) -> pd.Series:
    """Return a boolean Series indicating which rows are WebSphere MQ messages."""
    return df["tag"].str.contains("-A926-") | df["tag"].str.contains("-A1937-")


def is_scada_message_vectorized(df: pd.DataFrame) -> pd.Series:
    """Return a boolean Series indicating which rows are SCADA messages."""
    return df["tag"].str.startswith("A1724")


def is_ftp_message_vectorized(df: pd.DataFrame) -> pd.Series:
    """Return a boolean Series indicating which rows are FTP messages."""
    return df["tag"].str.startswith("A1315")


def is_local6_message_vectorized(df: pd.DataFrame) -> pd.Series:
    """Return a boolean Series indicating which rows are local6 messages."""
    return ~(
        is_airco_message_vectorized(df)
        | is_dcms_message_vectorized(df)
        | is_ha_message_vectorized(df)
        | is_mq_message_vectorized(df)
        | is_scada_message_vectorized(df)
        | is_ftp_message_vectorized(df)
    )
