from unittest.mock import <PERSON>Mock

import pytest
from icinga2apic.exceptions import Icinga2ApiRequestException

from olympus_common.exceptions import IcingaMaxRetryError
from olympus_common.icinga import retry


def test_retry():
    """Test that the retry decorator raises IcingaMaxRetryException when using raise_on_max_retries=True."""

    @retry(raise_on_max_retries=True, max_retries=3, backoff_seconds=0.1)  # Configure to not take too long.
    def raise_exception():
        raise Icinga2ApiRequestException("Dummy", {})

    with pytest.raises(IcingaMaxRetryError):
        raise_exception()


def test_retry_no_raise():
    """Test that the retry decorator does not raise IcingaMaxRetryException when using raise_on_max_retries=False."""

    @retry(raise_on_max_retries=False, max_retries=3, backoff_seconds=0.1)  # Configure to not take too long.
    def raise_exception():
        raise Icinga2ApiRequestException("Dummy", {})

    raise_exception()  # This should not raise an exception since we use raise_on_max_retries=False.


def test_retry_unsupported_exception():
    """Test that the retry decorator lets an unexpected exception bubbles up.

    An unexpected exception is any exception other than Icinga2ApiRequestException.
    """

    @retry(raise_on_max_retries=False, max_retries=3, backoff_seconds=0.1)  # Configure to not take too long.
    def raise_exception():
        raise ValueError("Dummy unsupported exception")

    # This should raise an exception since the raised exception is not Icinga2ApiRequestException.
    with pytest.raises(ValueError, match="Dummy unsupported exception"):
        raise_exception()


def test_retry_respects_max_retries():
    """Test that the retry decorator respects the max_retries parameter."""

    retry_counter = MagicMock(name="retry_counter")

    @retry(raise_on_max_retries=False, max_retries=3, backoff_seconds=0.1)  # Configure to not take too long.
    def raise_exception():
        retry_counter()
        raise Icinga2ApiRequestException("Dummy", {})

    raise_exception()  # This should not raise an exception since we use raise_on_max_retries=False.
    assert retry_counter.call_count == 4  # We should have ran initially and retried 3 times, resulting in 4 calls.


def test_retry_accepts_str_or_iterable_as_unless():
    """Test that the retry decorator accepts `unless` as an iterable of strings or a single str."""
    retry_counter = MagicMock(name="retry_counter")

    @retry(unless="Dummy", raise_on_max_retries=False, max_retries=3, backoff_seconds=0.1)
    def raise_exception_unless_str():
        retry_counter()
        raise Icinga2ApiRequestException("Dummy", {})

    @retry(unless=["Dummy", "Foo", "Bar"], raise_on_max_retries=False, max_retries=3, backoff_seconds=0.1)
    def raise_exception_unless_iterable():
        retry_counter()
        raise Icinga2ApiRequestException("Foo", {})

    @retry(unless="Flummy", raise_on_max_retries=False, max_retries=3, backoff_seconds=0.1)
    def raise_exception_unless_no_match():
        retry_counter()
        raise Icinga2ApiRequestException("Foo", {})

    raise_exception_unless_str()
    assert retry_counter.call_count == 1
    retry_counter.reset_mock()
    raise_exception_unless_iterable()
    assert retry_counter.call_count == 1
    retry_counter.reset_mock()
    raise_exception_unless_no_match()
    assert retry_counter.call_count == 4
