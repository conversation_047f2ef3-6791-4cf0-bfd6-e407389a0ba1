"""Static module for the enrichment-client."""

from olympus_common.enums import Severity

ACTIONABLE_ALARM_RAISE_DICT = {
    "Indeterminate": 1,
    "Warning": 2,
    "Minor": 3,
    "Major": 4,
    "Critical": 5,
}

# Spectrum enrichment variables
SPECTRUM_SEVERITIES_NOT_ORANGE = {
    "00010b10": Severity.MAJOR.value,
    "00010f06": Severity.MAJOR.value,
    "00210cf1": Severity.MINOR.value,
    "00210cf4": Severity.MINOR.value,
    "011c004e": Severity.CRITICAL.value,
    "011c0488": Severity.MAJOR.value,
    "03b10001": Severity.CRITICAL.value,
    "00010009": Severity.MAJOR.value,
    "00210009": Severity.CRITICAL.value,
    "00210010": Severity.CRITICAL.value,
    "00220011": Severity.MAJOR.value,
    "fff00004": Severity.MAJOR.value,
    "fff00008": Severity.MAJOR.value,
    "fff00174": Severity.MAJOR.value,
    "fff0017a": Severity.MINOR.value,
    "fff0017e": Severity.MAJOR.value,
    "fff00184": Severity.MINOR.value,
    "fff00189": Severity.MINOR.value,
    "fff0018f": Severity.MINOR.value,
    "fff00192": Severity.MINOR.value,
    "fff00196": Severity.CRITICAL.value,
    "fff001b9": Severity.MINOR.value,
}

SPECTRUM_SEVERITIES_ORANGE = {
    "00010b10": Severity.CRITICAL.value,
    "00010f06": Severity.MAJOR.value,
    "00210cf1": Severity.MAJOR.value,
    "00210cf4": Severity.MAJOR.value,
    "011c004e": Severity.CRITICAL.value,
    "011c0488": Severity.CRITICAL.value,
    "03b10001": Severity.CRITICAL.value,
    "00010009": Severity.CRITICAL.value,
    "00210009": Severity.CRITICAL.value,
    "00210010": Severity.CRITICAL.value,
    "00220011": Severity.MAJOR.value,
    "fff00004": Severity.CRITICAL.value,
    "fff00008": Severity.MAJOR.value,
    "fff00174": Severity.CRITICAL.value,
    "fff0017a": Severity.MAJOR.value,
    "fff0017e": Severity.MAJOR.value,
    "fff00184": Severity.MAJOR.value,
    "fff00189": Severity.MAJOR.value,
    "fff0018f": Severity.MAJOR.value,
    "fff00192": Severity.CRITICAL.value,
    "fff00196": Severity.CRITICAL.value,
    "fff001b9": Severity.CRITICAL.value,
}
