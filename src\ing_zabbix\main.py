"""Entrypoint for the application."""

from ing_zabbix import ingest
from ing_zabbix.config import config
from ing_zabbix.datareaders import ZabbixReader
from ing_zabbix.datawriters import KafkaWriter
from olympus_common.core import Application
from olympus_common.datareaders import Checkpoint
from olympus_common.defaults import get_logger


def _init_application():
    checkpoint = Checkpoint(config.checkpoints_folder, config.checkpoint_filename)
    datareader = ZabbixReader(checkpoint=checkpoint)
    datawriter = KafkaWriter()
    logger = get_logger(config.debug, config.logger_config)
    return Application(datareader, datawriter, logger=logger)


application = _init_application()


@application.run_forever(sleep_time=config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Execute the main function for when the project is run.

    Parameters
    ----------
    data : list[dict]
        The data to transform. For this DD, this is supposed to be a single item list where the single item is a
        dictionary with keys "events" and "hosts".

    Returns
    -------
    list[dict]
        The transformed data.

    Notes
    -----
    Since app.run_forever sends a list[dict] to this function, the used reader should return a single item list with
    a dictionary that contains events and hosts in their respective keys.

    Examples
    --------
    Example input data. {...} represents a dictionary, the actual data is omitted for brevity:
    [{"events": [{...}, {...}, {...}], "hosts": [{...}, {...}, {...}]}]
    """
    events = data[0]["events"]
    hosts = data[0]["hosts"]
    return ingest.run(events, hosts)
