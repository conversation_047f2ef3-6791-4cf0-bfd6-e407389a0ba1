"""Detail design implementation for mon-adva."""

import json
from pathlib import Path

import pandas as pd
from sqlalchemy.orm import Session

from mon_adva import utils
from olympus_common import db
from olympus_common import pd as olympus_pd


def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the ADVA dd."""
    # oid data mapping
    data = json.loads(Path(Path(__file__).parent / "adva_oid_mapping.json").read_text())
    elastic_name = data["elastic_src_name"]
    agent_id = db.Agent.get_agent_id_from_name("ADVA", session)
    df_records = olympus_pd.clean_raw_dataframe(df, elastic_name, drop_if_all_nan=data["drop_if_all_nan"])
    df_records.dropna(inplace=True)
    return _transform(df_records, agent_id)


def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    df["agent_id"] = agent_id
    df["clear_type"] = utils.clear_type()
    df["manager"] = utils.manager()
    df["action_class"] = utils.scope()
    df["top_level"] = utils.top_level()
    df["handle_time"] = utils.handle_time()

    df["actionable"] = df.apply(
        lambda row: utils.actionable(row["summary"], int(row["severity"]), row["name"]),
        axis=1,
    )
    df["additional_data"] = df.apply(lambda row: utils.additional_data(int(row["neType"])), axis=1)
    df["ci_id"] = df.apply(lambda row: utils.ci_id(row["neName"], row["name"]), axis=1)
    df["event_id"] = df.apply(lambda row: utils.event_id(row["original_event_id"]), axis=1)
    df["node"] = df.apply(lambda row: utils.node(row["neName"]), axis=1)
    df["metric_type"] = df.apply(lambda row: utils.metric_type(row["name"]), axis=1)
    df["metric_name"] = df.apply(lambda row: utils.metric_name(row["neName"], row["name"]), axis=1)
    df["node_alias"] = df.apply(lambda row: utils.node_alias(row["nelpAdress"]), axis=1)
    df["raise_time"] = df.apply(lambda row: utils.raise_time(row["name"], row["nmsTime"], row["neTime"]), axis=1)
    df["summary"] = df.apply(lambda row: utils.summary(row["summary"]), axis=1)
    df["clear_time"] = df.apply(lambda row: utils.clear_time(int(row["event_type"]), row["raise_time"]), axis=1)
    df["severity"] = df.apply(lambda row: utils.severity(int(row["severity"]), int(row["event_type"])), axis=1)
    df["event_type"] = df.apply(lambda row: utils.event_type(int(row["event_type"]), row["name"]), axis=1)
    df["wake_up_time"] = df.apply(lambda row: utils.wake_up_time(row["raise_time"]), axis=1)

    return df
