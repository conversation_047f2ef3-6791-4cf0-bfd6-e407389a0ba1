apiVersion: apps/v1
kind: Deployment
metadata:
  name: filebrowser
  namespace: a1617-mail-monitoring-gateway
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: filebrowser
  template:
    metadata:
      labels:
        app: filebrowser
    spec:
      volumes:
        - name: vol-config
          persistentVolumeClaim:
            claimName: pvc-filebrowser-config
        - name: vol-certificates-data
          persistentVolumeClaim:
            claimName: a2110-mon-certificates-volume-claim-#{appEnv}#
      containers:
        - name: filebrowser
          image: artifactory.msnet.railb.be/docker/hurlenko/filebrowser
          args:
            [
              "--root=/data",
              "--address=0.0.0.0",
              "--database=/config/filebrowser.db",
              "--log=stdout",
            ]
          resources:
            requests:
              memory: "32Mi"
              cpu: "100m"
            limits:
              memory: "64Mi"
              cpu: "200m"
          ports:
            - containerPort: 8080
              protocol: TCP
          volumeMounts:
            - mountPath: "/data"
              name: vol-certificates-data
            - mountPath: /config
              name: vol-config
          livenessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 15
