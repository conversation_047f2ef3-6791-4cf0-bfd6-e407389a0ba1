"""Module containing all the shared code between certificates categories DDs."""

import json
import logging

import pandas as pd

from mon_certificates import static
from mon_certificates.config import config
from mon_certificates.enums import CertificateType, ExpirySeverities
from mon_certificates.utils.mailing import export_mail_text, render_html_cert_expiration, send_graph_email
from olympus_common.utils import read_file_content, str_to_set, write_in_file


def run_number_checkpoint_process() -> int:
    """Fetch, process and return the run number (checkpoint).

    Notes
    -----
    We add one directly to the run number, because this is used to count the number of days since the first run of the
    application.

    Returns
    -------
    int
        The number of run (including this one).
    """
    # Get the number of run (used as a checkpoint)
    if not (run_number_str := read_file_content(config.checkpoints_folder, config.run_number_file)):
        write_in_file(config.checkpoints_folder, config.run_number_file, "0", inplace=True)
        run_number_str = read_file_content(config.checkpoints_folder, config.run_number_file)
    # Add 1 to the number of run and write it into the checkpoint file.
    run_number = int(run_number_str) + 1
    run_number_str = str(run_number)
    write_in_file(config.checkpoints_folder, config.run_number_file, run_number_str, inplace=True)
    return run_number


def check_frequency(severity: ExpirySeverities, run_number: int) -> bool:
    """Check if the run number match the severity frequency."""
    return bool(run_number % static.SEVERITY_FREQUENCIES[severity] == 0 or run_number == 1)


def contacts_email_to_set(df: pd.DataFrame, columns: list[str]) -> set[str]:
    """Transform the contacts in the given column into a set.

    Parameters
    ----------
    df : pd.DataFrame
        The dataframe containing the certificates.
    columns : list[str]
        The name of the column containing the contacts.

    Returns
    -------
    set[str]
        A set containing the contacts.
    """
    emails = set()
    for column in columns:
        column_values = df[column].tolist()
        if column_values and isinstance(column_values[0], list):
            for value in column_values:
                emails.update(value)
        else:
            emails.update(column_values)
    return emails


def split_df_expiry_days(cert_type: CertificateType, df: pd.DataFrame, severity: ExpirySeverities) -> pd.DataFrame:
    """Return the dataframe depending on the defined severity.

    Info
    ----
    Currently there are different needs depending on the certificate type:
     * 4 different severities for NACADCA: info, warning, critical and emergency.
     * 3 different severities for TALOS: warning, critical and emergency.
    """
    match severity:
        case ExpirySeverities.INFO.value:
            df_sev = df[(df["expiry_days"] <= static.INFORMATIONAL_LIMIT) & (df["expiry_days"] > static.WARNING_LIMIT)]
        case ExpirySeverities.WARNING.value:
            df_sev = df[(df["expiry_days"] <= static.WARNING_LIMIT) & (df["expiry_days"] > static.CRITICAL_LIMIT)]
        case ExpirySeverities.CRITICAL.value:
            df_sev = df[(df["expiry_days"] <= static.CRITICAL_LIMIT) & (df["expiry_days"] > static.EMERGENCY_LIMIT)]
        case ExpirySeverities.EMERGENCY.value:
            df_sev = df[(df["expiry_days"] <= static.EMERGENCY_LIMIT) & (df["expiry_days"] >= static.EXPIRED_LIMIT)]

    logging.debug(f"Number of {cert_type.value} certificates in severity {severity.value} = {df_sev.shape[0]}")

    return df_sev


def prepare_mailing(
    cert_type: CertificateType, severity: ExpirySeverities, group: str, df: pd.DataFrame
) -> tuple[pd.DataFrame, set[str], set[str]]:
    """Return the filtered dataframe, the recipients and the cc_recipients for the given parameters.

    Parameters
    ----------
    cert_type : CertificateType
        The type of certificates (refer to the issuer).
    severity : ExpirySeverities
        The severity depending on the number of expiry days left.
    group : str
        The name of the group of certificate (for expl: the A-code or the organizational unit).
    df : pd.DataFrame
        The filtered Dataframe.

    Returns
    -------
    tuple[pd.DataFrame, set[str], set[str]]
        The tuple containing the filtered dataframe, the recipients and the recipients in copy.

    Raises
    ------
    ValueError
        Raise if the certificate type is not part of the existing one.
    """
    sev = severity.value
    match cert_type:
        case CertificateType.NACADCA.value:
            group_column_name = "organizational_unit"
            recipients_dict = static.NACADCA_RECIPIENTS_COLUMNS
            cc_recipients_dict = static.NACADCA_CC_RECIPIENTS_COLUMNS
            if sev == ExpirySeverities.INFO.value:
                sev = ExpirySeverities.WARNING.value
        case CertificateType.TALOS.value:
            group_column_name = "application_code"
            recipients_dict = static.TALOS_RECIPIENTS_COLUMNS
            cc_recipients_dict = static.TALOS_CC_RECIPIENTS_COLUMNS
        case _:
            raise ValueError(
                "The given type of the certificate is not one of the following: "
                f"{list(CertificateType.__members__.keys())}"
            )

    df_severity = split_df_expiry_days(cert_type, df, severity)
    df_email: pd.DataFrame = df_severity[df_severity[group_column_name] == group]
    recipients = contacts_email_to_set(df_email, recipients_dict[sev])
    cc_recipients = contacts_email_to_set(df_email, cc_recipients_dict[sev])
    cc_recipients.update(config.default_cc_receivers)

    return df_email, recipients, cc_recipients


def email_for_certificates(
    group_name: str,
    cert_type: CertificateType,
    recipients: set,
    cc_recipients: set,
    subject: str,
    df: pd.DataFrame,
    days_left: int,
    df_2: pd.DataFrame | None = None,
    days_left_2: int | None = None,
) -> None:
    """Create the body of the mail and send the mail.

    If df_2 and days_left_2 are filled, those will be used to create a merged e-mail content.

    Parameters
    ----------
    group_name : str
        The group used to slice the certificates (currently app_code or org_unit).
    cert_type : CertificateType
        The type of the given certificates.
    recipients : set
        List of the person that will receive the e-mail.
    cc_recipients : set
        List of the person that will receive the e-mail (being in CC).
    subject : str
        Subject of the email.
    df : pd.DataFrame
        pandas DataFrame which contains the certificates data.
    days_left : int
        Number of days for the severity.
    df_2 : pd.DataFrame | None, optional
        DataFrame which contains the certificates data of a second group, by default None
    days_left_2 : int | None, optional
        Number of days for the "warning" severity, by default None

    Raises
    ------
    ValueError
        If the df_2 is not None and days_left_2 is None.
    """
    if group_name == "N/A":
        return

    if df_2 is not None and days_left_2 is None:
        raise ValueError("As df_2 is not None, the function did not expect days_left_2 to be None.")

    # Get the templates name
    one_df_template, two_df_template = _define_templates(cert_type)

    if df_2 is not None and not df_2.empty and days_left_2 is not None:
        cache_file_2 = f"{group_name}_{days_left_2}.txt"
        old_cert_2 = str_to_set(str(read_file_content(config.checkpoints_folder, cache_file_2)))
        cert_list_2 = df_2["common_name"].values.tolist()
        df_2 = df_2[~df_2["common_name"].isin(old_cert_2)]
    else:
        old_cert_2 = set()  # type: ignore[assignment]
        cert_list_2 = set()  # type: ignore[assignment]

    if not df.empty:
        cache_file = f"{group_name}_{days_left}.txt"
        old_cert = str_to_set(str(read_file_content(config.checkpoints_folder, cache_file)))
        cert_list = df["common_name"].values.tolist()
        df = df[~df["common_name"].isin(old_cert)]
    else:
        old_cert = set()
        cert_list = set()  # type: ignore[assignment]

    if df_2 is not None and not df_2.empty and not df.empty:
        certificates_list = df.to_dict("records")
        certificates_list_2 = df_2.to_dict("records")
        mail_text = render_html_cert_expiration(
            two_df_template, days_left, certificates_list, days_left_2, certificates_list_2
        )
        send_graph_email(recipients, mail_text, subject, config.email_address, cc_recipients)
        if config.debug:
            export_mail_text(mail_text, group_name, days_left, certificates_list_2, days_left_2)

        certificate_checkpoint_set = _create_checkpoints_set(old_cert, cert_list)  # type: ignore[arg-type]
        _write_checkpoint(cache_file, certificate_checkpoint_set)
        certificate_checkpoint_set2 = _create_checkpoints_set(old_cert_2, cert_list_2)  # type: ignore[arg-type]
        _write_checkpoint(cache_file_2, certificate_checkpoint_set2)
    elif df_2 is not None and not df_2.empty and df.empty:
        if days_left_2 is None:
            raise ValueError("Did not expect warning_days_left to be None.")
        certificates_list_2 = df_2.to_dict("records")
        mail_text = render_html_cert_expiration(one_df_template, days_left_2, certificates_list_2)
        send_graph_email(recipients, mail_text, subject, config.email_address, cc_recipients)
        if config.debug:
            export_mail_text(mail_text, group_name, days_left, certificates_list_2, days_left_2)
        certificate_checkpoint_set2 = _create_checkpoints_set(old_cert_2, cert_list_2)  # type: ignore[arg-type]
        _write_checkpoint(cache_file_2, certificate_checkpoint_set2)
    elif not df.empty:
        certificates_list = df.to_dict("records")
        template = one_df_template
        mail_text = render_html_cert_expiration(template, days_left, certificates_list)
        send_graph_email(recipients, mail_text, subject, config.email_address, cc_recipients)
        if config.debug:
            export_mail_text(mail_text, group_name, days_left)
        certificate_checkpoint_set = _create_checkpoints_set(old_cert, cert_list)  # type: ignore[arg-type]
        _write_checkpoint(cache_file, certificate_checkpoint_set)


def _define_templates(cert_type: CertificateType) -> tuple[str, str]:
    """Return the file names corresponding to the templates to use [one_df_template, two_df_template]."""
    match cert_type:
        case CertificateType.NACADCA.value:
            one_df_template = "nacadca-mail_two_df.html.j2"
            two_df_template = "nacadca-mail.html.j2"
        case CertificateType.TALOS.value:
            one_df_template = "talos-mail_two_df.html.j2"
            two_df_template = "talos-mail.html.j2"
    return one_df_template, two_df_template


def _create_checkpoints_set(checkpoint_certificates: set, current_certificates: list) -> set:
    """Return the set of certificates to put inside the checkpoint file.

    Parameters
    ----------
    checkpoint_certificates : set
        The set of certificates that already are in the checkpoint file.
    current_certificates : list
        The list of certificates from the dataframe.

    Returns
    -------
    set
        The set of certificates to put inside the checkpoint file.
    """
    certificates_checkpoint_set = {cert for cert in checkpoint_certificates if cert in current_certificates}
    certificates_to_add = {cert for cert in current_certificates if cert not in checkpoint_certificates}
    certificates_checkpoint_set.update(certificates_to_add)
    return certificates_checkpoint_set


def _write_checkpoint(checkpoint_file: str, checkpoint_set: set) -> None:
    """Write the checkpoints in the correct folder for the given filename."""
    write_in_file(config.checkpoints_folder, checkpoint_file, json.dumps(list(checkpoint_set)), inplace=True)
