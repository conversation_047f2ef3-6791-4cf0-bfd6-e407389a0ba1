"""Tests for the config module."""

from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from olympus_common.config import DatabaseConfig


def test_database_config(mocker: MockerFixture):
    """Test that DatabaseConfig.to_conninfo returns the expected info."""
    expected_info = "postgresql+psycopg://dummyuser:dummypassword@dummyhost:5432/dummyname"
    assert DatabaseConfig().to_conninfo() == expected_info
