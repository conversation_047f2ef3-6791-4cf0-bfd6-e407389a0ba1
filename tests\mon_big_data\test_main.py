"""Tests for the `main` module."""

from typing import Any

from fastapi.testclient import TestClient
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from tests.olympus_common.mocked_db import MockedDBModule
from tests.utils import remove_middleware


def test_main(mocker: Mo<PERSON>Fixture):
    """Placeholder test."""
    from mon_big_data.main import app

    remove_middleware(app, "AuthMiddleware")

    mocked_database_writer = mocker.patch("mon_big_data.main.DatabaseWriter")
    mocker.patch("mon_big_data.dd.db", new=MockedDBModule())

    client = TestClient(app)
    url = "http://127.0.0.1:8000/webhook"
    data: Any = {
        "receiver": "olympus-acc",
        "status": "firing",
        "alerts": [
            {
                "status": "firing",
                "labels": {
                    "acode": "A2066",
                    "alertname": "prometheus_healthcheck",
                    "category": "healthcheck",
                    "ci": "iictmismlv013",
                    "env": "prod",
                    "instance": "iictmismlv013.msnet.railb.be",
                    "job": "prometheus",
                    "metricname": "prometheus_healthcheck",
                    "metrictype": "/Application/",
                    "node": "iictmismlv013.msnet.railb.be",
                    "optic": "enabled",
                    "server": "iictmismlv013",
                    "severity": "info",
                    "source": "prometheus",
                },
                "annotations": {"summary": "Healthcheck Instance iictmismlv013.msnet.railb.be is ok"},
                "startsAt": "2023-11-27T12:49:33.674Z",
                "endsAt": "0001-01-01T00:00:00Z",
                "generatorURL": "https://iictmismlv013.msnet.railb.be/prometheus/graph?",
                "fingerprint": "0eedc3cd100922a7",
            },
        ],
        "groupLabels": {"instance": "iictmismlv013.msnet.railb.be"},
        "commonLabels": {
            "acode": "A2066",
            "alertname": "prometheus_healthcheck",
            "category": "healthcheck",
            "ci": "iictmismlv013",
            "env": "prod",
            "instance": "iictmismlv013.msnet.railb.be",
            "job": "prometheus",
            "metricname": "prometheus_healthcheck",
            "metrictype": "/Application/",
            "node": "iictmismlv013.msnet.railb.be",
            "optic": "enabled",
            "server": "iictmismlv013",
            "severity": "info",
            "source": "prometheus",
        },
        "commonAnnotations": {"summary": "Healthcheck Instance iictmismlv013.msnet.railb.be is ok"},
        "externalURL": "https://iictmismlv013.msnet.railb.be/alertmanager",
        "version": "4",
        "groupKey": '{}/{env="prod",optic="enabled"}:{instance="iictmismlv013.msnet.railb.be"}',
        "truncatedAlerts": 0,
    }

    response = client.post(url, json=data)
    assert response.status_code == 200
    assert mocked_database_writer.return_value.success.called
