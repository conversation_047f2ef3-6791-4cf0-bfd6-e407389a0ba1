"""Utils module for mon-sap-solman."""


def substring_before_second(test_string: str, delimiter: str) -> str:
    """Return the substring before the second delimiter if possible, else the given string."""
    parts = test_string.split(delimiter)
    if len(parts) >= 3:
        return "_".join(parts[:2])
    else:
        return test_string


def substring_after_second(test_string: str, delimiter: str) -> str:
    """Return the substring after the second delimiter if possible, else the given string."""
    parts = test_string.split(delimiter)
    if len(parts) >= 3:
        return "_".join(parts[2:])
    else:
        return test_string
