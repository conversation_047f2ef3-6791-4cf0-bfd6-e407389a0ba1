"""Generic dataclasses module.

Provides the required objects and functionality to initialize dataclasses from env and have nested dataclasses.

Examples
--------
```python
import json
from dataclasses import dataclass

from olympus_common.config import env_field, dataclass_field
from olympus_common.db import DatabaseConfig
from olympus_common.utils import Singleton


@dataclass(frozen=True)  # Built-in functionality
class MyConfig(metaclass=Singleton):  # Single<PERSON> ensures only one instance of this class can ever exist
    hostname: str = env_field("HOSTNAME")  # Will be taken from the HOSTNAME env var
    port: int = env_field("PORT", astype=int, default="5000")  # Casted to int, if not in env "5000" will be casted
    debug: bool = env_field("DEBUG", astype=strtobool)  # Can pass other callables that accept a str as argument
    index_list: list[str] = env_field("INDEX_LIST", astype=json.loads)
    db: DatabaseConfig = dataclass_field(DatabaseConfig)  # Will receive a mypy error if the class is not a dataclass

# Ensure we initialize the config from the env and with the defaults at import time.
# Users can now use this or initialize a new instance without arguments (it will be the same object that's returned)
config = MyConfig()
```
"""

import os
from dataclasses import Field, field
from functools import partial
from typing import Any, Callable, ClassVar, Protocol, TypeVar, runtime_checkable

from olympus_common.exceptions import ConfigurationError, MissingKeyError


@runtime_checkable
class DataclassInstance(Protocol):
    """Represent a protocol that types an object as a dataclass.

    Notes
    -----
    The runtime_checkable decorator enables this protocol to be used in isinstance checks.

    References
    ----------
    typeshed lib for python stdlib defines this protocol:
    https://github.com/python/typeshed/blob/main/stdlib/_typeshed/__init__.pyi#L313
    """

    __dataclass_fields__: ClassVar[dict[str, Field[Any]]]


TDataclass = TypeVar("TDataclass", bound=DataclassInstance)
T = TypeVar("T")


def env_field(key: str, astype: Callable[[str], T] | None = None, default: str | None = None, **kwargs) -> T:
    """Return a field with `from_env` as a default_factory.

    The provided `key`, `astype` and `default` are used as arguments to `from_env`.
    The default `astype` to `from_env` is `str`.
    All other `kwargs` are sent directly to `dataclasses.field`.
    `dataclasses.field.default` and `dataclasses.field.default_factory` can not be set manually by using this function.
    """
    astype_ = astype or str
    from_env_partial = partial(_from_env, key, astype_, default=default)
    kwds = {**kwargs, "default_factory": from_env_partial}
    return field(**kwds)


def dataclass_field(astype: type[TDataclass]) -> TDataclass:
    """Return a field with the provided `astype` as a default_factory.

    This function is used to type the use of a nested dataclass that can be initialized without any arguments.
    Static type checkers will also verify that `astype` is a Dataclass.
    """
    return field(default_factory=astype)


def _from_env(key: str, astype: Callable[[str], T], default: str | None = None) -> T:
    """Return the given key's value (converted to astype) from the os.environ.

    Parameters
    ----------
    key : str
        The key to extract from the environment.
    astype : Callable[[str], T]
        The function to cast the extracted value to.
    default : str | None, optional
        The default value to use if the key is not found in the environment, by default None

    Returns
    -------
    T
        The value extracted from the env and casted to the provided astype.

    Notes
    -----
    A limitation by using this function is that it can not use None as a default as the default should be a replacement
    for the variable extracted from the env, which is always a str.

    Raises
    ------
    MissingKeyException
        Whenever the `key` was not found in `os.environ`
    ConfigurationError
        Whenever the retrieved value could not be casted to `astype`
    """
    try:
        value = os.environ[key]
    except KeyError as exc:
        if default is None:
            raise MissingKeyError(exc) from exc
        value = default

    try:
        return astype(value)
    except Exception as exc:
        raise ConfigurationError(f"Calling {astype} for {key=} with {value=} failed. {exc=}") from exc
