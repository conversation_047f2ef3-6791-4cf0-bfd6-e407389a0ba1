"""Configuration module for the archiving-job."""

from dataclasses import dataclass

from olympus_common.config import BaseServiceConfig, DatabaseConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(BaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for the optic-matcher module."""

    db_config: DatabaseConfig = dataclass_field(DatabaseConfig)
    nb_weeks_threshold: int = env_field("ARCHIVING_WEEKS", astype=int, default="1")


config = Config()  # Create the singleton instance at import time.
