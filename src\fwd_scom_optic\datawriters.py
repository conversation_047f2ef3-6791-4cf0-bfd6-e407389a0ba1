"""Module to provide DataWriters specific to fwd-scom-optic."""

from dataclasses import dataclass

from fwd_scom_optic import logger
from olympus_common.datawriters import DataWriter


@dataclass
class SyslogWriter(DataWriter):
    """Represent a SyslogWriter that forwards the results to the configured syslog."""

    def success(self, results: list[dict]) -> None:
        """Forward the results to the syslogger."""
        for result in results:
            logger.log_alarm(**result)

    def error(self, _: list[dict], __: Exception) -> None:
        """Do nothing on error."""
