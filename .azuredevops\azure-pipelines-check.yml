# Pipeline for each pull request change in A2110-Olympus.
# This pipeline uses a local agent on the NON-PROD environment.
# This pipeline should be configured in the GUI as a pipeline and a build validation rule for the `main` branch.

pool:
  name: iict315-openshift4
  demands:
    - ENV -equals NON-PROD
    - POETRY_VERSION -equals 1.8.2

trigger: none

variables:
  - name: POETRY_CACHE_DIR
    value: /tmp/.cache
  - name: PYTHONWARNINGS
    value: once
  - name: PIP_INDEX_URL
    value: "http://artifactory.msnet.railb.be:8081/artifactory/api/pypi/pypi-remote/simple"
  - name: PIP_TRUSTED_HOST
    value: artifactory.msnet.railb.be

stages:
  - stage: Lint
    dependsOn: []
    jobs:
      - job: Lint1
        steps:
          - script: poetry install
            displayName: "Install the project and its dependencies"
          - script: poetry run poe lint
            displayName: "Lint source code"

  - stage: Test
    dependsOn: []
    jobs:
      - job: Test1
        steps:
          - script: poetry install
            displayName: "Install the project and its dependencies"
          - script: poetry run poe test
            displayName: "Test source code"
