kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-mon-datalines-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/a2110_olympus/run.py
  DEBUG: "0"
  LOGS_FOLDER: "/data/logs"
  KAFKA_TOPICS: >-
    #{kafkaTopics}#
  KAFKA_BOOTSTRAP_SERVERS: >-
    #{kafkaBootstrapServers}#
  KAFKA_ENVIRONMENT: >-
    #{kafkaEnvironment}#
  KAFKA_CLIENT_ID_SUFFIX: "mon-datalines"
  DB_SCHEMA: "#{databaseSchema}#"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  LOGS_MAX_SIZE: "490"
  KAFKA_MAX_POLL_RECORDS: "#{kafkaMaxPollRecords}#"
  KAFKA_MAX_POLL_INTERVAL_MS: "#{kafkaMaxPollInterval}#"
  ELASTIC_APM_SERVER_URL: "#{elasticApmServerUrl}#"
  ELASTIC_APM_SERVICE_NAME: "#{olympusServiceName}#"
  ELASTIC_APM_VERIFY_SERVER_CERT: "#{elasticApmVerifyServerCert}#"
  ENABLE_ELASTIC_APM: "#{enableElasticApm}#"
  ELASTIC_APM_ENVIRONMENT: "#{elasticApmEnvironment}#"
  ELASTIC_APM_LOG_LEVEL: "#{elasticApmLogLevel}#"
