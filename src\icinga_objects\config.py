"""Configuration module for icinga-objects."""

from dataclasses import dataclass

from olympus_common.config import BaseServiceConfig, CMDBConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import Singleton, strtobool


@dataclass(frozen=True)
class Config(BaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for Icinga Objects."""

    cmdb_config: CMDBConfig = dataclass_field(CMDBConfig)
    icinga_api_endpoint: str = env_field("ICINGA_API_ENDPOINT", astype=str)
    icinga_user: str = env_field("ICINGA_USER", astype=str)
    icinga_password: str = env_field("ICINGA_PASSWORD", astype=str)
    thread_number: int = env_field("THREAD_NUMBER", astype=int, default="32")
    purge_icinga_base: bool = env_field("PURGE_ICINGA_BASE", astype=strtobool, default="0")
    process_check_missing: bool = env_field("PROCESS_CHECK_MISSING", astype=strtobool, default="0")
    can_delete_host: bool = env_field("CAN_DELETE_HOST", astype=strtobool, default="0")
    run_cis_job: bool = env_field("RUN_CIS_JOB", astype=strtobool, default="1")
    run_metrics_job: bool = env_field("RUN_METRICS_JOB", astype=strtobool, default="0")
    run_dependencies_job: bool = env_field("RUN_DEPENDENCIES_JOB", astype=strtobool, default="0")


config = Config()
