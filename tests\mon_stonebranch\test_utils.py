from mon_stonebranch.utils import convert_hex_to_ip, get_hex_ip_from_message


def test_convert_hex_to_ip() -> None:
    """Test the convert_hex_to_ip function."""
    hex_input: str = "\n\xfeJ5"
    result_ip = convert_hex_to_ip(hex_input)
    expected_ip = "************"
    assert result_ip == expected_ip


def test_get_hex_ip_from_message() -> None:
    """Test the get_hex_ip_from_message function."""
    message_trap = (
        "#<SNMP::SNMPv1_Trap:0xbf4c51a "
        "@enterprise=[*******.4.1.88898.*******.301], "
        "@timestamp=#<SNMP::TimeTicks:0x3e9bafcd @value=2422773944>, "
        "@varbind_list=[#<SNMP::VarBind:0xec98022 @name=[*******.4.1.88898.*******.300.1.1], "
        '@value="1">, '
        "#<SNMP::VarBind:0x23b848f4 @name=[*******.4.1.88898.*******.300.1.2], "
        '@value="JTU129D001">, '
        "#<SNMP::VarBind:0x403e7e89 @name=[*******.4.1.88898.*******.300.1.3], "
        '@value="Remote File Monitor">, '
        "#<SNMP::VarBind:0x1f7db5af @name=[*******.4.1.88898.*******.300.1.4], "
        '@value="1718787417106551430SCB4F6NQS33QU">, '
        "#<SNMP::VarBind:0x5df7eef2 @name=[*******.4.1.88898.*******.300.1.5], "
        '@value="FINISHED">, '
        "#<SNMP::VarBind:0x3c69f250 @name=[*******.4.1.88898.*******.300.1.6], "
        '@value="IICTCIAPWV405">], '
        "@specific_trap=1, "
        '@source_ip="**************", '
        '@agent_addr=#<SNMP::IpAddress:0xf900962 @value="\\n\\xFEJ5">, '
        "@generic_trap=6>"
    )

    retrieve_hex_message = get_hex_ip_from_message(message_trap)
    expected_hex = "\n\xfeJ5"

    assert retrieve_hex_message == expected_hex
