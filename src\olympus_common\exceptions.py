"""Common exceptions which can be used by an application."""


class OlympusError(Exception):
    """Base exception for the Olympus project."""


class ConfigurationError(OlympusError):
    """Raised whenever the application could not parse the configuration.

    Notes
    -----
    This exception is the base exception for configuration issues.
    """


class MissingKeyError(OlympusError):
    """Raised whenever the application could not find a specific key in the environment."""

    base_str = "Please add {e} as an environment variable (maybe in your .env?)"

    def __str__(self) -> str:
        """Return the base_str formatted with the first argument passed to this exception.

        Notes
        -----
        If no arguments were passed, the parent class' __str__ is called.
        If multiple arguments were passed, only the first one is used and the others are ignored.
        """
        if not self.args:
            return super().__str__()
        return self.base_str.format(e=self.args[0])


class DatabaseUsageError(OlympusError):
    """Raised whenever the db module is used in a wrong fashion."""


class IcingaAPIError(OlympusError):
    """Raised whenever the Icinga API connector receives an unexpected status code."""


class IcingaMaxRetryError(OlympusError):
    """Raised whenever `retry` on Icinga API calls exhausted the max_retries."""


class SapAirError(OlympusError):
    """Raised whenever the sap_air_client module receives an unexpected status code from SAP OR AIR."""
