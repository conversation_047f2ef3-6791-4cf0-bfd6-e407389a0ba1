# SQL Scripts
These scripts should be ran in a specific order.

```bash
2024_08_01_a2110_new_data_model.sql
2024_09_02_1_enrichment_change.sql
2024_09_02_2_optic_matcher.sql
2024_09_05_rename_dashboards.sql
2024_10_22_release_link.sql
2024_11_26_add_user_id_to_links.sql
```

## Replace schema
Since the schema and user are specific to an environment, they use a placeholder in these scripts.

Before using them, find and replace `#{Schema}#` with the appropriate schema. Do the same thing for `#{DbUser}#`.

