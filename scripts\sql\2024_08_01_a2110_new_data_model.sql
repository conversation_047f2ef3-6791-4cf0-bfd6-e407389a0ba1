--------------------------------------------------
-- SEQUENCE: #{Schema}#.s2110_agent_heartbeat_id_seq
--------------------------------------------------

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_agent_heartbeat_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_agent_heartbeat_id_seq
    OWNER TO #{DbUser}#;
--------------------------------------------------
-- SEQUENCE: #{Schema}#.s2110_agent_id_seq
--------------------------------------------------

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_agent_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_agent_id_seq
    OWNER TO #{DbUser}#;

--------------------------------------------------
-- SEQUENCE: #{Schema}#.s2110_alarm_action_id_seq
--------------------------------------------------

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_alarm_action_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_alarm_action_id_seq
    OWNER TO #{DbUser}#;
    
--------------------------------------------------
-- SEQUENCE: #{Schema}#.s2110_alarm_id_seq
--------------------------------------------------

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_alarm_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_alarm_id_seq
    OWNER TO #{DbUser}#;

--------------------------------------------------
-- SEQUENCE: #{Schema}#.s2110_alarm_incident_id_seq
--------------------------------------------------

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_alarm_incident_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_alarm_incident_id_seq
    OWNER TO #{DbUser}#;

--------------------------------------------------
-- SEQUENCE: #{Schema}#.s2110_alarm_sub_service_id_seq
--------------------------------------------------

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_alarm_sub_service_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_alarm_sub_service_id_seq
    OWNER TO #{DbUser}#;
    
--------------------------------------------------
-- SEQUENCE: #{Schema}#.s2110_enrichment_id_seq
--------------------------------------------------

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_enrichment_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_enrichment_id_seq
    OWNER TO #{DbUser}#;
    
--------------------------------------------------
-- SEQUENCE: #{Schema}#.s2110_incident_id_seq
--------------------------------------------------

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_incident_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_incident_id_seq
    OWNER TO #{DbUser}#;
    
--------------------------------------------------
-- SEQUENCE: #{Schema}#.s2110_occurrence_id_seq
--------------------------------------------------

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_occurrence_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_occurrence_id_seq
    OWNER TO #{DbUser}#;
    
--------------------------------------------------------------------
-- TABLES
--------------------------------------------------------------------
    
--------------------------------------------------
-- Table: #{Schema}#.s2110_agent
--------------------------------------------------
CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_agent
(
    id bigint NOT NULL DEFAULT nextval('s2110_agent_id_seq'::regclass),
    name text COLLATE pg_catalog."default",
    ucmdb_name text COLLATE pg_catalog."default",
    ci_enrichment boolean,
    metric_enrichment boolean,
    topology_enrichment boolean,
    scope_ref text COLLATE pg_catalog."default",
    CONSTRAINT s2110_agent_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_agent
    OWNER TO #{DbUser}#;

--------------------------------------------------
-- Table: #{Schema}#.s2110_agent_heartbeat
--------------------------------------------------

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_agent_heartbeat
(
    id bigint NOT NULL DEFAULT nextval('s2110_agent_heartbeat_id_seq'::regclass),
    agent_id bigint,
    ci_id text COLLATE pg_catalog."default",
    metric_name text COLLATE pg_catalog."default",
    metric_type text COLLATE pg_catalog."default",
    heartbeat_type text COLLATE pg_catalog."default",
    period_seconds bigint,
    idle_time_seconds bigint,
    is_active boolean,
    CONSTRAINT s2110_agent_heartbeat_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_agent_heartbeat
    OWNER TO #{DbUser}#;
-- Index: s2110_agent_heartbeat_agent_fk_i
CREATE INDEX IF NOT EXISTS s2110_agent_heartbeat_agent_fk_i
    ON #{Schema}#.s2110_agent_heartbeat USING btree
    (agent_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
--------------------------------------------------
-- Table: #{Schema}#.s2110_enrichment
--------------------------------------------------

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_enrichment
(
    id bigint NOT NULL DEFAULT nextval('s2110_enrichment_id_seq'::regclass),
    alarm_id bigint,
    address text COLLATE pg_catalog."default",
    asset_number text COLLATE pg_catalog."default",
    asset_tag text COLLATE pg_catalog."default",
    location_category text COLLATE pg_catalog."default",
    brand text COLLATE pg_catalog."default",
    ci_friendly_name text COLLATE pg_catalog."default",
    ci_base_impact text COLLATE pg_catalog."default",
    ci_uuid text COLLATE pg_catalog."default",
    ci_planned_out_of_service_end timestamp without time zone,
    ci_planned_out_of_service_release_reference text COLLATE pg_catalog."default",
    ci_planned_out_of_service_start timestamp without time zone,
    ci_sub_type text COLLATE pg_catalog."default",
    ci_type text COLLATE pg_catalog."default",
    client_zone text COLLATE pg_catalog."default",
    critical_ci text COLLATE pg_catalog."default",
    documentation_path text COLLATE pg_catalog."default",
    environment text COLLATE pg_catalog."default",
    identification text COLLATE pg_catalog."default",
    in_service_date timestamp without time zone,
    intervention_address text COLLATE pg_catalog."default",
    ip_address text COLLATE pg_catalog."default",
    location text COLLATE pg_catalog."default",
    location_attributes text COLLATE pg_catalog."default",
    location_security text COLLATE pg_catalog."default",
    location_type text COLLATE pg_catalog."default",
    monitoring_status text COLLATE pg_catalog."default",
    network_alias text COLLATE pg_catalog."default",
    owner_company text COLLATE pg_catalog."default",
    responsible text COLLATE pg_catalog."default",
    retirement_date timestamp without time zone,
    subzone text COLLATE pg_catalog."default",
    zone text COLLATE pg_catalog."default",
    floc_id text COLLATE pg_catalog."default",
    ci_enrichment boolean,
    parent_impact boolean,
    top_level_impact boolean,
    documentation text COLLATE pg_catalog."default",
    metric_enrichment boolean,
    ci_sub_service text COLLATE pg_catalog."default",
    additional_data jsonb,
    top_level text COLLATE pg_catalog."default",
    enrichment_time timestamp without time zone,
    severity smallint,
    linked_environments text COLLATE pg_catalog."default",
    floc_class text COLLATE pg_catalog."default",
    sub_groups text COLLATE pg_catalog."default",
    model text COLLATE pg_catalog."default",
    actionable boolean,
    CONSTRAINT s2110_enrichment_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
/*,
        
    CONSTRAINT s2110_fk_enrichment_to_alarm FOREIGN KEY (alarm_id)
        REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
*/
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    OWNER TO #{DbUser}#;
-- Index: s2110_enrichment_alarm_fk_i
CREATE INDEX IF NOT EXISTS s2110_enrichment_alarm_fk_i
    ON #{Schema}#.s2110_enrichment USING btree
    (alarm_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
--------------------------------------------------
-- Table: #{Schema}#.s2110_incident
--------------------------------------------------

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_incident
(
    id bigint NOT NULL DEFAULT nextval('s2110_incident_id_seq'::regclass),
    sap_id bigint,
    execution_id bigint,
    instruction_id bigint,
    sap_status text COLLATE pg_catalog."default",
    CONSTRAINT s2110_incident_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_incident
    OWNER TO #{DbUser}#;
--------------------------------------------------
-- Table: #{Schema}#.s2110_alarm
--------------------------------------------------

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_alarm
(
    id bigint NOT NULL DEFAULT nextval('s2110_alarm_id_seq'::regclass),
    last_occurrence_id bigint,
    last_enrichment_id bigint,
    last_alarm_incident_id bigint,
    last_problem_id bigint,
    last_clear_id bigint,
    agent_id bigint,
    manager text COLLATE pg_catalog."default",
    node text COLLATE pg_catalog."default",
    tally bigint,
    event_url text COLLATE pg_catalog."default",
    metric_type text COLLATE pg_catalog."default",
    metric_name text COLLATE pg_catalog."default",
    release_id text COLLATE pg_catalog."default",
    wake_up_time timestamp without time zone,
    is_active boolean,
    first_raise_time timestamp without time zone,
    platform text COLLATE pg_catalog."default",
    node_alias text COLLATE pg_catalog."default",
    scope_ref text COLLATE pg_catalog."default",
    event_id text COLLATE pg_catalog."default",
    enrichment_status smallint,
    ui_sending_status smallint,
    process_error text COLLATE pg_catalog."default",
    nb_retries smallint,
    actionable boolean,
    clear_type text COLLATE pg_catalog."default",
    ci_id text COLLATE pg_catalog."default",
    top_level text COLLATE pg_catalog."default",
    CONSTRAINT s2110_alarm_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_data
/*,
    CONSTRAINT s2110_alarm_agent_fk FOREIGN KEY (agent_id)
        REFERENCES #{Schema}#.s2110_agent (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT s2110_alarm_alarm_incident_fk FOREIGN KEY (last_alarm_incident_id)
        REFERENCES #{Schema}#.s2110_alarm_incident (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT s2110_alarm_enrichment_fk FOREIGN KEY (last_enrichment_id)
        REFERENCES #{Schema}#.s2110_enrichment (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT s2110_alarm_occurrence_clear_fk FOREIGN KEY (last_clear_id)
        REFERENCES #{Schema}#.s2110_occurrence (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT s2110_alarm_occurrence_last_fk FOREIGN KEY (last_occurrence_id)
        REFERENCES #{Schema}#.s2110_occurrence (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT s2110_alarm_occurrence_problem_fk FOREIGN KEY (last_problem_id)
        REFERENCES #{Schema}#.s2110_occurrence (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
*/
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    OWNER TO #{DbUser}#;
-- Index: s2110_alarm_agent_fk_i
CREATE INDEX IF NOT EXISTS s2110_alarm_agent_fk_i
    ON #{Schema}#.s2110_alarm USING btree
    (agent_id ASC NULLS LAST)
    TABLESPACE s2110_index;
-- Index: s2110_alarm_alarm_incident_fk_i
CREATE INDEX IF NOT EXISTS s2110_alarm_alarm_incident_fk_i
    ON #{Schema}#.s2110_alarm USING btree
    (last_alarm_incident_id ASC NULLS LAST)
    TABLESPACE s2110_index;
-- Index: s2110_alarm_enrichment_fk_i
CREATE INDEX IF NOT EXISTS s2110_alarm_enrichment_fk_i
    ON #{Schema}#.s2110_alarm USING btree
    (last_enrichment_id ASC NULLS LAST)
    TABLESPACE s2110_index;
-- Index: s2110_alarm_occurrence_clear_fk_i
CREATE INDEX IF NOT EXISTS s2110_alarm_occurrence_clear_fk_i
    ON #{Schema}#.s2110_alarm USING btree
    (last_clear_id ASC NULLS LAST)
    TABLESPACE s2110_index;
-- Index: s2110_alarm_occurrence_last_fk_i
CREATE INDEX IF NOT EXISTS s2110_alarm_occurrence_last_fk_i
    ON #{Schema}#.s2110_alarm USING btree
    (last_occurrence_id ASC NULLS LAST)
    TABLESPACE s2110_index;
-- Index: s2110_alarm_occurrence_problem_fk_i
CREATE INDEX IF NOT EXISTS s2110_alarm_occurrence_problem_fk_i
    ON #{Schema}#.s2110_alarm USING btree
    (last_problem_id ASC NULLS LAST)
    TABLESPACE s2110_index;

--------------------------------------------------
-- Table: #{Schema}#.s2110_alarm_action
--------------------------------------------------

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_alarm_action
(
    id bigint NOT NULL DEFAULT nextval('s2110_alarm_id_seq'::regclass),
    alarm_id bigint,
    type text COLLATE pg_catalog."default",
    creation_time timestamp without time zone,
    comment text COLLATE pg_catalog."default",
    author text COLLATE pg_catalog."default",
    CONSTRAINT s2110_alarm_action_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
/*,
    CONSTRAINT s2110_fk_alarm_action_to_alarm FOREIGN KEY (alarm_id)
        REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
*/
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_action
    OWNER TO #{DbUser}#;
-- Index: s2110_alarm_action_alarm_fk_i
CREATE INDEX IF NOT EXISTS s2110_alarm_action_alarm_fk_i
    ON #{Schema}#.s2110_alarm_action USING btree
    (alarm_id ASC NULLS LAST)
    WITH (deduplicate_items=False)
    TABLESPACE s2110_index;
    
--------------------------------------------------
-- Table: #{Schema}#.s2110_alarm_incident
--------------------------------------------------

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_alarm_incident
(
    id bigint NOT NULL DEFAULT nextval('s2110_alarm_incident_id_seq'::regclass),
    alarm_id bigint,
    incident_id bigint,
    link_time timestamp without time zone,
    is_origin_alarm boolean,
    is_link_active boolean,
    CONSTRAINT s2110_alarm_incident_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
/*,
    CONSTRAINT s2110_fk_alarm_incident_to_alarm FOREIGN KEY (alarm_id)
        REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT s2110_fk_alarm_incident_to_incident FOREIGN KEY (incident_id)
        REFERENCES #{Schema}#.s2110_incident (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
*/
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_incident
    OWNER TO #{DbUser}#;
-- Index: s2110_idx_fk_alarm_incident_to_alarm
CREATE INDEX IF NOT EXISTS s2110_idx_fk_alarm_incident_to_alarm
    ON #{Schema}#.s2110_alarm_incident USING btree
    (alarm_id ASC NULLS LAST)
    TABLESPACE s2110_index;
-- Index: s2110_idx_fk_alarm_incident_to_incident
CREATE INDEX IF NOT EXISTS s2110_idx_fk_alarm_incident_to_incident
    ON #{Schema}#.s2110_alarm_incident USING btree
    (incident_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
--------------------------------------------------
-- Table: #{Schema}#.s2110_alarm_sub_service
--------------------------------------------------

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_alarm_sub_service
(
    id bigint NOT NULL DEFAULT nextval('s2110_alarm_sub_service_id_seq'::regclass),
    alarm_id bigint,
    sub_service_code text COLLATE pg_catalog."default",
    CONSTRAINT s2110_alarm_sub_service_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
/*,
    CONSTRAINT s2110_fk_alarm_sub_service_to_alarm FOREIGN KEY (alarm_id)
        REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
*/
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_sub_service
    OWNER TO #{DbUser}#;
-- Index: s2110_idx_fk_alarm_sub_service_to_alarm
CREATE INDEX IF NOT EXISTS s2110_idx_fk_alarm_sub_service_to_alarm
    ON #{Schema}#.s2110_alarm_sub_service USING btree
    (alarm_id ASC NULLS LAST)
    TABLESPACE s2110_index;

--------------------------------------------------
-- Table: #{Schema}#.s2110_occurrence
--------------------------------------------------

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_occurrence
(
    id bigint NOT NULL DEFAULT nextval('s2110_occurrence_id_seq'::regclass),
    alarm_id bigint,
    summary text COLLATE pg_catalog."default",
    severity smallint,
    event_type text COLLATE pg_catalog."default",
    raise_time timestamp without time zone,
    clear_time timestamp without time zone,
    additional_data jsonb,
    handle_time timestamp without time zone,
    ci_id text COLLATE pg_catalog."default",
    agent_id bigint,
    metric_type text COLLATE pg_catalog."default",
    metric_name text COLLATE pg_catalog."default",
    CONSTRAINT s2110_occurrence_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
/*,
    CONSTRAINT s2110_occurrence_1_uk UNIQUE (ci_id, agent_id, metric_type, metric_name, raise_time)
        USING INDEX TABLESPACE s2110_index,
    CONSTRAINT s2110_fk_occurrence_to_agent FOREIGN KEY (agent_id)
        REFERENCES #{Schema}#.s2110_agent (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
        NOT VALID,
    CONSTRAINT s2110_fk_occurrence_to_alarm FOREIGN KEY (alarm_id)
        REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
*/
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_occurrence
    OWNER TO #{DbUser}#;
-- Index: s2110_idx_fk_occurrence_to_agent
CREATE INDEX IF NOT EXISTS s2110_idx_fk_occurrence_to_agent
    ON #{Schema}#.s2110_occurrence USING btree
    (agent_id ASC NULLS LAST)
    WITH (deduplicate_items=False)
    TABLESPACE s2110_index;
-- Index: s2110_idx_fk_occurrence_to_alarm
CREATE INDEX IF NOT EXISTS s2110_idx_fk_occurrence_to_alarm
    ON #{Schema}#.s2110_occurrence USING btree
    (alarm_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
--------------------------------------------------------------------
-- CONSTRAINTS
--------------------------------------------------------------------
    
-- Constraint: s2110_agent_heartbeat_agent_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_agent_heartbeat
    ADD CONSTRAINT s2110_agent_heartbeat_agent_fk FOREIGN KEY (agent_id)
    REFERENCES #{Schema}#.s2110_agent (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_enrichment_alarm_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD CONSTRAINT s2110_enrichment_alarm_fk FOREIGN KEY (alarm_id)
    REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_agent_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    ADD CONSTRAINT s2110_alarm_agent_fk FOREIGN KEY (agent_id)
    REFERENCES #{Schema}#.s2110_agent (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_alarm_incident_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    ADD CONSTRAINT s2110_alarm_alarm_incident_fk FOREIGN KEY (last_alarm_incident_id)
    REFERENCES #{Schema}#.s2110_alarm_incident (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_enrichment_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    ADD CONSTRAINT s2110_alarm_enrichment_fk FOREIGN KEY (last_enrichment_id)
    REFERENCES #{Schema}#.s2110_enrichment (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_occurrence_last_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    ADD CONSTRAINT s2110_alarm_occurrence_last_fk FOREIGN KEY (last_occurrence_id)
    REFERENCES #{Schema}#.s2110_occurrence (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_occurrence_clear_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    ADD CONSTRAINT s2110_alarm_occurrence_clear_fk FOREIGN KEY (last_clear_id)
    REFERENCES #{Schema}#.s2110_occurrence (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_occurrence_problem_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    ADD CONSTRAINT s2110_alarm_occurrence_problem_fk FOREIGN KEY (last_problem_id)
    REFERENCES #{Schema}#.s2110_occurrence (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_action_alarm_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_action
    ADD CONSTRAINT s2110_alarm_action_alarm_fk FOREIGN KEY (alarm_id)
    REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_incident_alarm_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_incident
    ADD CONSTRAINT s2110_alarm_incident_alarm_fk FOREIGN KEY (alarm_id)
    REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_incident_incident_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_incident
    ADD CONSTRAINT s2110_alarm_incident_incident_fk FOREIGN KEY (incident_id)
    REFERENCES #{Schema}#.s2110_incident (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_alarm_sub_service_alarm_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_sub_service
    ADD CONSTRAINT s2110_alarm_sub_service_alarm_fk FOREIGN KEY (alarm_id)
    REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_occurrence_agent_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_occurrence
    ADD CONSTRAINT s2110_occurrence_agent_fk FOREIGN KEY (agent_id)
    REFERENCES #{Schema}#.s2110_agent (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_occurrence_alarm_fk
ALTER TABLE IF EXISTS #{Schema}#.s2110_occurrence
    ADD CONSTRAINT s2110_occurrence_alarm_fk FOREIGN KEY (alarm_id)
    REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
-- Constraint: s2110_occurrence_1_uk
ALTER TABLE IF EXISTS #{Schema}#.s2110_occurrence
    ADD CONSTRAINT s2110_occurrence_1_uk UNIQUE (ci_id, agent_id, metric_type, metric_name, raise_time)
    USING INDEX TABLESPACE s2110_index;
