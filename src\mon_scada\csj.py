"""Detail design implementation for mon-scada csj."""

from datetime import datetime

import pandas as pd

from mon_scada.utils import is_active_status, is_tunnel_csj
from olympus_common import enums
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.utils import parse_datetime


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def metric_name(row: pd.Series) -> str:
    """Return the metric name for the DD."""
    return row["alarm_description_fr"].split("_")[-1]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def ci_id(row: pd.Series) -> str:
    """Return the ci_id for the DD."""
    ci_id = ""
    if is_tunnel_csj(row):
        # get the first part before the dot
        ci_id = row["asset_id"].split(".")[0]
    return ci_id


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def severity(row: pd.Series) -> int:
    """Return the severity for the DD."""
    status: str = str(row["status"])
    priority: str = row["priority"]  # 'Alarme SCCIT Mineure'

    if not is_active_status(status=status):
        return enums.Severity.CLEARED.value

    severity_map: dict[str, enums.Severity] = {
        "Mineure": enums.Severity.MINOR,
        "Majeure": enums.Severity.MAJOR,
        "Critique": enums.Severity.CRITICAL,
    }

    for key in severity_map.keys():
        if key in priority:
            return severity_map[key].value
    return enums.Severity.INDETERMINATE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_time(row: pd.Series) -> datetime:
    """Return the raise time for the DD."""
    timestamp: str = row["timestamp"]
    return parse_datetime(timestamp)
