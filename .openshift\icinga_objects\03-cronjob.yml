apiVersion: batch/v1
kind: CronJob
metadata:
  name: a2110-icinga-objects-cronjob-#{appEnv}#
  namespace: a2110-olympus-monitoring
spec:
  schedule: "#{scheduleTime}#"
  concurrencyPolicy: "Replace"
  startingDeadlineSeconds: 300
  suspend: false
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 2
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: "a2110-icinga-objects-app-#{appEnv}#"
        spec:
          volumes:
            - name: a2110-olympus-volume-claim-#{appEnv}#
              persistentVolumeClaim:
                claimName: a2110-olympus-volume-claim-#{appEnv}#
          containers:
            - name: a2110-icinga-objects-app-#{appEnv}#
              image: "artifactory.msnet.railb.be/a2110-docker/a2110-olympus-image:#{releaseName}#"
              volumeMounts:
                - name: a2110-olympus-volume-claim-#{appEnv}#
                  mountPath: "/data"
                  subPath: "#{olympusServiceName}#/"
              envFrom:
                - configMapRef:
                    name: a2110-icinga-objects-config-map-#{appEnv}#
                - secretRef:
                    name: a2110-icinga-objects-secret-#{appEnv}#
              resources:
                limits:
                  memory: 2Gi
          restartPolicy: OnFailure
