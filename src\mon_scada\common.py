"""Common module for mon-scada."""

from datetime import datetime, timedelta

import pandas as pd

from mon_scada.utils import is_active_status, is_heartbeat
from olympus_common import enums
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.utils import now_naive, parse_datetime

NA_STR = "N/A"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def agent() -> str:
    """Return the agent name for the DD."""
    return "SCADA"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def manager() -> str:
    """Return the manager for the DD."""
    return "mon-scada"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def metric_type() -> str:
    """Return the metric type for the DD."""
    return "/HardwareEvent/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def node() -> str:
    """Return the node for the DD."""
    return NA_STR


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def additional_data() -> str:
    """Return the additional data of the DD."""
    return NA_STR


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def clear_type() -> str:
    """Return the clear type for the DD."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def summary(row: pd.Series) -> str:
    """Return the summary of the DD."""
    alarm_description_en: str = row["alarm_description_en"]
    risk_en: str = row["risk_en"]
    procedure: str = row["procedure"]
    if not alarm_description_en and not risk_en and not procedure:
        return NA_STR
    return f"{alarm_description_en}|{risk_en}|{procedure}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def handle_time() -> datetime:
    """Return the handle time for the DD."""
    return now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def delay() -> int:
    """Return the delay for the DD."""
    return 30


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def raise_time(row: pd.Series) -> datetime:
    """Return the raise time for the DD."""
    timestamp: str = row["timestamp"]
    return parse_datetime(timestamp)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def wake_up_time(row: pd.Series) -> datetime:
    """Return the wake up time for the DD."""
    wake_up_time = raise_time(row) + timedelta(days=0, seconds=delay())
    return wake_up_time


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def action_class() -> str:
    """Return the action class for the DD."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def event_type(row: pd.Series) -> str:
    """Return the event type for the DD."""
    # clear -> resolution, alarm -> problem
    status: str = str(row["status"])
    if is_heartbeat(row):
        return enums.AlarmType.HEARTBEAT.value

    if not is_active_status(status=status):
        # inactive alarm
        return enums.AlarmType.RESOLUTION.value
    # active alarm
    return enums.AlarmType.PROBLEM.value
