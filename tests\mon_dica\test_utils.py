import pytest

from mon_dica.utils import conversion_to_alarmtype, conversion_to_severity, is_heartbeat
from olympus_common import enums


def test_is_heartbeat():
    assert is_heartbeat("HeartBeat event") is True
    assert is_heartbeat("Normal event") is False
    assert is_heartbeat("") is False


def test_conversion_to_severity():
    assert conversion_to_severity(1) == enums.Severity.CRITICAL.value
    assert conversion_to_severity(2) == enums.Severity.WARNING.value
    assert conversion_to_severity(3) == enums.Severity.INDETERMINATE.value
    assert conversion_to_severity(4) == enums.Severity.INDETERMINATE.value
    with pytest.raises(ValueError, match="Unexpected value for alarm_priority: 5"):
        conversion_to_severity(5)


def test_conversion_to_alarmtype():
    assert conversion_to_alarmtype(1) == enums.AlarmType.PROBLEM.value
    assert conversion_to_alarmtype(2) == enums.AlarmType.PROBLEM.value
    assert conversion_to_alarmtype(3) == enums.AlarmType.PROBLEM.value
    assert conversion_to_alarmtype(4) == enums.AlarmType.RESOLUTION.value
    with pytest.raises(ValueError, match="Unexpected for alarm_priority: 5"):
        conversion_to_alarmtype(5)
