# Openshift configuration for A2110-Olympus

## Directory layout
Each service in `A2110-Olympus` can define its own openshift configuration in a subfolder of `.openshift`.<br/>
The subfolder should be named like the service's name. (eg: `.openshift/myservice/00-secrets.yml` for a service named `myservice`)

## Objects
### Config-map
Used to declare clear-text runtime environment variables for a subservice.<br/>
Suggested filename: `.openshift/my-service/00-config-map.yml`

### Persistent volume claim
Used to save data on a volume for a subservice. This is useful to save logs, since normally pods on openshift are ephemeral.<br/>
Suggested filename: `.openshift/my-service/00-persistent-volume-claim.yml`.

### Secret
Used to declare sensitive runtime environment variables for a subservice.<br/>
Suggested filename: `.openshift/my-service/00-secret.yml`.

### Image Stream
**! No longer used !**<br/>
Used as an output for build-configs in case automatic triggers are required (eg: automatically rollout when a new image is available).<br/>
Since we use Azure DevOps to manage our releases, automatic triggers are not needed. For this reason we no longer use image-streams, but have switched to DockerImages instead. These are hosted on artifactory and do not require separate yml files.<br/>
~~Suggested filename: `.openshift/a2110-olympus/01-image-stream.yml`.~~

### Build Config
Used to create a new build for the application.<br/>
- The build-config will pull this git repository and checkout to the replaceToken `sourceBranch`.<br/>
  The value of `sourceBranch` depends on the way you created the release:<br/>
  If you chose to create a branch `release/Sprint*`, this will be the value, otherwise `main` will be the value.
- The code is built by using the strategy `Source2Image` on the internal DockerImage `ubi8-python311-certs:latest`.<br/>
  By setting `ENABLE_MICROPIPENV` our `poetry.lock` file will be converted into a supported format and our dependencies will be properly installed.<br/>
  By setting the environment variable `APP_FILE` in your config-map, the application will be started from that file. (For a2110-Olympus we always want to use `src/a2110_olympus/run.py`)
- Once the build succeeds, the resulting DockerImage will be pushed to the internal artifactory with as a tag the value of the replaceToken `releaseName`.

**This object should only be defined once. Preferably as `.openshift/a2110_olympus/02-build-config.yml`.<br/>**

### Deployment Config
Used to deploy an instance of the application. Each subservice should define a deployment-config and set the environment variable `OLYMPUS_SERVICE_NAME`.<br/>
No triggers should be specified as rollouts should be done in Azure DevOps releases.<br/>
The container's image (`spec.template.spec.containers.image`) should point to the build-config's output (`spec.output.to.name`).<br/>
Suggested filename: `.openshift/my-service/03-deployment-config.yml`.

### Service
Usually used in combination with a route if a subservice needs to expose some kind of frontend.
Suggested filename: `.openshift/my-service/04-service.yml`.

### Route
Used when a subservice needs to expose some kind of frontend.
Suggested filename: `.openshift/my-service/05-route.yml`.

## System requirements
When using the command line to execute openshift commands, some software should be installed on your local machine:
- The [openshift CLI](https://docs.openshift.com/container-platform/4.8/cli_reference/openshift_cli/getting-started-cli.html).
- A terminal to execute commands.

## Usage

Extensive documentation is available on [openshift's official documentation page](https://docs.openshift.com/container-platform/4.8/cli_reference/openshift_cli/developer-cli-commands.html)


##### Note: depending on your current working directory and operating system, you might need to adjust the path in the following commands.

### Create a config for an existing namespace

```bash
oc create -f .openshift/{service-name}/00-filename.yml
```

### Replace an existing config for an existing namespace

```bash
oc replace -f .openshift/{service-name}/00-filename.yml
```

### Replace tokens

The yml files in the .openshift folder contain some tokens which should be replaced (Azure pipelines has a task for this).<br/>
When configuring the release-pipeline in Azure devops, variables should be set in the correct stage.<br/>
To find out exactly which replaceTokens you should use in your service, check `config.py` or `00-config-map.yml` and `00-secret.yml`.<br/>
Some of the most common replace tokens:
* appEnv: This enables us to spin up pods for different environments. (value: tst, acc, ...)
* sourceBranch: This enables us to have a variable release branch (eg: release/Sprint01 or release/Sprint02) (value: $(Build.SourceBranch))
* openshiftEnv: This enables us to have a dynamic route for nonprod and prod (value: apps.nonprod-ocp.infrabel.be, apps.prod.infrabel.be)
* olympusServiceName: This enables us to dynamically run a service from the monolithic application. (value: mon_cnms)
* olympusServiceNameKebab: Used in the release-pipeline stages to construct openshift object names. (value: mon-cnms)

### Secrets

Secrets are currently managed using `.openshift/{service-name}/00-secret.yml` and replace tokens. You can then set these tokens in your release-pipeline's variables.

Since the build-config will always pull the repository from git, a personal access token is required.
Create a [personal-access-token](https://dev.azure.com/INFRABEL/_usersSettings/tokens) in Azure with permission `code (read)`.  
Then create the yaml file for this token and upload it.
tokensecret:
```yaml
---
kind: Secret
apiVersion: v1
metadata:
  name: tokensecret
  namespace: a123-your-namespace
stringData:
  password: yourplaintextpassword
type: Opaque
```
Then upload this file
```bash
oc create -f file-with-tokensecret.yml
```
##### NOTE: Ensure that these secrets are NOT version controlled as this would compromise the secret. Using replace tokens is a valid alternative.
##### NOTE: tokensecret can be re-used across multiple repositories as long as the user creating the token has access to the repository on which it is used.
