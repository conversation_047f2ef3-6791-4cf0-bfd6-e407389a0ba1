{"inputs": [{"@timestamp": "2024-07-04T03:28:27.970323989Z", "message": "#<SNMP::SNMPv2_Trap:0xc608000 @varbind_list=[#<SNMP::VarBind:0x67db6da1 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x215dbf20 @value=84978074>>, #<SNMP::VarBind:0x29354b8b @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x6cd302d5 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x50d9cc8b @value=723402>>, #<SNMP::VarBind:0x1c31dd0f @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x1f5ba411 @value=2>>, #<SNMP::VarBind:0x6beac885 @name=[*******.4.1.2544.********.1.3], @value=\"ENT-STAT\">, #<SNMP::VarBind:0x75c9ece7 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x67828ef4 @value=3>>, #<SNMP::VarBind:0x253bd539 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x18a88ffb @value=5>>, #<SNMP::VarBind:0x22bce6dd @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x513a3950 @value=2>>, #<SNMP::VarBind:0x2ad3bc53 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x76d839a5 @value=2>>, #<SNMP::VarBind:0x3397a3ad @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x33e7ec36 @value=100>>, #<SNMP::VarBind:0x461ef3d @name=[*******.4.1.2544.********.1.9], @value=\"adblock11\">, #<SNMP::VarBind:0x3343eb05 @name=[*******.4.1.2544.********.1.10], @value=\"***********\">, #<SNMP::VarBind:0x3d47ac0b @name=[*******.4.1.2544.********.1.11], @value=\"CH-2-4-C6\">, #<SNMP::VarBind:0x314d61c9 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x3a5e78da @value=0>>, #<SNMP::VarBind:0x1669a757 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x16ce7665 @value=0>>, #<SNMP::VarBind:0x4cd70e5c @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x05\\x1C\\e\\a\">, #<SNMP::VarBind:0x58627b4 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x05\\x1C\\e\\a\">, #<SNMP::VarBind:0x5e8e6b4c @name=[*******.4.1.2544.********.1.16], @value=\"Operational State = OUT (Outage)\">, #<SNMP::VarBind:0x3b1643a6 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x4fc71f76 @value=2>>, #<SNMP::VarBind:0x7b483abd @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x56b1fa93 @value=2>>, #<SNMP::VarBind:0x5fb39890 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x287810bf @value=0>>, #<SNMP::VarBind:0x125f5adf @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x4b634f65 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x8573981 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x60423ddc @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x8a31aa9 @value=2>>, #<SNMP::VarBind:0x596a64bf @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421959940, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "9 days, 20:03:00.74", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "d4992c7a-2d5f-43ec-b296-5ffce918d9d3", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T03:28:28.070Z", "event.kafka.key": null, "event.kafka.offset": 82108, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.corrRef": "", "snmptrap.severity": "5", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Operational State = OUT (Outage)", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0005\u001c\u001b\u0007", "snmptrap.comment": "", "snmptrap.name": "ENT-STAT", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adblock11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0005\u001c\u001b\u0007", "snmptrap.entity": "CH-2-4-C6", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "723402", "snmptrap.nelpAdress": "***********", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T03:29:25.201504644Z", "message": "#<SNMP::SNMPv2_Trap:0x54c84037 @varbind_list=[#<SNMP::VarBind:0x284430a2 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x7f4dac55 @value=84983796>>, #<SNMP::VarBind:0x69129273 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x72aca9eb @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x7b64563c @value=723404>>, #<SNMP::VarBind:0x69596201 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x14a5f453 @value=2>>, #<SNMP::VarBind:0x3d1f4dd @name=[*******.4.1.2544.********.1.3], @value=\"ENT-STAT\">, #<SNMP::VarBind:0x5add972d @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x78b6af1d @value=3>>, #<SNMP::VarBind:0x430adb34 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x4ff1b894 @value=5>>, #<SNMP::VarBind:0x79a33ab8 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x3420acd6 @value=2>>, #<SNMP::VarBind:0x21e6121d @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x63912bad @value=2>>, #<SNMP::VarBind:0x307bb9d @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x32f0e137 @value=100>>, #<SNMP::VarBind:0x23177b8e @name=[*******.4.1.2544.********.1.9], @value=\"adblock11\">, #<SNMP::VarBind:0x49b1f002 @name=[*******.4.1.2544.********.1.10], @value=\"***********\">, #<SNMP::VarBind:0x775471fa @name=[*******.4.1.2544.********.1.11], @value=\"CH-2-4-C6\">, #<SNMP::VarBind:0x4298df7e @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x5acb17a8 @value=0>>, #<SNMP::VarBind:0x1c3cdd13 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x4833a4cf @value=0>>, #<SNMP::VarBind:0x4cd4f198 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x05\\x1D\\x19\\x00\">, #<SNMP::VarBind:0x4e20d9fd @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x05\\x1D\\x18\\x00\">, #<SNMP::VarBind:0x340e516f @name=[*******.4.1.2544.********.1.16], @value=\"Operational State = OUT (Outage)\">, #<SNMP::VarBind:0x614e9efd @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0xafdfb7d @value=2>>, #<SNMP::VarBind:0x42da34ee @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x41990f3 @value=2>>, #<SNMP::VarBind:0x11b55ffb @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x74f668a3 @value=0>>, #<SNMP::VarBind:0x67185f97 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x2f4ed58f @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x14bf1d1a @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x53cbcc5e @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x760ac557 @value=2>>, #<SNMP::VarBind:0x2fb184b4 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421959942, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "9 days, 20:03:57.96", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "9682473b-7363-44fa-a7fb-9bd396f6b710", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T03:29:25.302Z", "event.kafka.key": null, "event.kafka.offset": 81128, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "5", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Operational State = OUT (Outage)", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0005\u001d\u0019\u0000", "snmptrap.comment": "", "snmptrap.name": "ENT-STAT", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adblock11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0005\u001d\u0018\u0000", "snmptrap.entity": "CH-2-4-C6", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "723404", "snmptrap.nelpAdress": "***********", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T03:32:17.912153950Z", "message": "#<SNMP::SNMPv2_Trap:0x16a20e1e @varbind_list=[#<SNMP::VarBind:0x5e62ca5 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x35f1b25f @value=85001068>>, #<SNMP::VarBind:0x33a59801 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x2b7aae5a @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x1f4d3c94 @value=723410>>, #<SNMP::VarBind:0x27961668 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x386cf70b @value=2>>, #<SNMP::VarBind:0x461a8622 @name=[*******.4.1.2544.********.1.3], @value=\"ENT-STAT\">, #<SNMP::VarBind:0x30c5bbe9 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x27f7cecf @value=3>>, #<SNMP::VarBind:0x787cb195 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x6ac5032e @value=5>>, #<SNMP::VarBind:0x77c2f76c @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x6edadeed @value=2>>, #<SNMP::VarBind:0x73b73aba @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x31a98b91 @value=2>>, #<SNMP::VarBind:0x5130635f @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x69a5e222 @value=100>>, #<SNMP::VarBind:0x238d6746 @name=[*******.4.1.2544.********.1.9], @value=\"adblock11\">, #<SNMP::VarBind:0x5e0595a5 @name=[*******.4.1.2544.********.1.10], @value=\"***********\">, #<SNMP::VarBind:0xc7d2fa1 @name=[*******.4.1.2544.********.1.11], @value=\"CH-2-4-C6\">, #<SNMP::VarBind:0x4a8f68fe @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x27081be @value=0>>, #<SNMP::VarBind:0x70aa94e8 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x154fbd7b @value=0>>, #<SNMP::VarBind:0x24b6ffb4 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x05 \\x11\\a\">, #<SNMP::VarBind:0x224de7ed @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x05 \\x11\\a\">, #<SNMP::VarBind:0x6643a98 @name=[*******.4.1.2544.********.1.16], @value=\"Operational State = OUT (Outage)\">, #<SNMP::VarBind:0x6e28fb4a @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x7d70f264 @value=2>>, #<SNMP::VarBind:0x304f10a8 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x17d84cc3 @value=2>>, #<SNMP::VarBind:0x3e8307d9 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x441a31bf @value=0>>, #<SNMP::VarBind:0xc18a3c4 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x59f358d0 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x7f6831f8 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x6a6a7063 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x2325f869 @value=2>>, #<SNMP::VarBind:0x60531105 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421959949, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "9 days, 20:06:50.68", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "c5cb7caa-d310-41c4-ae96-c089d26625e8", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T03:32:18.013Z", "event.kafka.key": null, "event.kafka.offset": 81575, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.corrRef": "", "snmptrap.severity": "5", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Operational State = OUT (Outage)", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0005 \u0011\u0007", "snmptrap.comment": "", "snmptrap.name": "ENT-STAT", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adblock11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0005 \u0011\u0007", "snmptrap.entity": "CH-2-4-C6", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "723410", "snmptrap.nelpAdress": "***********", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T03:31:32.915837595Z", "message": "#<SNMP::SNMPv2_Trap:0x1198eda @varbind_list=[#<SNMP::VarBind:0x6a0e6e8f @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x4ddd5576 @value=84996569>>, #<SNMP::VarBind:0xff7d49 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x1363ab67 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0xa00d592 @value=0>>, #<SNMP::VarBind:0x2d515741 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x563f6297 @value=1>>, #<SNMP::VarBind:0x5889d3a4 @name=[*******.4.1.2544.********.1.3], @value=\"HEART-BEAT\">, #<SNMP::VarBind:0x54a93162 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x73659fe9 @value=5>>, #<SNMP::VarBind:0x5e6370c8 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x48618c53 @value=1>>, #<SNMP::VarBind:0x55de961c @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0xa9bd199 @value=2>>, #<SNMP::VarBind:0x4fbb1532 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0xb7c0b82 @value=2>>, #<SNMP::VarBind:0x2ab30287 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x7ef00951 @value=9999>>, #<SNMP::VarBind:0x69759138 @name=[*******.4.1.2544.********.1.9], @value=\"HICTAIAPWV014\">, #<SNMP::VarBind:0x23eb78f @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x302b3a75 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x4e6f07a0 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0xbd6b304 @value=0>>, #<SNMP::VarBind:0x7c23ef3e @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x15b29a5d @value=0>>, #<SNMP::VarBind:0x2356dd4c @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x05\\x1F \\a\">, #<SNMP::VarBind:0x1d2604b1 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x05\\x1F \\a\">, #<SNMP::VarBind:0x7e9f4234 @name=[*******.4.1.2544.********.1.16], @value=\"FSP-NM Heart Beat\">, #<SNMP::VarBind:0x7ffe4fc8 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x6ad751c6 @value=1>>, #<SNMP::VarBind:0x47f9d3a1 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x4373b788 @value=2>>, #<SNMP::VarBind:0x4efe3021 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x1e770159 @value=0>>, #<SNMP::VarBind:0x179aaded @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x498b9761 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x764bc949 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x50107310 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x1227493a @value=2>>, #<SNMP::VarBind:0x782f480 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421959947, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "9 days, 20:06:05.69", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "eee81cde-7985-4e5c-be7f-6f78b9e541bd", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T03:31:33.016Z", "event.kafka.key": null, "event.kafka.offset": 81132, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "5", "snmptrap.update": "1", "snmptrap.neType": "9999", "snmptrap.direction": "0", "snmptrap.summary": "FSP-NM Heart Beat", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0005\u001f \u0007", "snmptrap.comment": "", "snmptrap.name": "HEART-BEAT", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "1", "snmptrap.neName": "HICTAIAPWV014", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0005\u001f \u0007", "snmptrap.entity": "", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "0", "snmptrap.nelpAdress": "*************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T11:02:01.100614036Z", "message": "#<SNMP::SNMPv2_Trap:0x280ea02d @varbind_list=[#<SNMP::VarBind:0x21d27c35 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0xe5dd51b @value=87699434>>, #<SNMP::VarBind:0x538f0335 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x1b9f631 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x333811e7 @value=724380>>, #<SNMP::VarBind:0x53adff96 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x7810d87d @value=2>>, #<SNMP::VarBind:0x36d6b130 @name=[*******.4.1.2544.********.1.3], @value=\"LOS\">, #<SNMP::VarBind:0x45122924 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x79ff3f5f @value=1>>, #<SNMP::VarBind:0x752c32d @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x48ebdc6 @value=2>>, #<SNMP::VarBind:0x5001ef7a @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x500437e2 @value=1>>, #<SNMP::VarBind:0x415ecd64 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x304f3804 @value=2>>, #<SNMP::VarBind:0x6fea6b48 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x1ea0fa70 @value=100>>, #<SNMP::VarBind:0x29a2312d @name=[*******.4.1.2544.********.1.9], @value=\"adblock11\">, #<SNMP::VarBind:0x60e4b305 @name=[*******.4.1.2544.********.1.10], @value=\"***********\">, #<SNMP::VarBind:0x5c68247a @name=[*******.4.1.2544.********.1.11], @value=\"CH-1-10-C\">, #<SNMP::VarBind:0x458226fd @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x7d114c39 @value=0>>, #<SNMP::VarBind:0x3828a405 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x257e1c8e @value=4>>, #<SNMP::VarBind:0x711f6312 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\r\\x02\\x01\\x03\">, #<SNMP::VarBind:0x698898c @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\r\\x01:\\x03\">, #<SNMP::VarBind:0x7bed0dd6 @name=[*******.4.1.2544.********.1.16], @value=\"Loss of Signal\">, #<SNMP::VarBind:0x2ea2f449 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x24af157c @value=2>>, #<SNMP::VarBind:0x66b93c8c @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x75290e30 @value=2>>, #<SNMP::VarBind:0x4c90bc60 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x1048547b @value=0>>, #<SNMP::VarBind:0x3fba57a2 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x2df96eb @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x4be5496c @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x195c37e7 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x74366429 @value=2>>, #<SNMP::VarBind:0xa20dc8a @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421961009, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 03:36:34.34", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "f9e865a8-aed6-49c8-8857-fb51f2f6c8f3", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T11:02:01.203Z", "event.kafka.key": null, "event.kafka.offset": 82441, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.corrRef": "", "snmptrap.severity": "2", "snmptrap.event_type": "1", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "4", "snmptrap.summary": "Loss of Signal", "snmptrap.nmsTime": "\u0007è\u0007\u0004\r\u0002\u0001\u0003", "snmptrap.comment": "", "snmptrap.name": "LOS", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adblock11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\r\u0001:\u0003", "snmptrap.entity": "CH-1-10-C", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "724380", "snmptrap.nelpAdress": "***********", "snmptrap.security": "2", "snmptrap.impairment": "1"}, {"@timestamp": "2024-07-04T14:29:20.350350772Z", "message": "#<SNMP::SNMPv2_Trap:0x5446d15a @varbind_list=[#<SNMP::VarBind:0x6a905d0b @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x81a4fa2 @value=88943324>>, #<SNMP::VarBind:0x6ab483fa @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x588f4fc1 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x301f39de @value=724832>>, #<SNMP::VarBind:0x6aa21720 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x24c7e2dd @value=2>>, #<SNMP::VarBind:0x61440c3 @name=[*******.4.1.2544.********.1.3], @value=\"DCN\">, #<SNMP::VarBind:0x4f8c4816 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x35ed5dd0 @value=1>>, #<SNMP::VarBind:0x2ea790ba @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0xa91dff5 @value=3>>, #<SNMP::VarBind:0x27f0ea49 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x782c20ba @value=2>>, #<SNMP::VarBind:0x6a1c4d45 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x6ef65984 @value=2>>, #<SNMP::VarBind:0x4e7444b1 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x57448435 @value=100>>, #<SNMP::VarBind:0x5fba8dfd @name=[*******.4.1.2544.********.1.9], @value=\"admechl22\">, #<SNMP::VarBind:0x3dcc5f7 @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x6fe158f1 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x1c771c14 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x1729bf02 @value=0>>, #<SNMP::VarBind:0x723f336d @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x177cdb0b @value=0>>, #<SNMP::VarBind:0x33cfe24c @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x10\\x1D\\x14\\x02\">, #<SNMP::VarBind:0x69448a98 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x10\\x1D\\x14\\x02\">, #<SNMP::VarBind:0x62eb6886 @name=[*******.4.1.2544.********.1.16], @value=\"Network element does not respond to SNMP requests\">, #<SNMP::VarBind:0x133e5353 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x5f2f1efa @value=2>>, #<SNMP::VarBind:0x12f24fcd @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x2be4aa4d @value=2>>, #<SNMP::VarBind:0x4a21c0a9 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x6ddedfd9 @value=0>>, #<SNMP::VarBind:0x6d527988 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x63ea00ad @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x278aa0f1 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x3c3193ce @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x425123f9 @value=2>>, #<SNMP::VarBind:0x718dac76 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421961502, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 07:03:53.24", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "1682d59f-d029-4ec4-babe-423d1364a68f", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T14:29:20.455Z", "event.kafka.key": null, "event.kafka.offset": 81733, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "3", "snmptrap.event_type": "1", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Network element does not respond to SNMP requests", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0010\u001d\u0014\u0002", "snmptrap.comment": "", "snmptrap.name": "DCN", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "admechl22", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0010\u001d\u0014\u0002", "snmptrap.entity": "", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "724832", "snmptrap.nelpAdress": "*************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-07T22:41:28.888598558Z", "message": "#<SNMP::SNMPv2_Trap:0x26b011da @varbind_list=[#<SNMP::VarBind:0xfe59c16 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x63f2d9e7 @value=117816168>>, #<SNMP::VarBind:0x456a3a82 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x551b3896 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x76bd0b0f @value=735284>>, #<SNMP::VarBind:0x3f5d9de @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x175f4d08 @value=2>>, #<SNMP::VarBind:0x1b8c18fd @name=[*******.4.1.2544.********.1.3], @value=\"LBCLANR\">, #<SNMP::VarBind:0x5f78df0 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x28618fb9 @value=1>>, #<SNMP::VarBind:0x45bbb0b0 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x3e8b130a @value=4>>, #<SNMP::VarBind:0x72a9126 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x2312da3d @value=2>>, #<SNMP::VarBind:0x3b34c0df @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x5dc187f0 @value=2>>, #<SNMP::VarBind:0xeb18ecf @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x3b476e16 @value=100>>, #<SNMP::VarBind:0x26687399 @name=[*******.4.1.2544.********.1.9], @value=\"admechl11\">, #<SNMP::VarBind:0x77c79ea3 @name=[*******.4.1.2544.********.1.10], @value=\"************\">, #<SNMP::VarBind:0x7502b162 @name=[*******.4.1.2544.********.1.11], @value=\"CH-1-3-N2\">, #<SNMP::VarBind:0x75b5460a @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x10a4079e @value=0>>, #<SNMP::VarBind:0x5b5da1af @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x3b348207 @value=5>>, #<SNMP::VarBind:0x646e067d @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\b\\x00)\\n\\a\">, #<SNMP::VarBind:0x54466599 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\b\\x00)\\n\\a\">, #<SNMP::VarBind:0x62a0f667 @name=[*******.4.1.2544.********.1.16], @value=\"Laser-Current Abnormal\">, #<SNMP::VarBind:0x7d9b648e @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x69d2d8ae @value=2>>, #<SNMP::VarBind:0xe5f12a0 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x6b29dec2 @value=2>>, #<SNMP::VarBind:0x4d30683d @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x32d8de9d @value=0>>, #<SNMP::VarBind:0x44641074 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x611e19f4 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x2fa1e46a @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x5bf9eb35 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x3ffaa78e @value=2>>, #<SNMP::VarBind:0x2ebac83c @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421972917, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "13 days, 15:16:01.68", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "d967e13b-ed85-4c70-a4c7-fcefd46367c9", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-07T22:41:28.989Z", "event.kafka.key": null, "event.kafka.offset": 86397, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.corrRef": "", "snmptrap.severity": "4", "snmptrap.event_type": "1", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "5", "snmptrap.summary": "Laser-Current Abnormal", "snmptrap.nmsTime": "\u0007è\u0007\b\u0000)\n\u0007", "snmptrap.comment": "", "snmptrap.name": "LBCLANR", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "admechl11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\b\u0000)\n\u0007", "snmptrap.entity": "CH-1-3-N2", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "735284", "snmptrap.nelpAdress": "************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T11:07:09.354775526Z", "message": "#<SNMP::SNMPv2_Trap:0x19e1e96f @varbind_list=[#<SNMP::VarBind:0x51d2b090 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x1877666d @value=87730259>>, #<SNMP::VarBind:0xecb5e9d @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x41b3ffbc @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x521912a @value=724399>>, #<SNMP::VarBind:0x26c6cbed @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x72ad461e @value=2>>, #<SNMP::VarBind:0x2c3d19e8 @name=[*******.4.1.2544.********.1.3], @value=\"LOS\">, #<SNMP::VarBind:0x251c6584 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x35955b69 @value=2>>, #<SNMP::VarBind:0x2d107673 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x69a6d860 @value=2>>, #<SNMP::VarBind:0x2f50a095 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x3081e805 @value=1>>, #<SNMP::VarBind:0x1e8ca388 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x41b60de1 @value=2>>, #<SNMP::VarBind:0x6bc8494a @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x543655f6 @value=100>>, #<SNMP::VarBind:0x2f5d3c1b @name=[*******.4.1.2544.********.1.9], @value=\"adblock11\">, #<SNMP::VarBind:0x5ef5e514 @name=[*******.4.1.2544.********.1.10], @value=\"***********\">, #<SNMP::VarBind:0x77de7599 @name=[*******.4.1.2544.********.1.11], @value=\"CH-1-10-C\">, #<SNMP::VarBind:0x11211771 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x575a5caa @value=0>>, #<SNMP::VarBind:0x2676ce10 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x220015fa @value=4>>, #<SNMP::VarBind:0x3b030454 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\r\\a\\t\\x06\">, #<SNMP::VarBind:0x3e39b092 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\r\\x06;\\x06\">, #<SNMP::VarBind:0x2e2fef0b @name=[*******.4.1.2544.********.1.16], @value=\"CLEARED: Loss of Signal\">, #<SNMP::VarBind:0xc2290f1 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x2f201485 @value=2>>, #<SNMP::VarBind:0x5cee8cd3 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0xd6867f2 @value=2>>, #<SNMP::VarBind:0x15d5cbb6 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x3a7e61b5 @value=0>>, #<SNMP::VarBind:0x12deea3d @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x5c475863 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x67f4a026 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x2ece16a4 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x4b249e42 @value=2>>, #<SNMP::VarBind:0x2dc65305 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421961029, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 03:41:42.59", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "ebf57858-bf2d-46cc-ad2f-711dffbf3ea5", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T11:07:09.457Z", "event.kafka.key": null, "event.kafka.offset": 81939, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.corrRef": "", "snmptrap.severity": "2", "snmptrap.event_type": "2", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "4", "snmptrap.summary": "CLEARED: Loss of Signal", "snmptrap.nmsTime": "\u0007è\u0007\u0004\r\u0007\t\u0006", "snmptrap.comment": "", "snmptrap.name": "LOS", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adblock11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\r\u0006;\u0006", "snmptrap.entity": "CH-1-10-C", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "724399", "snmptrap.nelpAdress": "***********", "snmptrap.security": "2", "snmptrap.impairment": "1"}, {"@timestamp": "2024-07-07T22:41:29.233889568Z", "message": "#<SNMP::SNMPv2_Trap:0x7abff005 @varbind_list=[#<SNMP::VarBind:0x4e407bcb @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0xc57198 @value=117816203>>, #<SNMP::VarBind:0x57f865d @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0xa3c3462 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x476f98cd @value=735291>>, #<SNMP::VarBind:0x6c8d6809 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x153956ab @value=2>>, #<SNMP::VarBind:0x7ecdef4a @name=[*******.4.1.2544.********.1.3], @value=\"TIFALM\">, #<SNMP::VarBind:0x455fb9ae @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x42155c2c @value=1>>, #<SNMP::VarBind:0x1d46c6cb @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0xce0cd96 @value=5>>, #<SNMP::VarBind:0x34ca976d @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x5c5649e4 @value=2>>, #<SNMP::VarBind:0x770dbc3b @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x7d8b5331 @value=2>>, #<SNMP::VarBind:0x1d1ac212 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x736db13c @value=100>>, #<SNMP::VarBind:0x691b370e @name=[*******.4.1.2544.********.1.9], @value=\"admechl11\">, #<SNMP::VarBind:0x4eaee3c7 @name=[*******.4.1.2544.********.1.10], @value=\"************\">, #<SNMP::VarBind:0x56fb252 @name=[*******.4.1.2544.********.1.11], @value=\"TIFO-1-FCU-1\">, #<SNMP::VarBind:0x1b951792 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x2b06e0a9 @value=0>>, #<SNMP::VarBind:0x128f9ed5 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x6a8a53f4 @value=3>>, #<SNMP::VarBind:0x5990f0b0 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\b\\x00)\\v\\x00\">, #<SNMP::VarBind:0x450f5083 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\b\\x00)\\n\\x00\">, #<SNMP::VarBind:0x2b9807c1 @name=[*******.4.1.2544.********.1.16], @value=\"Telemetry Interface: NE-ALM-CR\">, #<SNMP::VarBind:0x13b5e4b5 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x4d7e8303 @value=2>>, #<SNMP::VarBind:0x724a5967 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x1907fee0 @value=2>>, #<SNMP::VarBind:0x252b3139 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x1c74c804 @value=0>>, #<SNMP::VarBind:0x3cd52b8c @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x1a832208 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x665d6a98 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x5cd3aabf @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x49cf63e2 @value=2>>, #<SNMP::VarBind:0x4adf8bb1 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421972924, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "13 days, 15:16:02.03", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "35078afe-097e-48c4-a1ff-314a3f82f205", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-07T22:41:29.694Z", "event.kafka.key": null, "event.kafka.offset": 86404, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.corrRef": "", "snmptrap.severity": "5", "snmptrap.event_type": "1", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "3", "snmptrap.summary": "Telemetry Interface: NE-ALM-CR", "snmptrap.nmsTime": "\u0007è\u0007\b\u0000)\u000b\u0000", "snmptrap.comment": "", "snmptrap.name": "TIFALM", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "admechl11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\b\u0000)\n\u0000", "snmptrap.entity": "TIFO-1-FCU-1", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "735291", "snmptrap.nelpAdress": "************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T03:34:18.621761943Z", "message": "#<SNMP::SNMPv2_Trap:0x56a50819 @varbind_list=[#<SNMP::VarBind:0x6e799651 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x3d58b372 @value=85013139>>, #<SNMP::VarBind:0x43438100 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x14afd2cb @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x36461511 @value=723416>>, #<SNMP::VarBind:0x4efde388 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x11e930d6 @value=2>>, #<SNMP::VarBind:0x7b3a91aa @name=[*******.4.1.2544.********.1.3], @value=\"CPY-MEM-PRTL\">, #<SNMP::VarBind:0x7d52ed41 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x5c92b0c0 @value=3>>, #<SNMP::VarBind:0x4254404 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x2b3d32eb @value=1>>, #<SNMP::VarBind:0x416ffa28 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x8324675 @value=2>>, #<SNMP::VarBind:0x26193613 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x64f1e251 @value=2>>, #<SNMP::VarBind:0x5af1fa5d @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x40ea1e98 @value=100>>, #<SNMP::VarBind:0x2f2c1138 @name=[*******.4.1.2544.********.1.9], @value=\"adbertr11\">, #<SNMP::VarBind:0x44a49de6 @name=[*******.4.1.2544.********.1.10], @value=\"************\">, #<SNMP::VarBind:0x1152a43d @name=[*******.4.1.2544.********.1.11], @value=\"SRV-UBR\">, #<SNMP::VarBind:0x15c7651 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x4eb61293 @value=0>>, #<SNMP::VarBind:0x2e9ded0e @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x2aeac7ac @value=0>>, #<SNMP::VarBind:0x4866c25e @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x05\\\"\\x12\\x04\">, #<SNMP::VarBind:0x363a734b @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x05\\\"\\x11\\x04\">, #<SNMP::VarBind:0x778e0d1f @name=[*******.4.1.2544.********.1.16], @value=\"Local copy file started\">, #<SNMP::VarBind:0xba563e5 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x752f24fd @value=2>>, #<SNMP::VarBind:0x10eab138 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x178bb0cf @value=2>>, #<SNMP::VarBind:0x37114390 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x3b958143 @value=0>>, #<SNMP::VarBind:0x7e338478 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x34744eb9 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x9c48664 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x1e4c9186 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x7cbed673 @value=2>>, #<SNMP::VarBind:0x6a295edb @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421959955, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "9 days, 20:08:51.39", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "1fc70a2e-5e6c-48d2-a1c2-bfec633e41f2", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T03:34:18.726Z", "event.kafka.key": null, "event.kafka.offset": 81137, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Local copy file started", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0005\"\u0012\u0004", "snmptrap.comment": "", "snmptrap.name": "CPY-MEM-PRTL", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adbertr11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0005\"\u0011\u0004", "snmptrap.entity": "SRV-UBR", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "723416", "snmptrap.nelpAdress": "************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T05:45:12.416629756Z", "message": "#<SNMP::SNMPv2_Trap:0x5046296f @varbind_list=[#<SNMP::VarBind:0x4c8f6247 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x6d9c372b @value=85798533>>, #<SNMP::VarBind:0xc7066a6 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x5a123e28 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x6db8e277 @value=723696>>, #<SNMP::VarBind:0x6111b584 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x6e5baa56 @value=2>>, #<SNMP::VarBind:0x25f33acb @name=[*******.4.1.2544.********.1.3], @value=\"CPY-MEM-PRTL\">, #<SNMP::VarBind:0xf72cf58 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x6323e6a7 @value=3>>, #<SNMP::VarBind:0x5099e98f @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x6c00a435 @value=1>>, #<SNMP::VarBind:0x7591bee2 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x97b7829 @value=2>>, #<SNMP::VarBind:0x16312d18 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x7ff902e7 @value=2>>, #<SNMP::VarBind:0x767d554 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x558f5fa1 @value=100>>, #<SNMP::VarBind:0x4a49e1e5 @name=[*******.4.1.2544.********.1.9], @value=\"admechl11\">, #<SNMP::VarBind:0x7d902c71 @name=[*******.4.1.2544.********.1.10], @value=\"************\">, #<SNMP::VarBind:0x44648be2 @name=[*******.4.1.2544.********.1.11], @value=\"SRV-UBR\">, #<SNMP::VarBind:0x6b39b698 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x6efa24d3 @value=0>>, #<SNMP::VarBind:0x12757ed9 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x72aedcee @value=0>>, #<SNMP::VarBind:0x54053955 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\a-\\f\\x03\">, #<SNMP::VarBind:0x6b6643ba @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\a-\\f\\x03\">, #<SNMP::VarBind:0x525c7bb9 @name=[*******.4.1.2544.********.1.16], @value=\"Local copy file started\">, #<SNMP::VarBind:0x569184b0 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x2ba3d0a6 @value=2>>, #<SNMP::VarBind:0x72812dfb @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x433101a1 @value=2>>, #<SNMP::VarBind:0x195fa973 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x2ccc6c1e @value=0>>, #<SNMP::VarBind:0x57d96289 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x1cae5dda @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x785e7b0d @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x373d5484 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x2b6cb5c0 @value=2>>, #<SNMP::VarBind:0x45426dfa @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421960261, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "9 days, 22:19:45.33", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "65e37acc-080c-4087-a7b5-29919544008b", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T05:45:12.517Z", "event.kafka.key": null, "event.kafka.offset": 81196, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Local copy file started", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0007-\f\u0003", "snmptrap.comment": "", "snmptrap.name": "CPY-MEM-PRTL", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "admechl11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0007-\f\u0003", "snmptrap.entity": "SRV-UBR", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "723696", "snmptrap.nelpAdress": "************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T07:56:06.847931599Z", "message": "#<SNMP::SNMPv2_Trap:0x27d35b43 @varbind_list=[#<SNMP::VarBind:0x5e99852e @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x64e58a3c @value=86583989>>, #<SNMP::VarBind:0x59d1dca0 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x6c2e29f5 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x7e0c71ab @value=723978>>, #<SNMP::VarBind:0x3670a03c @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x61135e8e @value=2>>, #<SNMP::VarBind:0x7cefa2d8 @name=[*******.4.1.2544.********.1.3], @value=\"CPY-MEM-PRTL\">, #<SNMP::VarBind:0x617305c7 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x52313895 @value=3>>, #<SNMP::VarBind:0x29adfb73 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x285565fc @value=1>>, #<SNMP::VarBind:0x51f1000c @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0xadf7f37 @value=2>>, #<SNMP::VarBind:0x4cef6b7c @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x4f169d98 @value=2>>, #<SNMP::VarBind:0x16be1516 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x6769e631 @value=100>>, #<SNMP::VarBind:0x50ead52b @name=[*******.4.1.2544.********.1.9], @value=\"admechl21\">, #<SNMP::VarBind:0x2b6b78f3 @name=[*******.4.1.2544.********.1.10], @value=\"************\">, #<SNMP::VarBind:0x4d1bce2d @name=[*******.4.1.2544.********.1.11], @value=\"SRV-UBR\">, #<SNMP::VarBind:0x656bf1d1 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x632e33da @value=0>>, #<SNMP::VarBind:0x13f273ac @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0xbbd59 @value=0>>, #<SNMP::VarBind:0x3ad431f0 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\t8\\x06\\t\">, #<SNMP::VarBind:0x28767593 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\t8\\x06\\t\">, #<SNMP::VarBind:0x28991c22 @name=[*******.4.1.2544.********.1.16], @value=\"Local copy file started\">, #<SNMP::VarBind:0x75a58d1d @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x6ee579e8 @value=2>>, #<SNMP::VarBind:0x7524534a @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x15bc39c0 @value=2>>, #<SNMP::VarBind:0x4ca8930f @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x947994b @value=0>>, #<SNMP::VarBind:0x5e2e252a @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x2aa87516 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x2210aba7 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x17a8f36f @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x380093c5 @value=2>>, #<SNMP::VarBind:0x51695116 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421960569, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 00:30:39.89", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "70734656-fb96-4ce9-840c-628b9b941d85", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T07:56:06.948Z", "event.kafka.key": null, "event.kafka.offset": 81793, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Local copy file started", "snmptrap.nmsTime": "\u0007è\u0007\u0004\t8\u0006\t", "snmptrap.comment": "", "snmptrap.name": "CPY-MEM-PRTL", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "admechl21", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\t8\u0006\t", "snmptrap.entity": "SRV-UBR", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "723978", "snmptrap.nelpAdress": "************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T12:17:56.255167139Z", "message": "#<SNMP::SNMPv2_Trap:0x2ed25966 @varbind_list=[#<SNMP::VarBind:0x2cd17595 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x19a35f6f @value=88154902>>, #<SNMP::VarBind:0x25063c54 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x1931538c @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x3eebf0e2 @value=724550>>, #<SNMP::VarBind:0x3d309877 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x29c0e719 @value=2>>, #<SNMP::VarBind:0x3afe7c9e @name=[*******.4.1.2544.********.1.3], @value=\"CPY-MEM-PRTL\">, #<SNMP::VarBind:0x5b73e7c3 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x62393ffa @value=3>>, #<SNMP::VarBind:0x4ceff48b @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x5bc7b04 @value=1>>, #<SNMP::VarBind:0x4135d98f @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x6ad8f8bc @value=2>>, #<SNMP::VarBind:0x592012c0 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x3f2236bb @value=2>>, #<SNMP::VarBind:0x50347971 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x7d8476d3 @value=100>>, #<SNMP::VarBind:0x24ebcdb1 @name=[*******.4.1.2544.********.1.9], @value=\"adblock12\">, #<SNMP::VarBind:0xad2b079 @name=[*******.4.1.2544.********.1.10], @value=\"***********\">, #<SNMP::VarBind:0x79deafa3 @name=[*******.4.1.2544.********.1.11], @value=\"SRV-UBR\">, #<SNMP::VarBind:0x27327ca5 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x5dceabb7 @value=0>>, #<SNMP::VarBind:0x4280990f @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x4eaf954b @value=0>>, #<SNMP::VarBind:0x7a4c1f6f @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x0E\\x118\\x00\">, #<SNMP::VarBind:0x52779082 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x0E\\x118\\x00\">, #<SNMP::VarBind:0x14f92322 @name=[*******.4.1.2544.********.1.16], @value=\"Local copy file started\">, #<SNMP::VarBind:0x26cee93b @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x3c633dea @value=2>>, #<SNMP::VarBind:0x52fe4964 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0xfc3902a @value=2>>, #<SNMP::VarBind:0x5e1537d8 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x7bbccd9d @value=0>>, #<SNMP::VarBind:0x5ba90ee7 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x48788778 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x3fa4c258 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x22692014 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x31a4dc88 @value=2>>, #<SNMP::VarBind:0x3debce83 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421961194, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 04:52:29.02", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "371d1e1b-f502-4c07-bc61-9a6ff35eb3e9", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T12:17:56.355Z", "event.kafka.key": null, "event.kafka.offset": 81572, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Local copy file started", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u000e\u00118\u0000", "snmptrap.comment": "", "snmptrap.name": "CPY-MEM-PRTL", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adblock12", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u000e\u00118\u0000", "snmptrap.entity": "SRV-UBR", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "724550", "snmptrap.nelpAdress": "***********", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T15:00:21.889079549Z", "message": "#<SNMP::SNMPv2_Trap:0x36a1b8f4 @varbind_list=[#<SNMP::VarBind:0x720b4277 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x5a5b203a @value=89129479>>, #<SNMP::VarBind:0x6106459d @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x17f24a40 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x3f42aa33 @value=724900>>, #<SNMP::VarBind:0x3c1b1711 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x6fe287a2 @value=2>>, #<SNMP::VarBind:0x6ac76054 @name=[*******.4.1.2544.********.1.3], @value=\"T-ES-FEC-HT__1DAY\">, #<SNMP::VarBind:0x407e5c0d @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x24b9688c @value=3>>, #<SNMP::VarBind:0x5f1d242e @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0xe092a59 @value=1>>, #<SNMP::VarBind:0x3c2591a1 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x533763a1 @value=2>>, #<SNMP::VarBind:0x65bd11bd @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x2221a4b1 @value=2>>, #<SNMP::VarBind:0x329981cd @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x5adbd654 @value=100>>, #<SNMP::VarBind:0x5766044b @name=[*******.4.1.2544.********.1.9], @value=\"adantbe11\">, #<SNMP::VarBind:0x78cd263a @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x6244707 @name=[*******.4.1.2544.********.1.11], @value=\"CH-1-9-NE\">, #<SNMP::VarBind:0x24948e1 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x656a40bf @value=0>>, #<SNMP::VarBind:0x740756de @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x50bf3b26 @value=0>>, #<SNMP::VarBind:0x18796f90 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x11\\x00\\x15\\b\">, #<SNMP::VarBind:0x2fa1bb98 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x11\\x00\\x15\\b\">, #<SNMP::VarBind:0x615eb914 @name=[*******.4.1.2544.********.1.16], @value=\"Errored Seconds FEC OTU Monitoring\">, #<SNMP::VarBind:0x4811c812 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x1bc73b6f @value=2>>, #<SNMP::VarBind:0x39183ef4 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x2d9aa6da @value=2>>, #<SNMP::VarBind:0x6b8dba23 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x49b3f87e @value=0>>, #<SNMP::VarBind:0x31249022 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x49f05caa @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x4f2abb15 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x190cca86 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0xc3cec05 @value=2>>, #<SNMP::VarBind:0x4762e424 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421961576, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 07:34:54.79", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "fd3f3b6a-63a7-4a66-8557-e8a3c6c16f40", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T15:00:21.989Z", "event.kafka.key": null, "event.kafka.offset": 82604, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Errored Seconds FEC OTU Monitoring", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0011\u0000\u0015\b", "snmptrap.comment": "", "snmptrap.name": "T-ES-FEC-HT__1DAY", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adantbe11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0011\u0000\u0015\b", "snmptrap.entity": "CH-1-9-NE", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "724900", "snmptrap.nelpAdress": "*************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T16:40:14.753344364Z", "message": "#<SNMP::SNMPv2_Trap:0x7e6ec1f @varbind_list=[#<SNMP::VarBind:0x49ec30e2 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x79cbd8e @value=89728775>>, #<SNMP::VarBind:0x372a2f76 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x1134ffb9 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x3977a65d @value=725109>>, #<SNMP::VarBind:0x5be50c30 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x3dd2c9a5 @value=2>>, #<SNMP::VarBind:0xb2d3091 @name=[*******.4.1.2544.********.1.3], @value=\"DCN\">, #<SNMP::VarBind:0xf6c3ae7 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x7c9b33b6 @value=1>>, #<SNMP::VarBind:0x57af4261 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x356449bd @value=3>>, #<SNMP::VarBind:0x5bb1e6f4 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x7e1278b7 @value=2>>, #<SNMP::VarBind:0x5303815e @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x44bd1322 @value=2>>, #<SNMP::VarBind:0x106fdb07 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x7d55ce7d @value=100>>, #<SNMP::VarBind:0x3293a9f8 @name=[*******.4.1.2544.********.1.9], @value=\"admonce11\">, #<SNMP::VarBind:0x1e1b888b @name=[*******.4.1.2544.********.1.10], @value=\"**************\">, #<SNMP::VarBind:0x70c2cfac @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x23b7d1ef @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0xcc47008 @value=0>>, #<SNMP::VarBind:0x4ed9fecb @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x2880e713 @value=0>>, #<SNMP::VarBind:0x35a56345 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x12(\\x0E\\b\">, #<SNMP::VarBind:0x1ace01c9 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x12(\\x0E\\b\">, #<SNMP::VarBind:0x1b609bfd @name=[*******.4.1.2544.********.1.16], @value=\"Network element does not respond to SNMP requests\">, #<SNMP::VarBind:0x34775ffd @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x2a56bd7d @value=2>>, #<SNMP::VarBind:0x407c31b7 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x5fdce536 @value=2>>, #<SNMP::VarBind:0x515ac187 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x4e783384 @value=0>>, #<SNMP::VarBind:0x6fa84275 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x5765efe3 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x50344a71 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x5f5fb7e0 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x72c9fd94 @value=2>>, #<SNMP::VarBind:0xaab0ed9 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421961805, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 09:14:47.75", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "b938df88-b1f1-49d2-9094-0cd6f177b905", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T16:40:14.860Z", "event.kafka.key": null, "event.kafka.offset": 82682, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 1, "event.logstash.instance_name": "iictniapls015", "snmptrap.corrRef": "", "snmptrap.severity": "3", "snmptrap.event_type": "1", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Network element does not respond to SNMP requests", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0012(\u000e\b", "snmptrap.comment": "", "snmptrap.name": "DCN", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "admonce11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0012(\u000e\b", "snmptrap.entity": "", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "725109", "snmptrap.nelpAdress": "**************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T18:51:09.168989967Z", "message": "#<SNMP::SNMPv2_Trap:0x46b67b29 @varbind_list=[#<SNMP::VarBind:0x4f86bec0 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x4e28667a @value=90514230>>, #<SNMP::VarBind:0x3b3233a5 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x344cdf53 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x4ec617a4 @value=725386>>, #<SNMP::VarBind:0x43d0a3b0 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x69d50f74 @value=2>>, #<SNMP::VarBind:0x3b31f35a @name=[*******.4.1.2544.********.1.3], @value=\"DCN\">, #<SNMP::VarBind:0x45b4b8af @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x33f4e8a @value=1>>, #<SNMP::VarBind:0x42505f36 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x764fdf05 @value=3>>, #<SNMP::VarBind:0x165e70a3 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x6f6c37fc @value=2>>, #<SNMP::VarBind:0x2f056cc8 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x273a29da @value=2>>, #<SNMP::VarBind:0x42848ef6 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x4cd9928d @value=100>>, #<SNMP::VarBind:0x48bed4f2 @name=[*******.4.1.2544.********.1.9], @value=\"admonce12\">, #<SNMP::VarBind:0x3ebc50c6 @name=[*******.4.1.2544.********.1.10], @value=\"**************\">, #<SNMP::VarBind:0x58a99ad9 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x586436bb @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x66c36001 @value=0>>, #<SNMP::VarBind:0x3a25267f @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x2ece0cb0 @value=0>>, #<SNMP::VarBind:0x5766263c @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x143\\t\\x03\">, #<SNMP::VarBind:0x2a5897b9 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x143\\t\\x03\">, #<SNMP::VarBind:0x395d5a53 @name=[*******.4.1.2544.********.1.16], @value=\"Network element does not respond to SNMP requests\">, #<SNMP::VarBind:0x67287a44 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x199f75b @value=2>>, #<SNMP::VarBind:0x6f924d8e @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x3cce1ded @value=2>>, #<SNMP::VarBind:0x1890ba6a @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x567da50c @value=0>>, #<SNMP::VarBind:0x2bfe6c70 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x67933d97 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x7acd98e5 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x41230775 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x19666ecd @value=2>>, #<SNMP::VarBind:0x67e797f3 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421962108, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 11:25:42.30", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "5a49e47b-5b8c-434b-9736-860b4d3f9c56", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T18:51:09.275Z", "event.kafka.key": null, "event.kafka.offset": 81958, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "3", "snmptrap.event_type": "1", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Network element does not respond to SNMP requests", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u00143\t\u0003", "snmptrap.comment": "", "snmptrap.name": "DCN", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "admonce12", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u00143\t\u0003", "snmptrap.entity": "", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "725386", "snmptrap.nelpAdress": "**************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-04T21:01:35.279168075Z", "message": "#<SNMP::SNMPv2_Trap:0x5d99c3f6 @varbind_list=[#<SNMP::VarBind:0x1fb4d944 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x55974e6b @value=91296801>>, #<SNMP::VarBind:0x4682b15c @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0xc8807fa @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x354f81d6 @value=725661>>, #<SNMP::VarBind:0x5a446502 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x99f3747 @value=2>>, #<SNMP::VarBind:0x45644659 @name=[*******.4.1.2544.********.1.3], @value=\"CPY-MEM-PRTL\">, #<SNMP::VarBind:0xcec2e12 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x75f936f8 @value=3>>, #<SNMP::VarBind:0x5ac120f @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x621dca6d @value=1>>, #<SNMP::VarBind:0xfecf482 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x331e5d71 @value=2>>, #<SNMP::VarBind:0x56470d77 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x5a05eb70 @value=2>>, #<SNMP::VarBind:0x32a54c56 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x73917aa6 @value=100>>, #<SNMP::VarBind:0x1dd3ed16 @name=[*******.4.1.2544.********.1.9], @value=\"adhasse11\">, #<SNMP::VarBind:0x68f8dbb7 @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x70a7ed02 @name=[*******.4.1.2544.********.1.11], @value=\"SRV-UBR\">, #<SNMP::VarBind:0x81bfdfa @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0xa221ab2 @value=0>>, #<SNMP::VarBind:0x24a94d22 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x6ba97b48 @value=0>>, #<SNMP::VarBind:0x3c8abfea @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x17\\x01#\\x00\">, #<SNMP::VarBind:0x26e7c543 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x17\\x01\\\"\\x00\">, #<SNMP::VarBind:0x48494ce0 @name=[*******.4.1.2544.********.1.16], @value=\"Local copy file started\">, #<SNMP::VarBind:0x689a1d9a @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x1868a8c @value=2>>, #<SNMP::VarBind:0x19d39a22 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x1b7febb4 @value=2>>, #<SNMP::VarBind:0x23b1538c @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x16567f9c @value=0>>, #<SNMP::VarBind:0x1351e5fe @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x77979e6b @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x5e938e26 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x7b1cb0f7 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x677ed47f @value=2>>, #<SNMP::VarBind:0x2cd17a3d @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421962410, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 13:36:08.01", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "94bb86c6-3b5e-4431-8fba-512f8ce07992", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T21:01:35.387Z", "event.kafka.key": null, "event.kafka.offset": 82084, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Local copy file started", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0017\u0001#\u0000", "snmptrap.comment": "", "snmptrap.name": "CPY-MEM-PRTL", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adhasse11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0017\u0001\"\u0000", "snmptrap.entity": "SRV-UBR", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "725661", "snmptrap.nelpAdress": "*************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-05T01:23:23.310656281Z", "message": "#<SNMP::SNMPv2_Trap:0x12e7b576 @varbind_list=[#<SNMP::VarBind:0x14b9b8ab @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x656bb50 @value=92867631>>, #<SNMP::VarBind:0x2935e2e9 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x5707721 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x5cd5fbc4 @value=726234>>, #<SNMP::VarBind:0x6f6a4735 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x694ec29b @value=2>>, #<SNMP::VarBind:0x6f8179ec @name=[*******.4.1.2544.********.1.3], @value=\"CPY-MEM-PRTL\">, #<SNMP::VarBind:0x1c79a403 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x21170b56 @value=3>>, #<SNMP::VarBind:0x3e525ff5 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x5a781fca @value=1>>, #<SNMP::VarBind:0xb70e13a @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x122406b9 @value=2>>, #<SNMP::VarBind:0x3ea54eff @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x243990ae @value=2>>, #<SNMP::VarBind:0x284ccc22 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x1aef065c @value=100>>, #<SNMP::VarBind:0xea96148 @name=[*******.4.1.2544.********.1.9], @value=\"adnamur11\">, #<SNMP::VarBind:0x667d2cd3 @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x289ec1e2 @name=[*******.4.1.2544.********.1.11], @value=\"SRV-UBR\">, #<SNMP::VarBind:0x4a21ff4b @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0xf477466 @value=0>>, #<SNMP::VarBind:0x53cae9 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x5e1c9ad0 @value=0>>, #<SNMP::VarBind:0x1b82edff @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x05\\x03\\x17\\x17\\x03\">, #<SNMP::VarBind:0x2fd24ead @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x05\\x03\\x17\\x17\\x03\">, #<SNMP::VarBind:0x31c8dd3c @name=[*******.4.1.2544.********.1.16], @value=\"Local copy file started\">, #<SNMP::VarBind:0xbb81bcf @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x759e252 @value=2>>, #<SNMP::VarBind:0x3ac36332 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x4d15786b @value=2>>, #<SNMP::VarBind:0x6b86465 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x4e3b5d92 @value=0>>, #<SNMP::VarBind:0x6fc06d94 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x9708627 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x4c2e4020 @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x58ed53fa @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x34ad39be @value=2>>, #<SNMP::VarBind:0x39e1d0b2 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421963035, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "10 days, 17:57:56.31", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "e02692e8-7f6d-4bb8-9a7f-0457886c82a7", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-05T01:23:23.415Z", "event.kafka.key": null, "event.kafka.offset": 82418, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "3", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Local copy file started", "snmptrap.nmsTime": "\u0007è\u0007\u0005\u0003\u0017\u0017\u0003", "snmptrap.comment": "", "snmptrap.name": "CPY-MEM-PRTL", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "adnamur11", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0005\u0003\u0017\u0017\u0003", "snmptrap.entity": "SRV-UBR", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "726234", "snmptrap.nelpAdress": "*************", "snmptrap.security": "2", "snmptrap.impairment": "2"}, {"@timestamp": "2024-07-07T22:41:29.425209932Z", "message": "#<SNMP::SNMPv2_Trap:0x7d7e7b53 @varbind_list=[#<SNMP::VarBind:0x20df42e @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x4e989cf1 @value=117816223>>, #<SNMP::VarBind:0x40b2d292 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x5dd2bdbe @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x30139eae @value=735314>>, #<SNMP::VarBind:0x3ae6f349 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x6c9680c5 @value=2>>, #<SNMP::VarBind:0x2461efa4 @name=[*******.4.1.2544.********.1.3], @value=\"SERV-C-AFFECTED\">, #<SNMP::VarBind:0x82be368 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x5da359f @value=1>>, #<SNMP::VarBind:0x5d120d49 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x7625d580 @value=2>>, #<SNMP::VarBind:0x21c2488f @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x7560e7e5 @value=1>>, #<SNMP::VarBind:0x38d5d445 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x60049f0a @value=2>>, #<SNMP::VarBind:0x533135b5 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x211caff9 @value=9999>>, #<SNMP::VarBind:0x377d2063 @name=[*******.4.1.2544.********.1.9], @value=\"\">, #<SNMP::VarBind:0x73451549 @name=[*******.4.1.2544.********.1.10], @value=\"\">, #<SNMP::VarBind:0x29030a09 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x6da40bb7 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x33045058 @value=0>>, #<SNMP::VarBind:0x6830e42c @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x5952e861 @value=0>>, #<SNMP::VarBind:0x1466b94f @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\b\\x00)\\v\\x02\">, #<SNMP::VarBind:0x5f1ad0b @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\b\\x00)\\v\\x02\">, #<SNMP::VarBind:0x6a8f381d @name=[*******.4.1.2544.********.1.16], @value=\"Client Affected\">, #<SNMP::VarBind:0x24d0c4cd @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x55dadd9 @value=2>>, #<SNMP::VarBind:0xd4cc18c @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x1e104fe8 @value=2>>, #<SNMP::VarBind:0x3c44064b @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x549f577f @value=0>>, #<SNMP::VarBind:0x3f34acc1 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x2347cbc0 @name=[*******.4.1.2544.********.1.21], @value=\"FC8G FOID 16971 TRID 11435 Freq 19540 on DWDM 17075\">, #<SNMP::VarBind:0x3f2de7bd @name=[*******.4.1.2544.********.1.22], @value=\"DWDM ID 17075 L25N K-2 <> Muizen\">, #<SNMP::VarBind:0x11276b88 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0xbef9fc9 @value=2>>, #<SNMP::VarBind:0x16eb5cbf @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421972949, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "13 days, 15:16:02.23", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "b20532df-3654-44b7-840e-b59da806ccc5", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-07T22:41:29.695Z", "event.kafka.key": null, "event.kafka.offset": 85662, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.corrRef": "", "snmptrap.severity": "2", "snmptrap.event_type": "1", "snmptrap.update": "2", "snmptrap.neType": "9999", "snmptrap.direction": "0", "snmptrap.summary": "Client Affected", "snmptrap.nmsTime": "\u0007è\u0007\b\u0000)\u000b\u0002", "snmptrap.comment": "", "snmptrap.name": "SERV-C-AFFECTED", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "", "snmptrap.customerName": "DWDM ID 17075 L25N K-2 <> Muizen", "snmptrap.neTime": "\u0007è\u0007\b\u0000)\u000b\u0002", "snmptrap.entity": "", "snmptrap.mtosiNeType": "FC8G FOID 16971 TRID 11435 Freq 19540 on DWDM 17075", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "735314", "snmptrap.nelpAdress": "", "snmptrap.security": "2", "snmptrap.impairment": "1"}, {"@timestamp": "2024-07-07T22:41:29.399364113Z", "message": "#<SNMP::SNMPv2_Trap:0x71d98e61 @varbind_list=[#<SNMP::VarBind:0x1dd34e8 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x4a1b238c @value=117816220>>, #<SNMP::VarBind:0x770c0628 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x246edd3a @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x4cfcd72b @value=0>>, #<SNMP::VarBind:0x5f688a7b @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x48c2faf8 @value=2>>, #<SNMP::VarBind:0x1a1769d7 @name=[*******.4.1.2544.********.1.3], @value=\"SERV_FAULTED_C_AFFECTED\">, #<SNMP::VarBind:0x3078b031 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x2b58cf17 @value=5>>, #<SNMP::VarBind:0x7492f5be @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x55ea732a @value=1>>, #<SNMP::VarBind:0x2066b89e @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x4d2bebf2 @value=1>>, #<SNMP::VarBind:0x2ef7d8ec @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x28829d3b @value=2>>, #<SNMP::VarBind:0x3d3562fd @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x2f537ad2 @value=9999>>, #<SNMP::VarBind:0x3f73e76c @name=[*******.4.1.2544.********.1.9], @value=\"HICTAIAPWV014\">, #<SNMP::VarBind:0x3a4defc @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x159076ff @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x21211b75 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x149d0ba @value=0>>, #<SNMP::VarBind:0x243fbd51 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x328e10a7 @value=0>>, #<SNMP::VarBind:0x1b5d4f30 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\b\\x00)\\v\\x02\">, #<SNMP::VarBind:0x54de90e1 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\b\\x00)\\v\\x02\">, #<SNMP::VarBind:0xdd26a1e @name=[*******.4.1.2544.********.1.16], @value=\"Service Faulted - Client Affected\">, #<SNMP::VarBind:0x4f1b482b @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x3e4eba37 @value=2>>, #<SNMP::VarBind:0x1e376c87 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x2b67c5 @value=2>>, #<SNMP::VarBind:0x15642dfb @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x5e30b262 @value=0>>, #<SNMP::VarBind:0x7b46ae77 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x2008eb95 @name=[*******.4.1.2544.********.1.21], @value=\"FC8G FOID 16971 TRID 11435 Freq 19540 on DWDM 17075\">, #<SNMP::VarBind:0x1437019a @name=[*******.4.1.2544.********.1.22], @value=\"DWDM ID 17075 L25N K-2 <> Muizen\">, #<SNMP::VarBind:0x4fcf12dc @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x1342844 @value=2>>, #<SNMP::VarBind:0x66873fb4 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421972948, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "13 days, 15:16:02.20", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "593f3e75-b9ce-48e6-960e-75c470777b25", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-07T22:41:29.695Z", "event.kafka.key": null, "event.kafka.offset": 85661, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "5", "snmptrap.update": "2", "snmptrap.neType": "9999", "snmptrap.direction": "0", "snmptrap.summary": "Service Faulted - Client Affected", "snmptrap.nmsTime": "\u0007è\u0007\b\u0000)\u000b\u0002", "snmptrap.comment": "", "snmptrap.name": "SERV_FAULTED_C_AFFECTED", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "HICTAIAPWV014", "snmptrap.customerName": "DWDM ID 17075 L25N K-2 <> Muizen", "snmptrap.neTime": "\u0007è\u0007\b\u0000)\u000b\u0002", "snmptrap.entity": "", "snmptrap.mtosiNeType": "FC8G FOID 16971 TRID 11435 Freq 19540 on DWDM 17075", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "0", "snmptrap.nelpAdress": "*************", "snmptrap.security": "2", "snmptrap.impairment": "1"}, {"@timestamp": "2024-07-07T22:41:29.476889373Z", "message": "#<SNMP::SNMPv2_Trap:0x6cfa19a6 @varbind_list=[#<SNMP::VarBind:0x57de6b5f @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x4b6785a5 @value=117816228>>, #<SNMP::VarBind:0x776c9246 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0xa9c6b76 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x2e3c6de9 @value=0>>, #<SNMP::VarBind:0x59019f26 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x4aecd8e2 @value=2>>, #<SNMP::VarBind:0x2342dd12 @name=[*******.4.1.2544.********.1.3], @value=\"SERV_FAULTED_C_AFFECTED\">, #<SNMP::VarBind:0x6e5cbfb6 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x3b71f5f4 @value=5>>, #<SNMP::VarBind:0x17f611b0 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x22e9e411 @value=1>>, #<SNMP::VarBind:0x29434ef5 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x1d29c07c @value=1>>, #<SNMP::VarBind:0x3a7539f8 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x57d18bcb @value=2>>, #<SNMP::VarBind:0x64d64322 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x372f55e4 @value=9999>>, #<SNMP::VarBind:0x24861000 @name=[*******.4.1.2544.********.1.9], @value=\"HICTAIAPWV014\">, #<SNMP::VarBind:0x7f524888 @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x29cbdb13 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x64062bbb @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x7014312b @value=0>>, #<SNMP::VarBind:0x1def21a9 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x6a8b6244 @value=0>>, #<SNMP::VarBind:0x3f85d75d @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\b\\x00)\\v\\x03\">, #<SNMP::VarBind:0x6ec934ef @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\b\\x00)\\v\\x03\">, #<SNMP::VarBind:0xacfb2f6 @name=[*******.4.1.2544.********.1.16], @value=\"Service Faulted - Client Affected\">, #<SNMP::VarBind:0x3be778a9 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x2a497fb7 @value=2>>, #<SNMP::VarBind:0xd00485d @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x7aaf0309 @value=2>>, #<SNMP::VarBind:0x7a8d1ec5 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x1bd862e4 @value=0>>, #<SNMP::VarBind:0x14159362 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x16a06da2 @name=[*******.4.1.2544.********.1.21], @value=\"FC8G FOID 18201 TRID 11431 Freq 19580 on DWDM 17074\">, #<SNMP::VarBind:0x4c92285 @name=[*******.4.1.2544.********.1.22], @value=\"DWDM ID 17074 L27 K-2 <> Muizen\">, #<SNMP::VarBind:0x5c5eafba @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x6468a9d0 @value=2>>, #<SNMP::VarBind:0x71b00582 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421972950, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "13 days, 15:16:02.28", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "f6b2e61d-2c65-4868-be68-3b9ca47b265e", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-07T22:41:29.695Z", "event.kafka.key": null, "event.kafka.offset": 85663, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "5", "snmptrap.update": "2", "snmptrap.neType": "9999", "snmptrap.direction": "0", "snmptrap.summary": "Service Faulted - Client Affected", "snmptrap.nmsTime": "\u0007è\u0007\b\u0000)\u000b\u0003", "snmptrap.comment": "", "snmptrap.name": "SERV_FAULTED_C_AFFECTED", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "HICTAIAPWV014", "snmptrap.customerName": "DWDM ID 17074 L27 K-2 <> Muizen", "snmptrap.neTime": "\u0007è\u0007\b\u0000)\u000b\u0003", "snmptrap.entity": "", "snmptrap.mtosiNeType": "FC8G FOID 18201 TRID 11431 Freq 19580 on DWDM 17074", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "0", "snmptrap.nelpAdress": "*************", "snmptrap.security": "2", "snmptrap.impairment": "1"}, {"@timestamp": "2024-07-07T22:41:29.316874065Z", "message": "#<SNMP::SNMPv2_Trap:0xe7e8a08 @varbind_list=[#<SNMP::VarBind:0x1d06dc15 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x4689bf66 @value=117816212>>, #<SNMP::VarBind:0x3c5d5375 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x3f0ab4a9 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x14c73129 @value=0>>, #<SNMP::VarBind:0x1fbede58 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x492ef021 @value=2>>, #<SNMP::VarBind:0x236bba51 @name=[*******.4.1.2544.********.1.3], @value=\"SERV_FAULTED_N_AFFECTED\">, #<SNMP::VarBind:0xe88896c @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x67adcafb @value=5>>, #<SNMP::VarBind:0x59bc81d7 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x277b0183 @value=1>>, #<SNMP::VarBind:0x6d722fe9 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x6401daae @value=1>>, #<SNMP::VarBind:0x590e5dc0 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x2686287b @value=2>>, #<SNMP::VarBind:0x5af74625 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x3049337f @value=9999>>, #<SNMP::VarBind:0x1b0288eb @name=[*******.4.1.2544.********.1.9], @value=\"HICTAIAPWV014\">, #<SNMP::VarBind:0xcd0ae00 @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x6cf617d1 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x155d03fb @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x1a97946b @value=0>>, #<SNMP::VarBind:0x133e8997 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x4faa4cbe @value=0>>, #<SNMP::VarBind:0x3215dc6b @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\b\\x00)\\v\\x01\">, #<SNMP::VarBind:0x1407328e @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\b\\x00)\\v\\x01\">, #<SNMP::VarBind:0x731263a8 @name=[*******.4.1.2544.********.1.16], @value=\"Service Faulted - Network Affected\">, #<SNMP::VarBind:0x6ec00ec8 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x722a61ff @value=2>>, #<SNMP::VarBind:0x5f14ae76 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x3f7b891f @value=2>>, #<SNMP::VarBind:0x51237676 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x70a7d034 @value=0>>, #<SNMP::VarBind:0xa3aa748 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x69eaf461 @name=[*******.4.1.2544.********.1.21], @value=\"FC8G FOID 18197 TRID 11427 Freq 19580 on DWDM 17075\">, #<SNMP::VarBind:0x23b0afec @name=[*******.4.1.2544.********.1.22], @value=\"DWDM ID 17075 L25N K-2 <> Muizen\">, #<SNMP::VarBind:0x6dd8edde @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x328717ca @value=2>>, #<SNMP::VarBind:0xcf0b407 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421972946, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "13 days, 15:16:02.12", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "d657be2e-033d-424c-ac04-94f800f710e2", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-07T22:41:29.695Z", "event.kafka.key": null, "event.kafka.offset": 85749, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "5", "snmptrap.update": "2", "snmptrap.neType": "9999", "snmptrap.direction": "0", "snmptrap.summary": "Service Faulted - Network Affected", "snmptrap.nmsTime": "\u0007è\u0007\b\u0000)\u000b\u0001", "snmptrap.comment": "", "snmptrap.name": "SERV_FAULTED_N_AFFECTED", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "HICTAIAPWV014", "snmptrap.customerName": "DWDM ID 17075 L25N K-2 <> Muizen", "snmptrap.neTime": "\u0007è\u0007\b\u0000)\u000b\u0001", "snmptrap.entity": "", "snmptrap.mtosiNeType": "FC8G FOID 18197 TRID 11427 Freq 19580 on DWDM 17075", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "0", "snmptrap.nelpAdress": "*************", "snmptrap.security": "2", "snmptrap.impairment": "1"}, {"@timestamp": "2024-07-07T22:41:41.373385604Z", "message": "#<SNMP::SNMPv2_Trap:0x18b82a8 @varbind_list=[#<SNMP::VarBind:0x6dfb090a @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x71a5b68 @value=117817418>>, #<SNMP::VarBind:0x5ad917e1 @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x20f99eb5 @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x157f60f9 @value=0>>, #<SNMP::VarBind:0x40eba825 @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x529fc9c6 @value=2>>, #<SNMP::VarBind:0x3effe916 @name=[*******.4.1.2544.********.1.3], @value=\"SERV_FAULTED_N_AFFECTED\">, #<SNMP::VarBind:0x50e92e98 @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0x4dde1e20 @value=5>>, #<SNMP::VarBind:0x61233fa4 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x20d5ddcf @value=1>>, #<SNMP::VarBind:0x39d1aaa3 @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x4438ca7 @value=1>>, #<SNMP::VarBind:0x7ce54d94 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x7250dc62 @value=2>>, #<SNMP::VarBind:0x46e0f439 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x5ede7ef6 @value=9999>>, #<SNMP::VarBind:0x8010154 @name=[*******.4.1.2544.********.1.9], @value=\"HICTAIAPWV014\">, #<SNMP::VarBind:0x7cc66ffa @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x3d460d67 @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x1042edf7 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x10c9450a @value=0>>, #<SNMP::VarBind:0x5fbf5156 @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x529f42f3 @value=0>>, #<SNMP::VarBind:0x55b53002 @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\b\\x00)\\f\\x02\">, #<SNMP::VarBind:0xf8eceab @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\b\\x00)\\f\\x02\">, #<SNMP::VarBind:0x2a1e0bed @name=[*******.4.1.2544.********.1.16], @value=\"Service Faulted - Network Affected\">, #<SNMP::VarBind:0x23e5c87 @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x7ef759e2 @value=2>>, #<SNMP::VarBind:0x7f53b01 @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x5d6e0971 @value=2>>, #<SNMP::VarBind:0x2dc4bc3f @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x6938c82c @value=0>>, #<SNMP::VarBind:0x473bcb39 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x6a33772e @name=[*******.4.1.2544.********.1.21], @value=\"FC8G FOID 16975 TRID 11439 Freq 19540 on DWDM 17074\">, #<SNMP::VarBind:0x621e0b13 @name=[*******.4.1.2544.********.1.22], @value=\"DWDM ID 17074 L27 K-2 <> Muizen\">, #<SNMP::VarBind:0x4d48c6d4 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x5433be47 @value=2>>, #<SNMP::VarBind:0x25c829e8 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421972961, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "13 days, 15:16:14.18", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "06478bb2-4935-493a-a167-f816707072c4", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-07T22:41:41.491Z", "event.kafka.key": null, "event.kafka.offset": 85674, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 0, "event.logstash.instance_name": "iictmiapls016", "snmptrap.corrRef": "", "snmptrap.severity": "1", "snmptrap.event_type": "5", "snmptrap.update": "2", "snmptrap.neType": "9999", "snmptrap.direction": "0", "snmptrap.summary": "Service Faulted - Network Affected", "snmptrap.nmsTime": "\u0007è\u0007\b\u0000)\f\u0002", "snmptrap.comment": "", "snmptrap.name": "SERV_FAULTED_N_AFFECTED", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "HICTAIAPWV014", "snmptrap.customerName": "DWDM ID 17074 L27 K-2 <> Muizen", "snmptrap.neTime": "\u0007è\u0007\b\u0000)\f\u0002", "snmptrap.entity": "", "snmptrap.mtosiNeType": "FC8G FOID 16975 TRID 11439 Freq 19540 on DWDM 17074", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "0", "snmptrap.nelpAdress": "*************", "snmptrap.security": "2", "snmptrap.impairment": "1"}, {"@timestamp": "2024-07-04T03:34:17.660784014Z", "message": "#<SNMP::SNMPv2_Trap:0x7d8f5f63 @varbind_list=[#<SNMP::VarBind:0x73137ae4 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x62774da6 @value=85013044>>, #<SNMP::VarBind:0x650462cc @name=[*******.*******.4.1.0], @value=[*******.4.1.2544.********.0.1]>, #<SNMP::VarBind:0x3736139d @name=[*******.4.1.2544.********.1.1], @value=#<SNMP::Integer:0x5f1a8bc5 @value=723415>>, #<SNMP::VarBind:0x758f7fd @name=[*******.4.1.2544.********.1.2], @value=#<SNMP::Integer:0x418cf7d8 @value=2>>, #<SNMP::VarBind:0x46a1ae8f @name=[*******.4.1.2544.********.1.3], @value=\"S-NEBAK\">, #<SNMP::VarBind:0x4dee513d @name=[*******.4.1.2544.********.1.4], @value=#<SNMP::Integer:0xd49b062 @value=5>>, #<SNMP::VarBind:0x2b5da424 @name=[*******.4.1.2544.********.1.5], @value=#<SNMP::Integer:0x6043b216 @value=5>>, #<SNMP::VarBind:0x5f9b609a @name=[*******.4.1.2544.********.1.6], @value=#<SNMP::Integer:0x1f9111ab @value=2>>, #<SNMP::VarBind:0x59abeaf9 @name=[*******.4.1.2544.********.1.7], @value=#<SNMP::Integer:0x240530d8 @value=2>>, #<SNMP::VarBind:0x209e1b72 @name=[*******.4.1.2544.********.1.8], @value=#<SNMP::Integer:0x37154a83 @value=100>>, #<SNMP::VarBind:0x31ef82ba @name=[*******.4.1.2544.********.1.9], @value=\"HICTAIAPWV014\">, #<SNMP::VarBind:0x3c61e7af @name=[*******.4.1.2544.********.1.10], @value=\"*************\">, #<SNMP::VarBind:0x6cf43eda @name=[*******.4.1.2544.********.1.11], @value=\"\">, #<SNMP::VarBind:0x71a82095 @name=[*******.4.1.2544.********.1.12], @value=#<SNMP::Integer:0x3c9f8648 @value=0>>, #<SNMP::VarBind:0xf7052eb @name=[*******.4.1.2544.********.1.13], @value=#<SNMP::Integer:0x42bfbf31 @value=0>>, #<SNMP::VarBind:0x7223837b @name=[*******.4.1.2544.********.1.14], @value=\"\\a\\xE8\\a\\x04\\x05\\\"\\x11\\x04\">, #<SNMP::VarBind:0x32cef6b1 @name=[*******.4.1.2544.********.1.15], @value=\"\\a\\xE8\\a\\x04\\x05\\\"\\x11\\x04\">, #<SNMP::VarBind:0x3fa40d09 @name=[*******.4.1.2544.********.1.16], @value=\"Backup of configuration for network element adbertr11 has been initiated.\">, #<SNMP::VarBind:0xdd7bfb @name=[*******.4.1.2544.********.1.17], @value=#<SNMP::Integer:0x6e0b18c0 @value=2>>, #<SNMP::VarBind:0x2bb500ce @name=[*******.4.1.2544.********.1.18], @value=#<SNMP::Integer:0x34668cf4 @value=2>>, #<SNMP::VarBind:0x5f8f2b20 @name=[*******.4.1.2544.********.1.19], @value=#<SNMP::Integer:0x69f4e509 @value=0>>, #<SNMP::VarBind:0x6ac42b89 @name=[*******.4.1.2544.********.1.20], @value=\"\">, #<SNMP::VarBind:0x6ac51b09 @name=[*******.4.1.2544.********.1.21], @value=\"\">, #<SNMP::VarBind:0x4cb5fb5a @name=[*******.4.1.2544.********.1.22], @value=\"\">, #<SNMP::VarBind:0x2a78368 @name=[*******.4.1.2544.********.1.23], @value=#<SNMP::Integer:0x49f468d6 @value=1>>, #<SNMP::VarBind:0xb2b2fb2 @name=[*******.4.1.2544.********.1.24], @value=\"\">], @request_id=421959954, @error_index=0, @error_status=0, @source_ip=\"**************\">", "SNMPv2-MIB::sysUpTime.0": "9 days, 20:08:50.44", "host": "**************", "type": "snmp_trap", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "event.uuid": "07ed69e7-1a1a-40a7-8765-a720216f7645", "event.kafka.consumer_group": "a1559-logstash-a1132-adva-events-acc", "event.kafka.timestamp": "2024-07-04T03:34:17.766Z", "event.kafka.key": null, "event.kafka.offset": 81136, "event.kafka.topic": "a1132-adva-events-acc", "event.kafka.partition": 2, "event.logstash.instance_name": "iictniapls016", "snmptrap.corrRef": "", "snmptrap.severity": "5", "snmptrap.event_type": "5", "snmptrap.update": "2", "snmptrap.neType": "100", "snmptrap.direction": "0", "snmptrap.summary": "Backup of configuration for network element adbertr11 has been initiated.", "snmptrap.nmsTime": "\u0007è\u0007\u0004\u0005\"\u0011\u0004", "snmptrap.comment": "", "snmptrap.name": "S-NEBAK", "snmptrap.disabled": "2", "snmptrap.corr": "0", "snmptrap.description": "2", "snmptrap.neName": "HICTAIAPWV014", "snmptrap.customerName": "", "snmptrap.neTime": "\u0007è\u0007\u0004\u0005\"\u0011\u0004", "snmptrap.entity": "", "snmptrap.mtosiNeType": "", "snmptrap.acknowledged": "2", "snmptrap.location": "0", "snmptrap.id": "723415", "snmptrap.nelpAdress": "*************", "snmptrap.security": "1", "snmptrap.impairment": "2"}, {"host": "*************", "@version": "1", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.2544.********.0.1", "message": "{\"error_index\":0,\"variable_bindings\":{\"*******.4.1.2544.********.1.10\":\"*************\",\"*******.4.1.2544.********.1.11\":\"\",\"*******.4.1.2544.********.1.5\":1,\"*******.4.1.2544.********.1.6\":2,\"*******.4.1.2544.********.1.3\":\"HEART-BEAT\",\"*******.4.1.2544.********.1.4\":5,\"*******.4.1.2544.********.1.1\":0,\"*******.4.1.2544.********.1.23\":2,\"*******.*******.4.1.0\":\"SNMPv2-SMI::enterprises.2544.********.0.1\",\"*******.4.1.2544.********.1.2\":1,\"*******.4.1.2544.********.1.24\":\"\",\"*******.4.1.2544.********.1.9\":\"HICTAIAPWV014\",\"*******.4.1.2544.********.1.7\":2,\"*******.4.1.2544.********.1.8\":9999,\"*******.4.1.2544.********.1.20\":\"\",\"*******.4.1.2544.********.1.21\":\"\",\"*******.4.1.2544.********.1.22\":\"\",\"*******.*******.0\":170288441,\"*******.4.1.2544.********.1.16\":\"FSP-NM Heart Beat\",\"*******.4.1.2544.********.1.17\":1,\"*******.4.1.2544.********.1.18\":2,\"*******.4.1.2544.********.1.19\":0,\"*******.4.1.2544.********.1.12\":0,\"*******.4.1.2544.********.1.13\":0,\"*******.4.1.2544.********.1.14\":\"07:e9:05:1a:0e:0a:23:00\",\"*******.4.1.2544.********.1.15\":\"07:e9:05:1a:0e:0a:23:00\"},\"error_status\":0,\"type\":\"TRAP\",\"error_status_text\":\"Success\",\"community\":\"public\",\"version\":\"2c\",\"request_id\":572713424}", "type": "snmp_trap", "SNMPv2-MIB::sysUpTime.0": 170288441, "snmptrap": {"location": 0, "neType": 9999, "description": 1, "summary": "FSP-NM Heart Beat", "corrRef": "", "disabled": 2, "customerName": "", "security": 2, "neTime": "07:e9:05:1a:0e:0a:23:00", "id": 0, "nmsTime": "07:e9:05:1a:0e:0a:23:00", "neName": "HICTAIAPWV014", "impairment": 2, "comment": "", "corr": 0, "event_type": 5, "acknowledged": 2, "entity": "", "severity": 1, "nelpAdress": "*************", "name": "HEART-BEAT", "mtosiNeType": "", "direction": 0, "update": 1}, "@timestamp": "2025-05-26T12:10:33.627561144Z", "event": {"uuid": "10022fcc-6b3f-48b0-9cff-ff5619388e9a", "kafka": {"topic": "a1132-adva-events-acc", "offset": 655396, "consumer_group": "a1559-logstash-a1132-adva-events-acc", "partition": 0, "key": null, "timestamp": "2025-05-26T12:10:33.745Z"}, "logstash": {"instance_name": "iictmiapls016"}}}], "outputs": [{"event_type": "problem", "severity": 2, "summary": "Operational State = OUT (Outage)", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": true, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adblock11", "event_id": "723402", "node": "ADBLOCK11", "metric_type": "ENT-STAT", "metric_name": "adblock11", "node_alias": "***********", "raise_time": "2024-07-04 03:28:27", "clear_time": null, "wake_up_time": "2024-07-04 03:28:27"}, {"event_type": "problem", "severity": 2, "summary": "Operational State = OUT (Outage)", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": true, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adblock11", "event_id": "723404", "node": "ADBLOCK11", "metric_type": "ENT-STAT", "metric_name": "adblock11", "node_alias": "***********", "raise_time": "2024-07-04 03:29:24", "clear_time": null, "wake_up_time": "2024-07-04 03:29:24"}, {"event_type": "problem", "severity": 2, "summary": "Operational State = OUT (Outage)", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": true, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adblock11", "event_id": "723410", "node": "ADBLOCK11", "metric_type": "ENT-STAT", "metric_name": "adblock11", "node_alias": "***********", "raise_time": "2024-07-04 03:32:17", "clear_time": null, "wake_up_time": "2024-07-04 03:32:17"}, {"event_type": "heartbeat", "severity": 1, "summary": "FSP-NM Heart Beat", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fspNm\"}", "ci_id": "ADVA_HB", "event_id": "0", "node": "HICTAIAPWV014", "metric_type": "/ApplicationEvent/", "metric_name": "Heartbeat", "node_alias": "*************", "raise_time": "2024-07-04 03:31:32", "clear_time": null, "wake_up_time": "2024-07-04 03:31:32"}, {"event_type": "problem", "severity": 5, "summary": "Loss of Signal", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adblock11", "event_id": "724380", "node": "ADBLOCK11", "metric_type": "LOS", "metric_name": "adblock11", "node_alias": "***********", "raise_time": "2024-07-04 11:01:58", "clear_time": null, "wake_up_time": "2024-07-04 11:01:58"}, {"event_type": "problem", "severity": 4, "summary": "Network element does not respond to SNMP requests", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "admechl22", "event_id": "724832", "node": "ADMECHL22", "metric_type": "DCN", "metric_name": "admechl22", "node_alias": "*************", "raise_time": "2024-07-04 14:29:20", "clear_time": null, "wake_up_time": "2024-07-04 14:29:20"}, {"event_type": "problem", "severity": 3, "summary": "Laser-Current Abnormal", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": true, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "admechl11", "event_id": "735284", "node": "ADMECHL11", "metric_type": "LBCLANR", "metric_name": "admechl11", "node_alias": "************", "raise_time": "2024-07-07 22:41:10", "clear_time": null, "wake_up_time": "2024-07-07 22:41:10"}, {"event_type": "clear", "severity": 0, "summary": "CLEARED: Loss of Signal", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adblock11", "event_id": "724399", "node": "ADBLOCK11", "metric_type": "LOS", "metric_name": "adblock11", "node_alias": "***********", "raise_time": "2024-07-04 11:06:59", "clear_time": "2024-07-04 11:06:59", "wake_up_time": "2024-07-04 11:06:59"}, {"event_type": "problem", "severity": 2, "summary": "Telemetry Interface: NE-ALM-CR", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": true, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "admechl11", "event_id": "735291", "node": "ADMECHL11", "metric_type": "TIFALM", "metric_name": "admechl11", "node_alias": "************", "raise_time": "2024-07-07 22:41:10", "clear_time": null, "wake_up_time": "2024-07-07 22:41:10"}, {"event_type": "problem", "severity": 1, "summary": "Local copy file started", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adbertr11", "event_id": "723416", "node": "ADBERTR11", "metric_type": "CPY-MEM-PRTL", "metric_name": "adbertr11", "node_alias": "************", "raise_time": "2024-07-04 03:34:17", "clear_time": null, "wake_up_time": "2024-07-04 03:34:17"}, {"event_type": "problem", "severity": 1, "summary": "Local copy file started", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "admechl11", "event_id": "723696", "node": "ADMECHL11", "metric_type": "CPY-MEM-PRTL", "metric_name": "admechl11", "node_alias": "************", "raise_time": "2024-07-04 05:45:12", "clear_time": null, "wake_up_time": "2024-07-04 05:45:12"}, {"event_type": "problem", "severity": 1, "summary": "Local copy file started", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "admechl21", "event_id": "723978", "node": "ADMECHL21", "metric_type": "CPY-MEM-PRTL", "metric_name": "admechl21", "node_alias": "************", "raise_time": "2024-07-04 07:56:06", "clear_time": null, "wake_up_time": "2024-07-04 07:56:06"}, {"event_type": "problem", "severity": 1, "summary": "Local copy file started", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adblock12", "event_id": "724550", "node": "ADBLOCK12", "metric_type": "CPY-MEM-PRTL", "metric_name": "adblock12", "node_alias": "***********", "raise_time": "2024-07-04 12:17:56", "clear_time": null, "wake_up_time": "2024-07-04 12:17:56"}, {"event_type": "problem", "severity": 1, "summary": "Errored Seconds FEC OTU Monitoring", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adantbe11", "event_id": "724900", "node": "ADANTBE11", "metric_type": "T-ES-FEC-HT__1DAY", "metric_name": "adantbe11", "node_alias": "*************", "raise_time": "2024-07-04 15:00:21", "clear_time": null, "wake_up_time": "2024-07-04 15:00:21"}, {"event_type": "problem", "severity": 4, "summary": "Network element does not respond to SNMP requests", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "admonce11", "event_id": "725109", "node": "ADMONCE11", "metric_type": "DCN", "metric_name": "admonce11", "node_alias": "**************", "raise_time": "2024-07-04 16:40:14", "clear_time": null, "wake_up_time": "2024-07-04 16:40:14"}, {"event_type": "problem", "severity": 4, "summary": "Network element does not respond to SNMP requests", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "admonce12", "event_id": "725386", "node": "ADMONCE12", "metric_type": "DCN", "metric_name": "admonce12", "node_alias": "**************", "raise_time": "2024-07-04 18:51:09", "clear_time": null, "wake_up_time": "2024-07-04 18:51:09"}, {"event_type": "problem", "severity": 1, "summary": "Local copy file started", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adhasse11", "event_id": "725661", "node": "ADHASSE11", "metric_type": "CPY-MEM-PRTL", "metric_name": "adhasse11", "node_alias": "*************", "raise_time": "2024-07-04 21:01:34", "clear_time": null, "wake_up_time": "2024-07-04 21:01:34"}, {"event_type": "problem", "severity": 1, "summary": "Local copy file started", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "adnamur11", "event_id": "726234", "node": "ADNAMUR11", "metric_type": "CPY-MEM-PRTL", "metric_name": "adnamur11", "node_alias": "*************", "raise_time": "2024-07-05 01:23:23", "clear_time": null, "wake_up_time": "2024-07-05 01:23:23"}, {"event_type": "problem", "severity": 5, "summary": "Client Affected", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fspNm\"}", "ci_id": "", "event_id": "735314", "node": "N/A", "metric_type": "SERV-C-AFFECTED", "metric_name": "", "node_alias": "", "raise_time": "2024-07-07 22:41:11", "clear_time": null, "wake_up_time": "2024-07-07 22:41:11"}, {"event_type": "problem", "severity": 1, "summary": "Service Faulted - Client Affected", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fspNm\"}", "ci_id": "HICTAIAPWV014", "event_id": "0", "node": "HICTAIAPWV014", "metric_type": "SERV_FAULTED_C_AFFECTED", "metric_name": "HICTAIAPWV014", "node_alias": "*************", "raise_time": "2024-07-07 22:41:11", "clear_time": null, "wake_up_time": "2024-07-07 22:41:11"}, {}, {"event_type": "problem", "severity": 1, "summary": "Service Faulted - Network Affected", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fspNm\"}", "ci_id": "HICTAIAPWV014", "event_id": "0", "node": "HICTAIAPWV014", "metric_type": "SERV_FAULTED_N_AFFECTED", "metric_name": "HICTAIAPWV014", "node_alias": "*************", "raise_time": "2024-07-07 22:41:11", "clear_time": null, "wake_up_time": "2024-07-07 22:41:11"}, {"event_type": "problem", "severity": 1, "summary": "Service Faulted - Network Affected", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fspNm\"}", "ci_id": "HICTAIAPWV014", "event_id": "0", "node": "HICTAIAPWV014", "metric_type": "SERV_FAULTED_N_AFFECTED", "metric_name": "HICTAIAPWV014", "node_alias": "*************", "raise_time": "2024-07-07 22:41:12", "clear_time": null, "wake_up_time": "2024-07-07 22:41:12"}, {"event_type": "problem", "severity": 2, "summary": "Backup of configuration for network element adbertr11 has been initiated.", "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": true, "additional_data": "{\"network_element_type\": \"fsp3000R7\"}", "ci_id": "HICTAIAPWV014", "event_id": "723415", "node": "HICTAIAPWV014", "metric_type": "S-NEBAK", "metric_name": "HICTAIAPWV014", "node_alias": "*************", "raise_time": "2024-07-04 03:34:17", "clear_time": null, "wake_up_time": "2024-07-04 03:34:17"}, {"event_type": "heartbeat", "severity": 1, "agent_id": 0, "clear_type": "automatic", "manager": "mon-adva", "action_class": "IT", "top_level": "A1332", "handle_time": "2024-01-01 12:00:01", "actionable": false, "additional_data": "{\"network_element_type\": \"fspNm\"}", "ci_id": "ADVA_HB", "event_id": "N/A", "node": "HICTAIAPWV014", "metric_type": "/ApplicationEvent/", "metric_name": "Heartbeat", "node_alias": "*************", "raise_time": "2025-05-26 12:10:35", "clear_time": null, "wake_up_time": "2025-05-26 12:10:35"}]}