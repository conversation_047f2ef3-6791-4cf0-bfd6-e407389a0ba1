"""Entrypoint for the application."""

from fastapi import FastAPI, HTTPException

from core_api.config import config
from core_api.db import get_alarm_info_response_from_criteria
from core_api.exceptions import CoreAPIError
from core_api.models import AlarmInfoCriteria
from olympus_common import auth, defaults

app = FastAPI(debug=config.debug)

if not config.debug:
    auth.add_security(app, config.jwk_config)


@app.post("/alarm/info")
async def get_alarm_info(criteria: AlarmInfoCriteria):
    """Retrieve one or more alarms given the criteria and construct the response as expected.

    An options field can be specified with the following keys:
    - active_only: if True, only retrieve the active alarms.
    - max_results: the maximum number of results.
    """
    try:
        return get_alarm_info_response_from_criteria(criteria)
    except CoreAPIError as exc:
        raise HTTPException(status_code=exc.status_code, detail=exc.msg) from exc


@app.get("/")
def get():
    """Return a dummy response.

    This dummy endpoint acts as some kind of liveness probe.
    """
    return "core_api module for api call"


def main() -> None:
    """Run the FastAPI service."""
    defaults.run_fastapi_service(app, config.server_config)
