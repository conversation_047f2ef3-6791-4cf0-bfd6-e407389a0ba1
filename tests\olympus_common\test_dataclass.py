import json
from dataclasses import dataclass

import pytest
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from olympus_common import exceptions, utils
from olympus_common.dataclass import dataclass_field, env_field


@dataclass
class Foo:
    """Represent an example dataclass with some env_fields.

    Notes
    -----
    This is a simple object for testing. This object is not frozen and can be instantiated multiple times.
    For example, a config should have `metaclass=utils.Singleton` and `dataclass.frozen=True` to ensure this behavior:
    ```python
    @dataclass(frozen=True)
    class Foo(metaclass=Singleton):
        bar: str = env_field("BAR")
        ...
    ```
    """

    bar: str = env_field("BAR")
    baz: int = env_field("BAZ", astype=int)
    bay: bool = env_field("BAY", astype=utils.strtobool)
    bax: list[str] = env_field("BAX", astype=json.loads)
    baw: int = env_field("BAW", astype=int, default="1")


@dataclass
class Bar:
    """Represent an example dataclass with a nested dataclass."""

    foo: Foo = dataclass_field(Foo)


def test_dataclass_creation(mocker: MockerFixture) -> None:
    """Test that creating a dataclass with env_fields properly gets and converts the required values."""
    environ = _get_environ()
    environ.pop("BAW", None)
    mocker.patch.dict("os.environ", environ)
    foo = Foo()
    # See _get_environ for the default values.
    assert foo.bar == "dummystr"
    assert foo.baz == 1
    assert foo.bay is True
    assert foo.bax == ["dummystr"]
    assert foo.baw == 1


@pytest.mark.parametrize(
    "obj",
    [
        Foo,  # Test that root level missing keys raises
        Bar,  # Test that nested missing keys raises
    ],
)
def test_missing_key(obj, mocker: MockerFixture):
    """Test that a missing key raises the appropriate error."""
    environ = _get_environ("BAR")
    mocker.patch.dict("os.environ", environ)
    with pytest.raises(exceptions.MissingKeyError, match="BAR"):
        obj()


@pytest.mark.parametrize(
    "obj",
    [
        Foo,  # Test that root level error raises
        Bar,  # Test that nested error raises
    ],
)
def test_dataclass_exception(obj, mocker: MockerFixture):
    """Test that an invalid value raises the appropriate error."""
    environ = _get_environ(BAZ="NaN")
    mocker.patch.dict("os.environ", environ)
    with pytest.raises(exceptions.OlympusError, match="BAZ"):
        obj()


def _get_environ(*keys2pop: str, **kwargs: str) -> dict[str, str]:
    """Return a dictionary which can be used as a value when mocking os.environ.

    A default environment will be returned if no arguments or keywords are provided.
    Provided keywords will overwrite anything in the default.
    Provided arguments will be popped after merging default with provided keywords.
    """
    default = {"BAR": "dummystr", "BAZ": "1", "BAY": "yEs", "BAX": '["dummystr"]'}
    environ = {**default, **kwargs}
    for key in keys2pop:
        environ.pop(key, None)
    return environ
