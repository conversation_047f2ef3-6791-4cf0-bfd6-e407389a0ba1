<html>

<head></head>
<style>
    table,
    th,
    td,
    p {
        border-collapse: collapse;
        font-family: <PERSON><PERSON><PERSON>;
    }

    table td,
    th {
        padding: 12px;
    }

    table thead th {
        background-color: #213A53;
        color: #ffffff;
        font-weight: bold;
        font-size: 15px;
        border: 1px solid #54585d;
    }

    table tbody td {
        color: #163c55;
        border: 1px solid #AEAEAE;
        font-size: 13px;
    }

    table tbody tr {
        background-color: #e1eaf3;
        font-size: 13px;
    }
</style>

<body>
    <img src="data:image/png;base64,{{ header_image }}" alt="Red dot">

    {% if (certificates_2|length + certificates|length) > 1 %}
    <p>Bonjour,</p>
    <p>
        Vous êtes identifié comme personne de contact concernant les certificats de Talos ci-dessous qui vont bientôt expirer.<br>
        Pouvez-vous prendre les actions nécessaires pour renouveler ces certificats ou nous informer si un certificat n’est plus/ne doit plus être utilisé.
    </p>

    <p>Goeiedag,</p>
    <p>
        U werd aangeduid als de contactpersoon voor onderstaande Talos certificaten die binnenkort verlopen.<br>
        Kunt u de nodige acties ondernemen om deze certificaten te verlengen of ons te informeren wanneer deze certificaten niet meer gebruikt/mag gebruikt worden.
    </p>

    {% else %}

    <p>Bonjour,</p>    
    <p>
        Vous êtes identifié comme personne de contact concernant le certificat de Talos ci-dessous qui va bientôt expirer.<br>
        Pouvez-vous prendre les actions nécessaires pour renouveler ce certificat ou nous informer si ce certificat n’est plus/ne doit plus être utilisé.<br>
    </p>

    <p>Goeiedag,</p>
    <p>
        U werd aangeduid als de contactpersoon voor onderstaand Talos certificaat dat binnenkort verloopt.
        Kunt u de nodige acties ondernemen om dit certificaat te verlengen of ons te informeren wanneer dit certificaat niet meer gebruikt wordt/mag worden.
    </p>
    {% endif %}


    {% for remaining_days, certs in [(days_left_2, certificates_2), (days_left, certificates)] %}    
    {% if certs|length > 0 %}
    {% if certs|length > 1 %}
    <p>
        Il reste moins de {{ remaining_days }} jours pour réactiver les certificats ci-dessous.<br>
        U heeft nog minder dan {{ remaining_days }} dagen over om de onderstaande certificaten opnieuw te reactiveren
    </p>
    {% else %}
    <p>
        Il reste moins de {{ remaining_days }} jours pour réactiver le certificat ci-dessous.<br>
        U heeft nog minder dan {{ remaining_days }} dagen over om het onderstaande certificaat opnieuw te reactiveren.
    </p>
    {% endif %}



    <table>
        <thead>
            <tr>
                <th>Common name</th>
                <th>Issuer</th>
                <th>Application code</th>
                <th>Expiry days</th>
                <th>Expiry date</th>
            </tr>
        </thead>
        <tbody>
            {% for certificate in certs %}
            <tr>
                <td>{{ certificate.common_name }}</td>
                <td>{{ certificate.issuer }}</td>
                <td>{{ certificate.application_code }}</td>
                <td>{{ certificate.expiry_days }}</td>
                <td>{{ certificate.not_after }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}
    {% endfor %}

    <p>
        Bien à vous,<br>
        Met vriendelijke groeten,
    </p>

    <p>I-ICT.312</p>
</body>