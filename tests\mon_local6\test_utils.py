"""Test utils for the mon-local6 package."""

import pandas as pd

from mon_local6.utils import (
    is_airco_message,
    is_dcms_message,
    is_ha_heartbeat,
    is_ha_message,
    is_scada_message,
    startswith_endswith,
)


def test_is_airco_message():
    data = {"tag": ["A1574-123", "A1574-456", "B1234", "A1574"]}
    df = pd.DataFrame(data)
    assert is_airco_message(df.iloc[0]) is True
    assert is_airco_message(df.iloc[1]) is True
    assert is_airco_message(df.iloc[2]) is False
    assert is_airco_message(df.iloc[3]) is True


def test_is_dcms_message():
    data = {"tag": ["A2103-123", "A2137-456", "B1234", "A2103-"]}
    df = pd.DataFrame(data)
    assert is_dcms_message(df.iloc[0]) is True
    assert is_dcms_message(df.iloc[1]) is True
    assert is_dcms_message(df.iloc[2]) is False
    assert is_dcms_message(df.iloc[3]) is True


def test_is_ha_message():
    data = {"channel": ["OPTIC_HA", "OTHER_CHANNEL", "OPTIC_HA"]}
    df = pd.DataFrame(data)
    assert is_ha_message(df.iloc[0]) is True
    assert is_ha_message(df.iloc[1]) is False
    assert is_ha_message(df.iloc[2]) is True


def test_is_scada_message():
    data = {"tag": ["A1724-123", "A1724-456", "B1234", "A1724"]}
    df = pd.DataFrame(data)
    assert is_scada_message(df.iloc[0]) is True
    assert is_scada_message(df.iloc[1]) is True
    assert is_scada_message(df.iloc[2]) is False
    assert is_scada_message(df.iloc[3]) is True


def test_is_ha_heartbeat():
    data = {
        "channel": ["OPTIC_HA", "OTHER_CHANNEL", "OPTIC_HA"],
        "tag": ["arte2", "a1112", "a1250"],
        "metric": ["metric_heartbeat", "other_metric", "metric_heartbeat"],
        "syslog_severity": ["info", "warning", "info"],
    }
    df = pd.DataFrame(data)
    assert is_ha_heartbeat(df.iloc[0]) is True
    assert is_ha_heartbeat(df.iloc[1]) is False
    assert is_ha_heartbeat(df.iloc[2]) is True

    # Test each row


def test_startswith_endswith():
    assert startswith_endswith("A1617-SyslogHB", "A1617-", "-SyslogHB") is True
    assert startswith_endswith("A1617-SNMP_HB", "A1617-", "-SNMP_HB") is True
    assert startswith_endswith("A1617-Other", "A1617-", "-SyslogHB") is False
    assert startswith_endswith("A1617-Other", "A1617-", "-SNMP_HB") is False
