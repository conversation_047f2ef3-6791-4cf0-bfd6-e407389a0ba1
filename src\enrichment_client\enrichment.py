"""Enrichment module for the enrichment-client.

Notes
-----
The uCMDB database is set as case insensitive so the check for the metric, cis and metric types should be
case insensitive as well.
"""

import concurrent.futures
import logging
from bisect import bisect_left
from datetime import datetime, timezone
from typing import Any, Callable, Sequence

from sqlalchemy import select
from sqlalchemy.orm import Session

from enrichment_client import static
from enrichment_client.config import config
from olympus_common import enums
from olympus_common.ca_spectrum import CA_SPECTRUM_METRICS
from olympus_common.db import Alarm, Enrichment, Occurrence, get_alarm_field
from olympus_common.elastic_apm import CaptureSpan, elastic_apm, trace_scan
from olympus_common.ucmdb import AssetListView, HierarchyListView, MetricListView, create_ucmdb_db_session


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def enrichment(alarms_be: list[dict], session: Session) -> list[dict]:
    """Execute the main code for the enrichment script."""
    logging.info(f"Number of alarms to be enriched: {len(alarms_be)}")
    # The "new_enrichment" dict will contain the data to write in DB.
    dummy_enrichment = Enrichment()
    for alarm in alarms_be:
        if (
            not alarm["agent"]["ci_enrichment"]
            and not alarm["agent"]["metric_enrichment"]
            and not alarm["agent"]["topology_enrichment"]
        ):
            alarm["process_status"] = enums.AlarmJobStatus.NOT_DONE.value
        else:
            alarm["new_enrichment"] = dummy_enrichment.asdict()
            alarm["process_status"] = enums.AlarmJobStatus.DONE.value
    ci_ids = list(
        {
            alarm["ci_id"]
            for alarm in alarms_be
            if alarm["ci_id"] and alarm["process_status"] != enums.AlarmJobStatus.NOT_DONE.value
        }
    )
    metric_types = list({alarm["metric_type"] for alarm in alarms_be if alarm["metric_type"]})

    ucmdb_db_session = create_ucmdb_db_session()

    try:
        cis_data = _get_ucmdb_ci_data(ci_ids, ucmdb_db_session)
        floc_ids = [ci_data["floc_id"] for ci_data in cis_data if ci_data["floc_id"]]
        cis_without_floc_id = _construct_cis_without_floc_id(ci_ids, cis_data)
        metrics_data = _get_ucmdb_metric_data(floc_ids, cis_without_floc_id, metric_types, ucmdb_db_session)
        topologies_data = _get_ucmdb_topology_data(floc_ids, cis_without_floc_id, ucmdb_db_session)

        ucmdb_db_session.close()
    except Exception as exc:
        _update_alarm_process(alarms_be, exc)
        ucmdb_db_session.close()
        elastic_apm.capture_exception()
        raise Exception(exc) from exc

    try:
        # CI Enrichment
        cis_data_list = [cis_data] * len(alarms_be)
        ci_enrichment_arguments = list(zip(alarms_be, cis_data_list, strict=True))
        alarms_ci_enriched = _parallelize_process(ci_enrichment_arguments, _ci_enrichment)

        # Update network aliases for EML, SDH and LucentOMS
        alarms_ci_enriched = _parallelize_process(alarms_ci_enriched, _update_network_alias)
        logging.info(f"Number of alarms after ci enrichment: {len(alarms_ci_enriched)}")

        # Topology enrichment
        topologies_data_list = [topologies_data] * len(alarms_ci_enriched)
        topology_enrichment_arguments = list(zip(alarms_ci_enriched, topologies_data_list, strict=True))
        alarms_topology_enriched = _parallelize_process(topology_enrichment_arguments, _topology_enrichment)
        logging.info(f"Number of alarms after topology enrichment: {len(alarms_topology_enriched)}")

        # Metric Enrichment
        metrics_data_list = [metrics_data] * len(alarms_topology_enriched)
        sessions = [session] * len(alarms_topology_enriched)
        metric_enrichment_arguments = list(zip(alarms_topology_enriched, metrics_data_list, sessions, strict=True))
        alarms_metric_enriched = _parallelize_process(metric_enrichment_arguments, _metric_enrichment)
        logging.info(f"Number of alarms after metric and ci enrichment: {len(alarms_metric_enriched)}")

        # Get the main service and sub-service
        alarms_with_services = _parallelize_process(alarms_metric_enriched, _get_main_service)

        # Compute enrichment status on Alarms.
        _compute_enrichment_status(alarms_be, alarms_with_services)
    except Exception as exc:
        _update_alarm_process(alarms_be, exc)
        elastic_apm.capture_exception()
        raise exc

    # Transform enriched_alarms into Enrichments (but still dicts).
    return _enriched_alarms_to_enrichments(alarms_with_services)


@CaptureSpan(span_type=enums.MeasureType.DB_QUERY.value)
def _get_ucmdb_ci_data(ci_ids: list[str], ucmdb_db_session: Session) -> list[dict]:
    """Get the data from the uCMDB 'AssetListView' corresponding to the CIs.

    Parameters
    ----------
    ci_ids : list[str]
        The list of CI_IDs.
    ucmdb_db_session: Session
        The UCMDB session.

    Returns
    -------
    list[dict]
        The CIs data for the enrichment.
    """
    if ci_ids:
        cis_data = AssetListView().get_ci_data_dict(ci_ids, ucmdb_db_session)

        # De-duplication, as some CI IDs may correspond to multiple FLOC IDs.
        # We simply take the first FLOC ID for each identification.
        previous_identification = ""
        new_cis_data = []

        for ci in cis_data:
            if ci["identification"] != previous_identification:
                new_cis_data.append(ci)
            previous_identification = ci["identification"]
    else:
        new_cis_data = []
    return new_cis_data


@CaptureSpan(span_type=enums.MeasureType.DB_QUERY.value)
def _get_ucmdb_metric_data(
    floc_ids: list[str], ci_ids: list[str], metric_types: list[str], ucmdb_db_session: Session
) -> list[dict]:
    """Get the data from the uCMDB 'MetricListView' corresponding to the CIs and metric_types."""
    if floc_ids and metric_types:
        metrics_data = MetricListView().get_metric_data_dict(floc_ids, ci_ids, metric_types, ucmdb_db_session)
    else:
        metrics_data = []
    return metrics_data


@CaptureSpan(span_type=enums.MeasureType.DB_QUERY.value)
def _get_ucmdb_topology_data(floc_ids: list[str], ci_ids: list[str], ucmdb_db_session: Session) -> list[dict]:
    """Get the data from the uCMDB 'HierarchyListView' corresponding to the CIs and metric_types."""
    if floc_ids:
        cis_data = HierarchyListView().get_topology_data_dict(floc_ids, ci_ids, ucmdb_db_session)
    else:
        cis_data = []
    return cis_data


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_enrichment(alarm: dict, cis_data: list[dict]) -> dict:
    """Enrich the alarm with the CIs data from uCMDB.

    Parameters
    ----------
    alarm : dict
        The alarm.
    cis_data : list[dict]
        The CIs data from uCMDB.

    Returns
    -------
    dict
        The CI enriched alarm.
    """
    try:
        skip_enrichment = False

        if alarm["agent"]["ci_enrichment"] is False:
            skip_enrichment = True

        ci_id: str = alarm["ci_id"]

        if alarm["agent"]["name"] == "CA_Spectrum":
            # Hirshmann devices (tunnel Antwerp Central) and datalines (Dt.R/St.R) don't have ucmdb CI's
            ci_id_lower = ci_id.lower()
            if ci_id_lower.startswith("antce-") or ci_id_lower.startswith("dt.r") or ci_id_lower.startswith("st.r"):
                skip_enrichment = True

        if skip_enrichment:
            if "new_enrichment" in alarm:
                alarm["new_enrichment"]["ci_enrichment"] = False
            return alarm

        for ci_data in cis_data:
            identification: str = ci_data["identification"]
            if identification and ci_id and identification.lower() == ci_id.lower():
                alarm["new_enrichment"]["severity"] = _update_severity(alarm, ci_data)
                alarm["new_enrichment"] = _fill_alarm_data(alarm["new_enrichment"], ci_data)
                alarm["new_enrichment"]["ci_enrichment"] = True
                # Set actionable to False for ADVA Location_Attr=’LABO’ or ‘BRICO 4 LABO’ alarms
                # TODO: Remove this condition on ADVA as there's no CI enrichment for ADVA anyway.
                if (
                    alarm["agent"]["name"] == "ADVA"
                    and ci_data["location_attribute"]
                    and ("LABO" in ci_data["location_attribute"] or "BRICO 4 LABO" in ci_data["location_attribute"])
                ):
                    alarm["new_enrichment"]["actionable"] = False
                # Update summary for DataLines agent with CI_SUB_TYPE prefix
                if alarm["agent"]["name"] == "DataLines":
                    _update_datalines_summary(alarm)
                break
        else:
            # Set actionable to False for:
            # * GSM-R alarms for which a CI_ID is not found
            # * NFM-P alarms for which a CI_ID is not found
            if alarm["agent"]["name"] in ["NetAct", "NFM-P"]:
                alarm["new_enrichment"]["actionable"] = False
                return alarm

            alarm["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
            alarm["process_error"] = str("No CI found.")
    except Exception as exc:
        logging.exception(exc)
        alarm["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
        alarm["process_error"] = str(exc)
        elastic_apm.capture_exception()
    return alarm


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _update_severity(alarm: dict, ci_data: dict) -> int:
    """Update the severity of the given alarm if needed.

    Parameters
    ----------
    alarm : dict
        The alarm.
    ci_data : dict
        The CI data from uCMDB corresponding to the alarm.

    Returns
    -------
    int
        The updated severity.
    """
    metric_name_: str = alarm["metric_name"]
    ci_group_ = ci_data.get("ci_group") or "N/A"
    critical_ci_ = ci_data.get("critical_ci") or "N/A"
    category_ = ci_data.get("location_category") or "N/A"
    location_type_ = ci_data.get("location_type") or "N/A"
    severity = None
    last_problem = alarm.get("last_problem", {}) or {}
    summary = last_problem.get("summary") or "N/A"
    match alarm["agent"]["name"]:
        case "CA_Spectrum":
            severity = _severity_update_spectrum(metric_name_, ci_group_, critical_ci_)
        case "StruxureWare":
            severity = _severity_update_struxureware(category_, location_type_, metric_name_, summary)
        case "MileStone":
            severity = _severity_update_milestone(critical_ci_, alarm["ci_id"])

    if severity is not None:
        return severity
    if alarm["last_problem"]:
        return alarm["last_problem"]["severity"]
    return alarm["last_occurrence"]["severity"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity_update_milestone(critical_ci: str, ci_id_: str) -> int | None:
    """Return a new severity for MileStone if needed.

    Parameters
    ----------
    critical_ci : str
        The critical_ci field of the ci data for the alarm.
    ci_id_ : str
        The ci_id of the alarm.

    Returns
    -------
    int | None
        Return a new severity if needed else return None.
    """
    if "-THM-" in ci_id_:
        return (
            enums.Severity.MAJOR.value
            if critical_ci and "BLUE" in critical_ci.upper()
            else enums.Severity.CRITICAL.value
        )
    elif "-DIP-" in ci_id_ or "-CIP-" in ci_id_ or "-RLM-" in ci_id_:
        return (
            enums.Severity.WARNING.value
            if critical_ci and "BLUE" in critical_ci.upper()
            else enums.Severity.CRITICAL.value
        )
    elif "-ADA-" in ci_id_:
        return enums.Severity.MAJOR.value
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity_update_struxureware(category: str, location_type: str, metric_name_: str, summary: str) -> int | None:
    """Return a new severity for StruxureWare if needed.

    Parameters
    ----------
    category : str
        The category of the ci data for the alarm.
    location_type : str
        The location type of the ci data for the alarm.
    metric_name_ : str
        The metric name of the alarm.
    summary : str
        The summary of the alarm.

    Returns
    -------
    int | None
        Return a new severity if needed else return None.
    """
    severity = None
    # Keep the first part of the summary (eg : SYSTEM - MINOR ALARM: FALSE. ) -> SYSTEM - MINOR ALARM
    summary = summary.split(": ")[0]
    if "BATTERY" in metric_name_ or "VOLTAGE" in metric_name_:
        severity = enums.Severity.MAJOR.value if category == "T4" else enums.Severity.CRITICAL.value
    elif "COMMUNICATION" in metric_name_:
        severity = enums.Severity.MINOR.value if category == "T4" else enums.Severity.CRITICAL.value
    elif "EXTERNAL" in metric_name_:
        if summary.startswith("EXTERNAL - D1") and location_type == "GSM-R site - Site GSM-R":
            severity = enums.Severity.CRITICAL.value
        elif summary.startswith("EXTERNAL - D1") or summary.startswith("EXTERNAL - D2") or category != "T4":
            severity = enums.Severity.MAJOR.value
        elif category == "T4":
            severity = enums.Severity.MINOR.value
    elif "INVERTER" in metric_name_ or "RECTIFIER" in metric_name_ or "SYSTEM" in metric_name_:
        if " - MINOR" in summary:
            severity = enums.Severity.WARNING.value if category == "T4" else enums.Severity.MINOR.value
        else:
            severity = enums.Severity.MINOR.value if category == "T4" else enums.Severity.MAJOR.value
    elif "TEMPERATURE" in metric_name_:
        severity = enums.Severity.MINOR.value if category in ["T2", "T3", "T4"] else enums.Severity.CRITICAL.value
    elif "ZONE" in metric_name_:
        severity = (
            enums.Severity.MAJOR.value if (" - F" in summary and "ZONE" in summary) else enums.Severity.CRITICAL.value
        )
    return severity


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity_update_spectrum(metric_name_: str, ci_group_: str, critical_ci_: str) -> int | None:
    """Return a new severity for CA_Spectrum if needed.

    Parameters
    ----------
    metric_name_ : str
        The metric name of the alarm.
    ci_group_ : str
        The ci group of the alarm.
    critical_ci_ : str
        The critical_ci of the alarm.

    Returns
    -------
    int | None
        Return a new severity if needed else return None.
        We only override if critical or orange, severity prod/nonprod is already set in mon script spectrum
    """
    if ci_group_.lower() == "orange" or (critical_ci_.isdigit() and int(critical_ci_) > 0):
        severity = CA_SPECTRUM_METRICS.get(metric_name_, {}).get("severity_orange_crit", None)
        return int(severity) if severity is not None else None
    elif ci_group_.lower() != "orange" and (critical_ci_ in (0, "", None)):
        severity = CA_SPECTRUM_METRICS.get(metric_name_, {}).get("severity_orange_non_crit", None)
        return int(severity) if severity is not None else None
    else:
        return None


def _fill_alarm_data(alarm: dict, data_dict: dict) -> dict:
    """Fill the given pandas Series with all the data from the given dict.

    Parameters
    ----------
    alarm : dict
        The alarm.
    data_dict : dict
        The data that will be added to the Series.

    Returns
    -------
    dict
        The Series with the dict data added.
    """
    for key in data_dict.keys():
        alarm[key] = data_dict[key]
    return alarm


def _update_datalines_summary(alarm: dict) -> None:
    """Update the summary field for DataLines agent with CI_SUB_TYPE prefix according to the optic.

    Parameters
    ----------
    alarm : dict
        The alarm containing the summary and ci_sub_type information.
    """
    try:
        ci_sub_type = alarm.get("ci_sub_type")
        if ci_sub_type and ci_sub_type.strip():
            # Get the current summary from last_problem or last_occurrence
            current_summary = ""
            if alarm.get("last_problem") and alarm["last_problem"].get("summary"):
                current_summary = alarm["last_problem"]["summary"]
            elif alarm.get("last_occurrence") and alarm["last_occurrence"].get("summary"):
                current_summary = alarm["last_occurrence"]["summary"]

            # Only update if summary doesn't already start with the CI_SUB_TYPE prefix
            if current_summary and not current_summary.startswith(f"{ci_sub_type} - "):
                updated_summary = f"{ci_sub_type} - {current_summary}"

                # Update the summary in the appropriate location
                if alarm.get("last_problem"):
                    alarm["last_problem"]["summary"] = updated_summary
                elif alarm.get("last_occurrence"):
                    alarm["last_occurrence"]["summary"] = updated_summary
    except Exception as exc:
        logging.exception(f"Error updating DataLines summary: {exc}")
        elastic_apm.capture_exception()


# TODO: Check for the point on enrichment DD (6.10) if still needed.
# TODO: Check what is EML and SDH because they are not in config.SOURCES


def _update_network_alias(alarm: dict) -> dict:
    """Update the network alias of the alarm if needed.

    Parameters
    ----------
    alarm : dict
        The alarm.

    Returns
    -------
    dict
        The alarm with the updated network alias.
    """
    try:
        if alarm["agent"]["name"] in ["EML", "SDH", "LucentOMS"]:
            if alarm["ci_sub_type"] == "SDH PHYS CONNECTION":
                alarm["network_alias"] += alarm["ci_id"]
    except Exception as exc:
        logging.exception(exc)
        alarm["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
        alarm["process_error"] = str(exc)
        elastic_apm.capture_exception()

    return alarm


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_enrichment(alarm: dict, metrics_data: list[dict], session: Session) -> dict:
    """Enrich the alarm with the metric data from uCMDB.

    Parameters
    ----------
    alarm : dict
        The alarm.
    metrics_data : list[dict]
        The metrics data fetched from uCMDB.
    session: Session
        The backend DB session.

    Returns
    -------
    dict
        The metric enriched alarm.
    """
    try:
        skip_enrichment = False

        if alarm["agent"]["metric_enrichment"] is False:
            skip_enrichment = True

        ci_id: str = alarm["ci_id"]

        if alarm["agent"]["name"] == "CA_Spectrum":
            # Hirshmann devices (tunnel Antwerp Central) and datalines (Dt.R/St.R) don't have ucmdb CI's
            ci_id_lower = ci_id.lower()
            if ci_id_lower.startswith("antce-") or ci_id_lower.startswith("dt.r") or ci_id_lower.startswith("st.r"):
                skip_enrichment = True

        if skip_enrichment:
            if "new_enrichment" in alarm:
                alarm["new_enrichment"]["metric_enrichment"] = False
            return alarm

        agent = alarm["agent"]["ucmdb_name"]
        alarm_metric_type: str = alarm["metric_type"]
        alarm_metric_name: str = alarm["metric_name"]

        for metric_data in metrics_data:
            metric_type: str = metric_data["metric_type"]
            identification: str = metric_data["identification"]
            metric_name: str = metric_data["metric_name"]

            if (
                metric_type
                and alarm_metric_type
                and identification
                and ci_id
                and metric_name
                and alarm_metric_name
                and agent
                and metric_type.lower() == alarm_metric_type.lower()
                and identification.lower() == ci_id.lower()
                and (metric_name.lower() == alarm_metric_name.lower() or metric_name == "*")
                and metric_data.get("source", "N/A") == agent
            ):
                metric_data = _ensure_no_empty_values_metric_data(metric_data)
                alarm["new_enrichment"] = _fill_alarm_data(alarm["new_enrichment"], metric_data)

                alarm["new_enrichment"]["actionable"] = _actionable_alarm_raise(alarm, metric_data)

                # Special cases
                if alarm["agent"]["name"] == "Stonebranch" and get_alarm_field(alarm, "severity") == 2:
                    alarm["new_enrichment"].pop("actionable")
                elif alarm["agent"]["name"] == "StruxureWare":
                    additional_data: dict = get_alarm_field(alarm, "additional_data")
                    event_name: str = additional_data.get("event_name", "") if additional_data else ""
                    if event_name.upper() == "SYSTEM - MINOR ALARM BATTERY TEST":
                        alarm["new_enrichment"]["actionable"] = False
                    elif event_name.upper() in [
                        "VOLTAGE - BATTERY VOLTAGE",
                        "VOLTAGE - LOAD VOLTAGE",
                        "BATTERY - TIME BEFORE SHUTDOWN",
                    ] and _has_struxureware_battery_test_alarm(session, ci_id=ci_id):
                        alarm["new_enrichment"]["actionable"] = False
                elif alarm["agent"]["name"] == "NetAct":
                    # NetAct alarms with code 8888 are non-actionable if Network_Alias=AN-L10-015
                    if (
                        alarm_metric_name.startswith("8888")
                        and alarm["new_enrichment"]["location_attribute"] == "AN-L10-015"
                    ):
                        alarm["new_enrichment"]["actionable"] = False

                alarm["new_enrichment"]["metric_enrichment"] = True

                break
        else:
            alarm["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
            alarm["process_error"] = str("No metric found.")
    except Exception as exc:
        logging.exception(exc)
        alarm["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
        alarm["process_error"] = str(exc)
        elastic_apm.capture_exception()
    return alarm


def _ensure_no_empty_values_metric_data(metric_data: dict) -> dict:
    """Ensure that possible wrong values in the boolean fields are set to False.

    Parameters
    ----------
    metric_data : dict
        The metric information fetch from the uCMDB view.

    Returns
    -------
    dict
        The given metric info were boolean fields where falsey values are set to False.
    """
    if not metric_data["parent_impact"]:
        metric_data["parent_impact"] = False
    if not metric_data["top_level_impact"]:
        metric_data["top_level_impact"] = False
    return metric_data


def _actionable_alarm_raise(alarm: dict, metric_data: dict) -> bool | None:
    """Update the actionable_alarm field of the alarm if needed.

    Parameters
    ----------
    alarm : dict
        The alarm.
    metric_data : dict
        The metric data from uCMDB corresponding to the alarm.

    Returns
    -------
    bool | None
        The updated actionable_alarm or None.
    """
    if not (actionable_alarm_ := alarm["new_enrichment"]["actionable"]):
        # Actionable alarm = False or None
        return actionable_alarm_

    if actionable_threshold := static.ACTIONABLE_ALARM_RAISE_DICT.get(metric_data["actionable_alarm_raise"], None):
        # Actionable alarm = True and threshold exists.
        return True if get_alarm_field(alarm, "severity", alarm["new_enrichment"]) >= actionable_threshold else False

    # Actionable alarm = True and threshold does not exist.
    return actionable_alarm_


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _topology_enrichment(alarm: dict, topologies_data: list[dict[str, Any]]) -> dict:
    """Perform the topology enrichment which consist in adding the top level.

    Parameters
    ----------
    alarm : dict
        The alarm
    topologies_data : list[dict[str, Any]]
        The topologies data available for the list of the alarms of the current run.

    Returns
    -------
    dict
        The topology enriched alarm.
    """
    try:
        skip_enrichment = False

        if alarm["agent"]["topology_enrichment"] is False:
            skip_enrichment = True

        ci_id: str = alarm["ci_id"]

        if alarm["agent"]["name"] == "CA_Spectrum":
            # Hirshmann devices (tunnel Antwerp Central) and datalines (Dt.R/St.R) don't have ucmdb CI's
            ci_id_lower = ci_id.lower()
            if ci_id_lower.startswith("antce-") or ci_id_lower.startswith("dt.r") or ci_id_lower.startswith("st.r"):
                skip_enrichment = True

        if skip_enrichment:
            if "new_enrichment" in alarm:
                alarm["new_enrichment"]["topology_enrichment"] = False
            return alarm

        applications = set()
        environments = set()
        dashboards = set()
        current_critical_confirmed: int | None = None

        for topology in topologies_data:
            if ci_id and (topology["ci_identification"] and ci_id.lower() == topology["ci_identification"].lower()):
                if topology["application"]:
                    applications.add(topology["application"])
                if topology["environment"]:
                    environments.add(topology["environment"])
                if topology["dashboard"]:
                    dashboards.add(topology["dashboard"])
                if topology["sub_service"] and topology["service"]:
                    if (
                        current_critical_confirmed is None
                        or (topology.get("critical_confirmed", 0) or 0) > current_critical_confirmed
                    ):
                        alarm["new_enrichment"]["main_service"] = topology["service"]
                        alarm["new_enrichment"]["main_sub_service"] = topology["sub_service_code"]
                        current_critical_confirmed = topology.get("critical_confirmed", 0)

        alarm["new_enrichment"]["top_level"] = ",".join(applications) if applications else None
        alarm["new_enrichment"]["linked_environments"] = ",".join(environments) if environments else None
        alarm["new_enrichment"]["dashboards"] = ",".join(dashboards) if dashboards else None

    except Exception as exc:
        logging.exception(exc)
        alarm["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
        alarm["process_error"] = str(exc)
        elastic_apm.capture_exception()
    return alarm


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _parallelize_process(arguments: Sequence, process: Callable) -> list:
    """Allow any process to be parallelized.

    If first argument in arguments is a tuple given the tuple values as input for function calls else perform the calls
    with each element in the arguments as parameters.

    Notes
    -----
    The if arguments is to avoid having to check if arguments is None before each call of this function.

    Parameters
    ----------
    arguments : Sequence
        The sequence containing the arguments for the function calls.
    process : Callable
        The function to parallelize.

    Returns
    -------
    list
        If arguments is not None or empty return the list of returned values from the function calls.
        Return an empty list if no arguments were passed.
    """
    if arguments:
        logging.debug(f"Number of object to be parallelized: {len(arguments)}")
        if type(arguments[0]) is not tuple:
            with concurrent.futures.ThreadPoolExecutor(config.thread_number) as executor:
                result = executor.map(process, arguments)
        else:
            with concurrent.futures.ThreadPoolExecutor(config.thread_number) as executor:
                result = executor.map(lambda args: process(*args), arguments)
        return list(result)

    return []


def _compute_enrichment_status(alarms: list[dict], enriched_alarms: list[dict]) -> None:
    """Compute the enrichment status of the alarms."""
    sorted_enriched = sorted(enriched_alarms, key=lambda enriched_alarm: enriched_alarm["id"])
    data_keys = [elem["id"] for elem in sorted_enriched]

    for alarm in alarms:
        idx = bisect_left(data_keys, alarm["id"])
        if data_keys[idx] != alarm["id"]:
            # Equivalent to "not found".
            alarm["process_status"] = enums.AlarmJobStatus.NOT_DONE.value
        else:
            alarm["process_status"] = (
                sorted_enriched[idx].get("process_status", enums.AlarmJobStatus.DONE.value)
                or enums.AlarmJobStatus.DONE.value
            )
            alarm["process_error"] = sorted_enriched[idx].get("process_error", None)
            if alarm["process_status"] == enums.AlarmJobStatus.DONE.value and not (
                alarm["ci_id"]
                and alarm["metric_name"]
                and alarm["ci_id"].upper() not in ["N/A", "NONE"]
                and alarm["metric_name"].upper() not in ["N/A", "NONE"]
                and (
                    not alarm["agent"]["ci_enrichment"]
                    or (
                        sorted_enriched[idx]["new_enrichment"]["floc_id"]
                        and sorted_enriched[idx]["new_enrichment"]["floc_id"].upper() not in ["N/A", "NONE"]
                    )
                )
            ):
                alarm["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
                alarm["process_error"] = "Missing CI information"

    return


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _enriched_alarms_to_enrichments(enriched_alarms: list[dict]) -> list[dict]:
    """Extract the enrichments from the enriched alarms."""
    try:
        for enriched_alarm in enriched_alarms:
            # Populate the enrichment's alarm ID.
            if (
                "new_enrichment" in enriched_alarm
                and enriched_alarm["process_status"] == enums.AlarmJobStatus.DONE.value
            ):
                enriched_alarm["new_enrichment"]["alarm_id"] = enriched_alarm["id"]
                enriched_alarm["new_enrichment"]["enrichment_time"] = datetime.now(timezone.utc)

        return [
            enriched_alarm["new_enrichment"]
            for enriched_alarm in enriched_alarms
            if "new_enrichment" in enriched_alarm
            and enriched_alarm["process_status"] == enums.AlarmJobStatus.DONE.value
        ]
    except Exception as exc:
        _update_alarm_process(enriched_alarms, exc)
        elastic_apm.capture_exception()
        raise exc


def _update_alarm_process(alarms, exc):
    """Update all alarm's process_status and process_error.

    This is only done in case the process_status is not NOT_DONE.
    """
    for alarm in alarms:
        if alarm["process_status"] != enums.AlarmJobStatus.NOT_DONE.value:
            alarm["process_status"] = enums.AlarmJobStatus.IN_ERROR.value
            alarm["process_error"] = str(exc)


def _get_main_service(enriched_alarm: dict) -> dict:
    """Get the main service and sub-service for an alarm."""
    # Is there a sub-service linked to the metric? If so, this is the main sub-service.
    if enriched_alarm["process_status"] in [enums.AlarmJobStatus.NOT_DONE.value, enums.AlarmJobStatus.IN_ERROR.value]:
        return enriched_alarm

    if enriched_alarm["new_enrichment"].get("metric_service", "") and enriched_alarm["new_enrichment"].get(
        "metric_sub_service", ""
    ):
        enriched_alarm["new_enrichment"]["main_service"] = enriched_alarm["new_enrichment"]["metric_service"]
        enriched_alarm["new_enrichment"]["main_sub_service"] = enriched_alarm["new_enrichment"]["metric_sub_service"]
        return enriched_alarm

    return enriched_alarm


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _construct_cis_without_floc_id(ci_ids: list[str], cis_data: list[dict]) -> list[str]:
    """Construct the list of CIs without floc IDs, for metric and topology enrichments."""
    cis_without_floc_id: list[str] = []

    # Case 1: CI in cis_data -> isolate those without floc ID.
    cis_without_floc_id.extend(ci_data["identification"] for ci_data in cis_data if not ci_data["floc_id"])

    # Case 2: CI not in cis_data
    cis_without_floc_id.extend(
        ci for ci in ci_ids if ci.lower() not in [ci_data["identification"].lower() for ci_data in cis_data]
    )

    return cis_without_floc_id


def _has_struxureware_battery_test_alarm(session: Session, ci_id: str) -> bool:
    """Determine if the CI has an active battery test alarm."""
    if not (
        alarm := Alarm.get_active_alarm_on_metric(
            session=session,
            ci_id=ci_id,
            metric_type="/HardwareEvent/",
            metric_name="SYSTEM - MINOR",
            agent_name="StruxureWare",
        )
    ):
        return False

    select_occurrence = select(Occurrence).where(Occurrence.id == alarm.last_problem_id)
    if not (occurrence := session.execute(select_occurrence).scalar()):
        return False

    additional_data: dict[str, Any] = occurrence.additional_data or {}
    if additional_data.get("event_name", "").upper() == "SYSTEM - MINOR ALARM BATTERY TEST":
        return True

    return False
