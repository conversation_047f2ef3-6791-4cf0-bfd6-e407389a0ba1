# A2110-Olympus
Olympus monitoring project.

[![Imports: isort](https://img.shields.io/badge/%20imports-isort-%231674b1?style=flat)](https://pycqa.github.io/isort/)  
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## System requirements
A2110 Olympus requires [Python 3.11+](https://www.python.org/downloads/) and [Poetry 1.2.2](https://python-poetry.org/docs/).

##### Tip: The recommended IDE is [VSCode](https://code.visualstudio.com/). A `.vscode` directory is provided with a file containing recommended extensions alongside default launch configurations and workspace specific settings.

## What's in the box?
This project contains multiple smaller projects and includes an entrypoint for each of the smaller projects.
Currently, only one pyproject.toml is included and all dependencies are always installed. This could be improved if needed, 
but for now a lot of the dependencies are shared anyways.

A pipeline is run for each commit to a PR (targeting main), this pipeline runs all linters and tests for all projects. Once again, this could be finetunes in a later step, but for now it's fine to just run everything at once. The fact that we have a self-hosted agent which retains caching will speed up the pipelines by a lot (compared to running azure hosted agents).

A script is included to run each project separately. A bit like Django's `manage.py`. Refer to `src/a2110_olympus/run.py`

Each service has it's own python package located under `src`. Their tests are either a python file with the package's name (eg: test_mon_cnms.py for mon_cnms) or a package. Either way, these tests will be located under `tests`.

The `.openshift/` folder contains the required openshift files for the deployment of a certain service. The release pipeline should include a step after the single build step for each subdirectory in `.openshift` that should be deployed.

### Services
This monolithic repository contains multiple python packages. A package is considered a service in case it is a python 
package under `src/` that has a main module which defines a main function.

### Entrypoints
#### Runservice
This module runs other services dynamically using the provided `run.py` as a CLI.
For deployed instances of this entrypoint, use the environment variable OLYMPUS_SERVICE_NAME
For local instances of this entrypoint, use `poetry run runservice myservice` where `myservice` is the name of the 
subdirectory in `src/`. (e.g.: "enrichment_client").

#### Updatelaunch
This module updates `.vscode/launch.json` with a launch configuration for each service in `src/`. Useful when adding a new service.

#### Updatepackages
This module updates `pyproject.toml` with an entry in `tool.poetry.packages` for each service in `src/`. Useful when adding a new service.
Do note that it is not possible to run this script to remove packages. This is because poetry will fail if it can not find an entry in pyproject's packages.

#### Updatefilebrowser
This module updates `.openshift/filebrowser/03-deployment.yml` with all configured persitent-volume-claims that are found in `.openshift`.

#### Generatedotenv
This module generates a .env file for each service that defines a `config` module. If a `.env` already exists, missing keys are added and superfluous keys are commented. Useful to run services locally as the `runservice` entrypoint will load the `.env` from the service's directory if it exists, otherwise the .env from the root is loaded, if that doesn't exist, the current working directory is used to find the `.env`. See `olympus_common.utils.load_dotenv_from_service` for the exact implementation.

## Install the project
Navigate to this file's parent directory and run `poetry install`.

## Run the project
Running the project is done through the single entrypoint `src/a2110-olympus/run.py`.  
If `OLYMPUS_SERVICE_NAME` is set in the environment, any arguments passed to the entrypoint are ignored.
- Using poetry:  
`poetry run runservice myservice`
- Using python:  
`python -m a2110_olympus.run myservice`
- Using any of the above methods with environment variables (unix-style)
`OLYMPUS_SERVICE_NAME="myservice" python -m a2110_olympus.run`  
`OLYMPUS_SERVICE_NAME="myservice" poetry run runservice`  
###### Note: myservice could be replaced by any of the supported services. (eg: mon_zabbix, ai_reactivity_client, ...)

### Local
Ensure a .env file exists in `src/myservice/` or at the root of this repository.
`poetry run runservice myservice`

### Deployed (Openshift)
Ensure the folder `.openshift/myservice` contains the required openshift files.
Add a DEV step to the release pipeline (Can use an existing DEV copy, remember to update the pipeline variables for this new step).
Make sure OLYMPUS_SERVICE_NAME is set in the config-map.

## Poe tasks
A2110-Olympus uses poethepoet as a task runner as is works well with poetry.  
The tasks are defined in `pyproject.toml`. Some tasks are subtasks as they are used in other tasks as a sequence.  
Poe tasks are run as follows: `poetry run poe taskname`  
The most used tasks are:
- `update` -> Upgrade pip, then dependencies withing their bounds.
- `upgrade` -> Upgrade pip, then the latest version of all dependencies.
- `format` -> Format the code (Using black and isort) (src/ and tests/)
- `lint` -> Run all configured linters (black, isort, pydocstyle, flake8, mypy, bandit)
- `test` -> Test the code using pytest and configured plugins (cov and mock)
- `clean-git-branches` -> Remove the local branches that were pushed to a remote, but no longer exist on that remote.

## Repositories skipped
- ing-ca-spectrum: Managed outside Olympus scope, remains [a separate repo](https://dev.azure.com/INFRABEL/a2110-olympus/_git/ing-ca-spectrum).
- mail-monitoring-gateway: Managed under [a different a-code](https://dev.azure.com/INFRABEL/a1617-monitoringgateways/_git/mail-monitoring-gateway)
- mon-openshift: [Initial PR is still in review](https://dev.azure.com/INFRABEL/a2110-olympus/_git/mon-openshift/pullrequest/15695).
- monitoring-ingestion: old repo, should be disabled.
- mqtt-gateway: old repo, should be disabled.
- ms-defender: Repo is quite old, is deployed on another namespace, not sure this should be included. Skipping for now.
- sap-client: The project is still a WIP, no review done.