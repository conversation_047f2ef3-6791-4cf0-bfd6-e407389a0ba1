apiVersion: batch/v1
kind: CronJob
metadata:
  name: a2110-archiving-job-cronjob-#{appEnv}#
  namespace: a2110-olympus-monitoring
spec:
  schedule: "1 0 * * 3"
  concurrencyPolicy: "Replace"
  startingDeadlineSeconds: 300
  suspend: false
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 10
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: "a2110-archiving-job-app-#{appEnv}#"
        spec:
          volumes:
            - name: a2110-olympus-volume-claim-#{appEnv}#
              persistentVolumeClaim:
                claimName: a2110-olympus-volume-claim-#{appEnv}#
          containers:
            - name: a2110-archiving-job-app-#{appEnv}#
              image: "artifactory.msnet.railb.be/a2110-docker/a2110-olympus-image:#{releaseName}#"
              volumeMounts:
                - name: a2110-olympus-volume-claim-#{appEnv}#
                  mountPath: "/data"
                  subPath: "#{olympusServiceName}#/"
              envFrom:
                - configMapRef:
                    name: a2110-archiving-job-config-map-#{appEnv}#
                - secretRef:
                    name: a2110-archiving-job-secret-#{appEnv}#
              resources:
                limits:
                  memory: 4Gi
          restartPolicy: OnFailure
