"""Configuration module for the enrichment-client."""

import json
from dataclasses import dataclass

from olympus_common.config import BaseServiceConfig, DatabaseConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import <PERSON>ton, strtobool


@dataclass(frozen=True)
class OpticDatabaseConfig(DatabaseConfig, metaclass=Singleton):
    """Represent the configuration for the Optic database."""

    host: str = env_field("OPTIC_DB_HOST")
    port: int = env_field("OPTIC_DB_PORT", astype=int)
    name: str = env_field("OPTIC_DB_NAME")
    username: str = env_field("OPTIC_DB_USER")
    password: str = env_field("OPTIC_DB_PASSWORD")

    def to_conninfo(self) -> str:
        """Return a string representing the connection info for the postgres database."""
        return f"oracle+oracledb://{self.username}:{self.password}@{self.host}:{self.port}/?service_name={self.name}"


@dataclass(frozen=True)
class GraphConfig(metaclass=Singleton):
    """Represent the config for MS Graph API."""

    client_id: str = env_field("GRAPH_CLIENT_ID")
    client_secret: str = env_field("GRAPH_CLIENT_SECRET")
    tenant_id: str = env_field("GRAPH_TENANT_ID")

    sender: str = env_field("GRAPH_SENDER")
    recipients: list[str] = env_field("GRAPH_RECIPIENTS", astype=json.loads)
    cc_recipients: list[str] = env_field("GRAPH_CC_RECIPIENTS", astype=json.loads)


@dataclass(frozen=True)
class Config(BaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for the optic-matcher module."""

    optic_db_config: OpticDatabaseConfig = dataclass_field(OpticDatabaseConfig)
    backend_db_config: DatabaseConfig = dataclass_field(DatabaseConfig)
    must_do_import: bool = env_field("MUST_DO_IMPORT", astype=strtobool)
    must_do_match: bool = env_field("MUST_DO_MATCH", astype=strtobool)

    graph_config: GraphConfig = dataclass_field(GraphConfig)


config = Config()  # Create the singleton instance at import time.
