ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS asset_number;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS asset_tag;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS ci_friendly_name;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS ci_base_impact;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS ci_planned_out_of_service_end;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS ci_planned_out_of_service_release_reference;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS ci_planned_out_of_service_start;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS ci_sub_type;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS client_zone;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS documentation_path;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS environment;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS in_service_date;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS intervention_address;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS location_security;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS location_type;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS monitoring_status;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS network_alias;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS owner_company;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS responsible;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS retirement_date;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS subzone;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS zone;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS ci_sub_service;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS additional_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    RENAME location TO location_address;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    RENAME location_attributes TO location_attribute;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    RENAME documentation TO instructions;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    RENAME sub_groups TO subgroups;
	
ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN source text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN metric_category text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN actionable_alarm_raise text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN actionable_alarm_clear text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN warning_level_criteria text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN critical_level_criteria text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN action_class text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN actions_enabled boolean;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN metric_description text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN metric_short_name text;

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_alarm_job_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_alarm_action_id_seq
    OWNER TO #{DbUser}#;
    
CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_alarm_job
(
    id bigint NOT NULL DEFAULT nextval('s2110_alarm_job_id_seq'::regclass),
    alarm_id bigint NOT NULL,
    job_name text COLLATE pg_catalog."default",
    job_status smallint,
    job_error text COLLATE pg_catalog."default",
    job_retries bigint,
    job_time timestamp without time zone,
    CONSTRAINT s2110_alarm_job_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_job
    OWNER to #{DbUser}#;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_job
    ADD CONSTRAINT s2110_alarm_job_alarm_fk FOREIGN KEY (alarm_id)
    REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_job_alarm_fk_i
    ON #{Schema}#.s2110_alarm_job USING btree
    (alarm_id ASC)
    TABLESPACE s2110_index;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_job
    ALTER COLUMN job_name SET NOT NULL;
    
CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_alarm_enrichment_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
	
CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_alarm_enrichment
(
    id bigint NOT NULL DEFAULT nextval('s2110_alarm_enrichment_id_seq'::regclass),
    alarm_id bigint NOT NULL,
    enrichment_id bigint NOT NULL,
    last_enrichment boolean,
    CONSTRAINT s2110_alarm_enrichment_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_enrichment
    OWNER to #{DbUser}#;    
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_enrichment
    ADD CONSTRAINT s2110_alarm_enrichment_alarm_fk FOREIGN KEY (alarm_id)
    REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_enrichment_alarm_fk_i
    ON #{Schema}#.s2110_alarm_enrichment USING btree
    (alarm_id ASC)
    TABLESPACE s2110_index;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_enrichment
    ADD CONSTRAINT s2110_alarm_enrichment_enrichment_fk FOREIGN KEY (enrichment_id)
    REFERENCES #{Schema}#.s2110_enrichment (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_enrichment_enrichment_fk_i
    ON #{Schema}#.s2110_alarm_enrichment USING btree
    (enrichment_id ASC)
    TABLESPACE s2110_index;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    RENAME scope_ref TO action_class;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_agent
    RENAME scope_ref TO action_class;
    
INSERT INTO s2110_alarm_job 
 SELECT nextval('s2110_alarm_job_id_seq'), alarm.id AS alarm_id, 'enrichment' AS job_name
      , alarm.enrichment_status AS job_status, alarm.process_error AS job_error
	  , alarm.nb_retries AS job_retries, NULL AS job_time
   FROM s2110_alarm alarm;
	
INSERT INTO s2110_alarm_job 
 SELECT nextval('s2110_alarm_job_id_seq'), alarm.id AS alarm_id, 'ui_sending' AS job_name
      , alarm.ui_sending_status AS job_status, alarm.process_error AS job_error
	  , alarm.nb_retries AS job_retries, NULL AS job_time
   FROM s2110_alarm alarm;

INSERT INTO s2110_alarm_enrichment
 SELECT nextval('s2110_alarm_enrichment_id_seq'), alarm.id AS alarm_id, enrichment.id AS enrichment_id, True AS last_enrichment
   FROM s2110_alarm alarm
   JOIN s2110_enrichment enrichment
     ON alarm.last_enrichment_id = enrichment.id;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm DROP CONSTRAINT IF EXISTS s2110_alarm_enrichment_fk;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm DROP COLUMN IF EXISTS last_enrichment_id;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm DROP COLUMN IF EXISTS enrichment_status;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm DROP COLUMN IF EXISTS ui_sending_status;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm DROP COLUMN IF EXISTS process_error;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm DROP COLUMN IF EXISTS nb_retries;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment ADD COLUMN location_type text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    ADD COLUMN closing_time timestamp without time zone;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm DROP COLUMN IF EXISTS last_alarm_incident_id;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN "dashboardName" text;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN metric_service text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN metric_sub_service text;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_incident
    ADD COLUMN air_status text;
    
ALTER TABLE #{Schema}#.s2110_incident
    ALTER COLUMN sap_id TYPE text;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS address;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS actionable_alarm_raise;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS actionable_alarm_clear;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS warning_level_criteria;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS critical_level_criteria;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment DROP COLUMN IF EXISTS parent_impact;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_job
    ADD COLUMN nb_success bigint;

update s2110_alarm_job set nb_success = 1 where job_status = 1;

update s2110_alarm_job set nb_success = 0 where job_status != 1;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN main_service text;

ALTER TABLE IF EXISTS #{Schema}#.s2110_enrichment
    ADD COLUMN main_sub_service text;
    
