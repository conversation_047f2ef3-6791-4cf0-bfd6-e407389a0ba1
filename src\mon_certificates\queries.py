"""Queries module for mon-certificates.

This file contains the elastic queries.
"""

ES_QUERY = {
    "bool": {
        "must": [],
        "filter": [
            {"range": {"last_update": {"gte": "now-24h", "lt": "now"}}},
            {
                "bool": {
                    "should": [
                        {"match_phrase": {"x509.issuer.common_name.keyword": "talos"}},
                        {"match_phrase": {"x509.issuer.common_name.keyword": "railb.be.nacadca"}},
                    ],
                    "minimum_should_match": 1,
                }
            },
        ],
        "should": [],
        "must_not": [
            {
                "bool": {
                    "should": [
                        {"wildcard": {"x509.subject.common_name": "po*"}},
                        {"wildcard": {"x509.subject.common_name": "pc*"}},
                        {"wildcard": {"x509.subject.common_name": "tb*"}},
                        {"wildcard": {"x509.subject.common_name": "vda*"}},
                        {"wildcard": {"x509.subject.common_name": "px*"}},
                        {"wildcard": {"x509.subject.common_name.keyword": "canari-*"}},
                        {"wildcard": {"x509.subject.common_name.keyword": "DX-DICA-*"}},
                        {"wildcard": {"x509.subject.common_name.keyword": "DESKTOP-*"}},
                    ]
                }
            },
            {"match_phrase": {"x509.subject.common_name.keyword": ""}},
            {"match_phrase": {"x509.subject.common_name.keyword": " "}},
        ],
    }
}

ES_SORT_QUERY = {"last_update": {"order": "asc", "format": "strict_date_optional_time_nanos"}}, {"_shard_doc": "desc"}
