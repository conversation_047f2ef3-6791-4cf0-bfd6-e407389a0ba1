# Azure devops folder for A2110-Olympus

## Usage
The files azure-pipelines-*.yml are used as a CI/CD tool in Azure Pipelines.
The check pipeline does not trigger on CI, but should be configured as a build-validation on the `main` branch.

## Information

All pipelines use the internal NON-PROD agent. Using the internal agent is required to be able to access internal resources like artifactory. Another advantage is that poetry is already installed and by allowing poetry to create virtual environments, the `poetry install` command will be faster than when using Microsoft-hosted agents.

The stages defined in the check pipeline:
1. Lint and Test (2 stages that run in parallel. Lint and Test the code.)
