"""Jobs called by the archiving job."""

import logging

from dateutil.relativedelta import relativedelta
from sqlalchemy import create_engine
from sqlalchemy.sql import text

from archiving_job.config import config
from olympus_common import utils


def archive_alarms() -> None:
    """Match Olympus alarms and Optic alarms."""
    connect_args = {
        "options": f"-c DateStyle=ISO -csearch_path={config.db_config.schema}",
        "application_name": config.service_name,
    }
    engine = create_engine(config.db_config.to_conninfo(), connect_args=connect_args, echo=False)

    date_threshold = utils.now_naive() + relativedelta(
        weeks=-config.nb_weeks_threshold, hour=0, minute=0, second=0, microsecond=0
    )
    optic_matcher_threshold = utils.now_naive() + relativedelta(months=-1, hour=0, minute=0, second=0, microsecond=0)
    params = {"min_date": date_threshold}

    with engine.begin() as connection:
        try:
            # Inserts
            statement = text(
                """INSERT INTO s2110_alarm_arc
                   SELECT *
                     FROM s2110_alarm WHERE NOT is_active AND closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """INSERT INTO s2110_occurrence_arc
                   SELECT oc.*
                    FROM s2110_occurrence oc
                    JOIN s2110_alarm al
                    ON oc.alarm_id = al.id
                    WHERE NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """INSERT INTO s2110_alarm_action_arc
                SELECT aa.*
                    FROM s2110_alarm_action aa
                    JOIN s2110_alarm al
                    ON aa.alarm_id = al.id
                    WHERE NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """INSERT INTO s2110_alarm_enrichment_arc
                SELECT ae.*
                    FROM s2110_alarm_enrichment ae
                    JOIN s2110_alarm al
                    ON ae.alarm_id = al.id
                    WHERE NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """INSERT INTO s2110_alarm_incident_arc
                SELECT ai.*
                    FROM s2110_alarm_incident ai
                    JOIN s2110_alarm al
                    ON ai.alarm_id = al.id
                    WHERE NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """INSERT INTO s2110_alarm_job_arc
                SELECT aj.*
                    FROM s2110_alarm_job aj
                    JOIN s2110_alarm al
                    ON aj.alarm_id = al.id
                    WHERE NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """INSERT INTO s2110_alarm_release_arc
                SELECT ar.*
                    FROM s2110_alarm_release ar
                    JOIN s2110_alarm al
                    ON ar.alarm_id = al.id
                    WHERE NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """INSERT INTO s2110_enrichment_arc
                SELECT DISTINCT e.*
                    FROM s2110_enrichment e
                    JOIN s2110_alarm_enrichment ae
                    ON e.id = ae.enrichment_id
                    JOIN s2110_alarm al
                    ON ae.alarm_id = al.id
                    WHERE NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """INSERT INTO s2110_user_action_arc
                SELECT DISTINCT ua.*
                    FROM s2110_user_action ua
                    JOIN s2110_alarm_action aa
                    ON ua.id = aa.action_id
                    JOIN s2110_alarm al
                    ON aa.alarm_id = al.id
                    WHERE NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            # Deletes

            statement = text(
                """UPDATE s2110_alarm al
                    SET last_problem_id = NULL, last_occurrence_id = NULL, last_clear_id = NULL
                    WHERE NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """DELETE
                    FROM s2110_occurrence oc
                    USING s2110_alarm al
                    WHERE oc.alarm_id = al.id
                    AND NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """DELETE
                    FROM s2110_alarm_action aa
                    USING s2110_alarm al
                    WHERE aa.alarm_id = al.id
                    AND NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """DELETE
                    FROM s2110_user_action ua_delete
                    USING s2110_user_action ua
                    LEFT JOIN s2110_alarm_action aa
                    ON ua.id = aa.action_id
                    WHERE ua_delete.id = ua.id
                    AND aa.id IS NULL"""
            )

            connection.execute(statement)

            statement = text(
                """DELETE
                    FROM s2110_alarm_enrichment ae
                    USING s2110_alarm al
                    WHERE ae.alarm_id = al.id
                    AND NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """DELETE
                    FROM s2110_enrichment e_delete
                    USING s2110_enrichment e
                    LEFT JOIN s2110_alarm_enrichment ae
                    ON e.id = ae.enrichment_id
                    WHERE e_delete.id = e.id
                    AND ae.id IS NULL"""
            )

            connection.execute(statement)

            statement = text(
                """DELETE
                    FROM s2110_alarm_incident ai
                    USING s2110_alarm al
                    WHERE ai.alarm_id = al.id
                    AND NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """DELETE
                    FROM s2110_alarm_job aj
                    USING s2110_alarm al
                    WHERE aj.alarm_id = al.id
                    AND NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """DELETE
                    FROM s2110_alarm_release ar
                    USING s2110_alarm al
                    WHERE ar.alarm_id = al.id
                    AND NOT al.is_active AND al.closing_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """DELETE
                    FROM s2110_alarm
                    WHERE NOT is_active AND closing_time < :min_date"""
            )

            connection.execute(statement, params)

            params = {"min_date": optic_matcher_threshold}
            statement = text(
                """DELETE
                    FROM s2110_matched_alarm
                    WHERE match_time < :min_date"""
            )

            connection.execute(statement, params)

            statement = text(
                """DELETE
                    FROM s2110_optic_alarm oa_delete
                    USING s2110_optic_alarm oa
                    LEFT JOIN s2110_matched_alarm ma
                    ON oa.id = ma.optic_alarm_id
                    WHERE oa_delete.id = oa.id
                    AND oa_delete.raisetime < :min_date
                    AND ma.id IS NULL"""
            )

            connection.execute(statement, params)

            statement = text(
                """DELETE
                    FROM s2110_occurrence
                    WHERE event_type = 'heartbeat'
                    AND raise_time < :min_date"""
            )

            connection.execute(statement, params)

            connection.commit()
        except Exception as exc:
            logging.exception(f"Error {str(exc)} in statement: {str(statement)}")
            connection.rollback()
            raise Exception from exc
        finally:
            connection.close()
