import re

import pytest

from olympus_common import exceptions


def test_missingkeyerror():
    """Test that the first argument passed to the exception is used to format the base_str."""
    arg = "dummy"
    match_str = exceptions.MissingKeyError.base_str.format(e=arg)
    escaped_match_str = re.escape(match_str)
    with pytest.raises(exceptions.Missing<PERSON>eyError, match=escaped_match_str):
        raise exceptions.MissingKeyError(arg)


def test_missingkeyerror_no_args():
    """Test that a MissingKeyException raised without args matches an empty string."""
    with pytest.raises(exceptions.Missing<PERSON>eyError, match=""):
        raise exceptions.MissingKeyError()
