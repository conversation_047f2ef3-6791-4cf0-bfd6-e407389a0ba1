"""Main entrypoint of the Olympus project.

This is a cli capable of running each service based on the module-name provided.
"""

import argparse
import importlib
import os

from olympus_common.utils import load_dotenv_from_service


def main() -> None:
    """Run the service based on the provided arguments.

    If OLYMPUS_SERVICE_NAME is set in the environment, it will take precedence over any passed arguments.

    A supported service has a main module with a main function.

    Examples
    --------
    `python -m a2110_olympus.run mon_cnms` will find `mon_cnms.main:main`. If this does not exist, the command fails.

    Raises
    ------
    Exception
        If a valid name cannot be found after checking env:OLYMPUS_SERVICE_NAME or the first argument.
    """
    name = os.environ.get("OLYMPUS_SERVICE_NAME")
    if not name:
        # Create parser and add name argument.
        parser = argparse.ArgumentParser()
        parser.add_argument("name", help="Name of the service to run (e.g.: mon_zabbix, mon_cnms).")

        # Get options
        args = parser.parse_args()
        name = args.name

    if not name:
        raise Exception("Could not find a valid name after checking env:OLYMPUS_SERVICE_NAME or first argument.")

    load_dotenv_from_service(name)

    # Get the main function from the provided name
    try:
        module = importlib.import_module(f"{name}.main")
        runner = module.main
    except (AttributeError, ModuleNotFoundError) as exc:
        raise Exception(f"Could not find `{name}.main:main`") from exc

    # Call the runner
    runner()


if __name__ == "__main__":
    main()
