-- Sequences
CREATE SEQUENCE #{Schema}#.seq_s2110_release
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

CREATE SEQUENCE #{Schema}#.seq_s2110_alarm_release
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

CREATE SEQUENCE #{Schema}#.seq_s2110_release_ci
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

-- Tables
CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_release
(
    id bigint NOT NULL DEFAULT nextval('seq_s2110_release'::regclass),
    sap_id text COLLATE pg_catalog."default",
    external_id text COLLATE pg_catalog."default",
    comment text COLLATE pg_catalog."default",
    start_time timestamp without time zone,
    end_time timestamp without time zone,
    sap_status text COLLATE pg_catalog."default",
    CONSTRAINT s2110_release_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_alarm_release
(
    id bigint NOT NULL DEFAULT nextval('seq_s2110_alarm_release'::regclass),
    alarm_id bigint,
    release_id bigint,
    link_time timestamp without time zone,
    is_link_active boolean DEFAULT true,
    CONSTRAINT s2110_alarm_release_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index,
    CONSTRAINT fk_alarm_id FOREIGN KEY (alarm_id)
        REFERENCES s2110_alarm (id),
    CONSTRAINT fk_release_id FOREIGN KEY (release_id)
        REFERENCES s2110_release (id)
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_release_ci
(
    id bigint NOT NULL DEFAULT nextval('seq_s2110_release_ci'::regclass),
    release_id bigint,
    ci_id text COLLATE pg_catalog."default",
    floc_id text COLLATE pg_catalog."default",
    CONSTRAINT s2110_release_ci_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index,
    CONSTRAINT fk_release_id FOREIGN KEY (release_id)
        REFERENCES s2110_release (id)
)
TABLESPACE s2110_data;

-- Table ownership
ALTER TABLE IF EXISTS s2110_release
    OWNER to #{DbUser}#;
ALTER TABLE IF EXISTS s2110_alarm_release
    OWNER to #{DbUser}#;
ALTER TABLE IF EXISTS s2110_release_ci
    OWNER to #{DbUser}#;

-- Indexes

CREATE INDEX IF NOT EXISTS s2110_idx_fk_alarm_release_to_alarm
    ON s2110_alarm_release USING btree
    (alarm_id ASC)
    TABLESPACE s2110_index;

CREATE INDEX IF NOT EXISTS s2110_idx_fk_alarm_release_to_release
    ON s2110_alarm_release USING btree
    (release_id ASC)
    TABLESPACE s2110_index;

CREATE INDEX IF NOT EXISTS s2110_idx_fk_release_ci_to_release
    ON s2110_release_ci USING btree
    (release_id ASC)
    TABLESPACE s2110_index;