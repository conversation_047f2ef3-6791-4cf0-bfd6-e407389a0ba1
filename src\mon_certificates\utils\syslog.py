"""Syslog module for mon-certificates.

This module is used for the heartbeat implementation in this project.

Notes
-----
The actual solution is not implemented in uCMDB/OPTIC and should be done (29/11/2023).
Another monitoring of this script could be done using <PERSON><PERSON><PERSON> as the orchestrator of this script but this solution
need to be discussed and prioritized by the project leaders (<PERSON>,<PERSON>).
"""

import logging
from datetime import datetime
from logging.handlers import SysLogHandler

from mon_certificates.config import config
from mon_certificates.static import CHANNEL, SYSLOG_HITS_FILENAME, SYSLOG_LOGGER_NAME


def heartbeat() -> None:
    """Send a heartbeat message to a distant syslog.

    Create a heartbeat message with the given information.
    Then send a message into the distant syslog corresponding to the given information.
    """
    create_syslog_logger()
    syslog_logger = logging.getLogger(SYSLOG_LOGGER_NAME)
    ci_id = "MON-CERTIFICATES_HB"
    metric = "Heartbeat"
    timestamp = datetime.strftime(datetime.now().replace(microsecond=0), "%Y/%m/%d %H:%M:%S")
    message = "heart is beating : Certificates monitoring OK"
    hb_syslog = f"{config.syslog_tag} {CHANNEL} {ci_id} {config.environment} {metric} {timestamp} : {message}"
    syslog_logger.error(hb_syslog)


def create_syslog_logger() -> None:
    """Create a logger to send events to a distant  with the defined format.

    Notes
    -----
    To keep a track of what was sent to the syslog, the logger also write the logs into a file.
    """
    logger = logging.getLogger(SYSLOG_LOGGER_NAME)
    if not logger.hasHandlers():
        logger.setLevel("INFO")
        syslog_handler = SysLogHandler(
            address=(config.syslog_host, config.syslog_port), facility=config.syslog_facility
        )
        file_handler = logging.FileHandler(config.logger_config.logs_folder / SYSLOG_HITS_FILENAME)
        syslog_formatter = logging.Formatter("mon-certificates %(message)s")
        file_formatter = logging.Formatter("%(asctime)s %(levelname)s: mon-certificates %(message)s")
        syslog_handler.setFormatter(syslog_formatter)
        file_handler.setFormatter(file_formatter)
        for handler in logger.handlers:
            logger.removeHandler(handler)
        logger.addHandler(syslog_handler)
        logger.addHandler(file_handler)
        logger.propagate = False
