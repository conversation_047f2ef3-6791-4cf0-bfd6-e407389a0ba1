"""Configuration module for mon-local6."""

from dataclasses import dataclass

from olympus_common.config import DatabaseKafkaConsumerServiceConfig
from olympus_common.dataclass import env_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseKafkaConsumerServiceConfig, metaclass=Singleton):
    """Represent the configuration for this DD."""

    agent_name: str = env_field("AGENT_NAME")


config = Config()  # Create the singleton instance at import time.
