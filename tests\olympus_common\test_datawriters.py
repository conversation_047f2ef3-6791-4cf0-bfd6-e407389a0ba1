import json
import tempfile
from pathlib import Path

from olympus_common.datawriters import LocalStorageWriter


def test_localstoragewriter():
    with tempfile.TemporaryDirectory() as tmpdir:
        tmpfile = Path(tmpdir) / "output.json"
        datawriter = LocalStorageWriter(tmpfile)
        dummy_data = [{"a": 1}]
        datawriter.success(dummy_data)
        assert tmpfile.read_text() == json.dumps(dummy_data)

        datawriter.error(dummy_data, Exception("Dummy"))  # No assertions required
