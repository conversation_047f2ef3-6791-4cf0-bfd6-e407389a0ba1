import tempfile
from pathlib import Path

import pandas as pd
import pytest
from pytest_mock import Mo<PERSON><PERSON>ix<PERSON>

from olympus_common import pd as olympus_pd


def dummy_df() -> pd.DataFrame:
    data = [{"a": 1}, {"b": 2}]
    df = pd.DataFrame(data)
    return df


def test_clean_raw_dataframe():
    """Test that the returned df is not empty, contains the expected keys and the columns were renamed."""
    df = dummy_df()
    columns_renaming = {"a": "a_renamed"}
    columns_to_keep = list(columns_renaming.keys())
    df_cleaned = olympus_pd.clean_raw_dataframe(df, columns_renaming, columns_to_keep)
    assert not df_cleaned.empty
    assert "a" not in df_cleaned
    assert "a_renamed" in df_cleaned
    assert "b" not in df_cleaned


def test_clean_raw_dataframe_no_columns_to_keep():
    """Test that when not providing columns_to_keep, columns_renaming.keys() is used."""
    df = dummy_df()
    columns_renaming = {"a": "a_renamed"}
    df_cleaned = olympus_pd.clean_raw_dataframe(df, columns_renaming)
    assert not df_cleaned.empty
    assert "a" not in df_cleaned
    assert "a_renamed" in df_cleaned
    assert "b" not in df_cleaned


@pytest.mark.parametrize(
    ("name", "expected_name"),
    [
        ("dummy.xlsx", "dummy.xlsx"),  # Provide extension, expect no change
        ("dummy", "dummy.xlsx"),  # Do not provide extension, expect to it to be added
        (
            "dummy.foo.bar",
            "dummy.foo.bar.xlsx",
        ),  # Do not provide extension, expect to it to be added and other extensions to be preserved
    ],
)
def test_df_to_excel(name: str, expected_name: str):
    """Test that an excel file is created and that it contains the original_df."""
    df = dummy_df()
    with tempfile.TemporaryDirectory() as tmpdir:
        tmpdirpath = Path(tmpdir)
        olympus_pd.df_to_excel(df, tmpdirpath, name=name)
        tmpfilepath = tmpdirpath / expected_name
        # Test that the excel file is created
        assert tmpfilepath.exists()
        # Test that reading the created excel file equals the original df.
        assert pd.read_excel(tmpfilepath).equals(df)


def test_tabulate_and_to_excel(mocker: MockerFixture):
    """Test that both tabulate and df_to_excel are called."""
    tabulate_patched = mocker.patch("olympus_common.pd.tabulate")
    df_to_excel_patched = mocker.patch("olympus_common.pd.df_to_excel")
    df = dummy_df()
    with tempfile.TemporaryDirectory() as tmpdir:
        tmpdirpath = Path(tmpdir)
        name = "output.xlsx"
        olympus_pd.tabulate_and_to_excel(df, tmpdirpath, name=name)
        assert tabulate_patched.call_count == 1  # Ensure tabulate is called
        assert df_to_excel_patched.call_count == 1  # Ensure df_to_excel is called
