"""Tests for the generate_env entrypoint."""

import tempfile
from dataclasses import dataclass
from pathlib import Path

from pytest_mock import Mocker<PERSON><PERSON>ture

from a2110_olympus.generate_env import _write_dotenv
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import <PERSON><PERSON>


def test_write_dotenv(mocker: MockerFixture) -> None:
    @dataclass(frozen=True)
    class Config(metaclass=Singleton):
        w: int = env_field("DUMMY_W", astype=int)
        x: str = env_field("DUMMY_X", default="dummyvalue")

    @dataclass(frozen=True)
    class UpdatedConfig(metaclass=Singleton):
        w: int = env_field("DUMMY_W", astype=int)
        y: int = env_field("DUMMY_Y", astype=int, default="5")

    configs = {"dummy": Config(w=1)}
    updated_configs = {"dummy": UpdatedConfig(w=1)}
    with tempfile.TemporaryDirectory() as tmpdir:
        src_path = _patch_src_and_root(mocker, tmpdir)
        _write_dotenv(configs, src=src_path)
        content_lines = (src_path / "dummy" / ".env").read_text().splitlines()
        assert content_lines == ["# DUMMY", 'DUMMY_W="0"', 'DUMMY_X="dummyvalue"']

        _write_dotenv(updated_configs, src=src_path)
        content_lines = (src_path / "dummy" / ".env").read_text().splitlines()
        assert content_lines == ["# DUMMY", 'DUMMY_W="0"', '# DUMMY_X="dummyvalue"', 'DUMMY_Y="5"']


def test_generate_dotenv(mocker: MockerFixture) -> None:
    """Test that generate_dotenv generates an entry for each env_field recursively."""

    @dataclass(frozen=True)
    class NestedDummyConfig(metaclass=Singleton):
        field_1: str = env_field("FIELD_1")

    @dataclass(frozen=True)
    class DummyConfig(metaclass=Singleton):
        foo: str = env_field("FOO", default="foo")  # Expect in .env with default value
        bar: int = env_field("BAR", astype=int, default="1")  # Expect in .env with default value as str
        baz: bool = False  # Do not expect in .env
        nested_config: NestedDummyConfig = dataclass_field(NestedDummyConfig)  # Expect all fields in .env

    expected_lines = ["# DUMMY", 'BAR="1"', 'FIELD_1="0"', 'FOO="foo"']
    NestedDummyConfig(field_1="field_1")  # instantiate to avoid the need to set FIELD_1 in the env.
    dummy_config = DummyConfig()

    with tempfile.TemporaryDirectory() as tmpdir:
        src_path = _patch_src_and_root(mocker, tmpdir)
        _write_dotenv({"dummy": dummy_config}, src=src_path)
        dotenv_path = src_path / "dummy" / ".env"
        assert dotenv_path.read_text().splitlines() == expected_lines


def _patch_src_and_root(mocker: MockerFixture, tmpdir: str):
    src_path = Path(tmpdir) / "root" / "src"
    mocker.patch("a2110_olympus.generate_env.ROOT").return_value = src_path.parent
    mocker.patch("a2110_olympus.generate_env.SRC_PATH").return_value = src_path
    return src_path
