"""Configuration module for mon-adva."""

from dataclasses import dataclass

from olympus_common.config import DatabaseKafkaConsumerServiceConfig
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseKafkaConsumerServiceConfig, metaclass=Singleton):
    """Represent the configuration for this DD.

    ADVA events contains some special characters that are not accepted in Excel file.
    so we cannot write outputs.
    """

    write_outputs: bool = False


config = Config()
