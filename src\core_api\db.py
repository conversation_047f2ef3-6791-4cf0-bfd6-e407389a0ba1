"""Module to interact with the Olympus database."""

from sqlalchemy import and_, func
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.sql.functions import coalesce

from core_api.config import config
from core_api.exceptions import NoFiltersProvidedError
from core_api.models import AlarmInfoCriteria, AlarmInfoResponse
from olympus_common import db


def get_alarm_info_response_from_criteria(criteria: AlarmInfoCriteria) -> list[AlarmInfoResponse]:
    """Get the alarm info response from the given criteria.

    This function also does session handling to ensure that the session is closed after the query is done.
    """
    session = db.create_session(config.service_name, echo=config.debug)
    alarms = _find_alarms_from_criteria(criteria, session)
    response = _add_required_response_fields(alarms)
    session.close()
    return response


def _find_alarms_from_criteria(criteria: AlarmInfoCriteria, session: Session) -> list[db.Alarm]:
    """Find alarms based on the given criteria.

    Notes
    -----
    We join AlarmEnrichment if there are any fields coming from different models than Alarm.
    This is done to cover the case where the alarm has multiple enrichments but we want just the last enrichment.

    The joinedloads are specified to avoid lazy loading when building the response. Since we know we need all the
    related tables for each alarm, it is more efficient to eagerly load every table.
    """
    query = session.query(db.Alarm).options(
        joinedload(db.Alarm.last_enrichment),
        joinedload(db.Alarm.last_occurrence),
        joinedload(db.Alarm.last_clear),
        joinedload(db.Alarm.last_problem),
        joinedload(db.Alarm.active_incident),
        joinedload(db.Alarm.agent),
        joinedload(db.Alarm.active_release),
    )
    criteria_model_dump = criteria.model_dump(exclude={"options"}, exclude_none=True)

    query = (
        query.join(
            db.AlarmEnrichment,
            and_(db.AlarmEnrichment.alarm_id == db.Alarm.id, db.AlarmEnrichment.last_enrichment == 1),
            isouter=True,
        )
        .join(db.Enrichment, db.Enrichment.id == db.AlarmEnrichment.enrichment_id, isouter=True)
        .join(
            db.Agent,
            db.Agent.id == db.Alarm.agent_id,
        )
    )

    filters = []

    for field_name, value in criteria_model_dump.items():
        extra_info = _get_field_extra(field_name)
        models: list[str] = extra_info.get("models")
        field = _get_field(field_name, models)
        if extra_info.get("operator") == "like":
            expr = field.ilike("%" + str(value).lower() + "%")
        elif extra_info.get("operator") == "in" and isinstance(value, list):
            expr = func.lower(field).in_([str(v).lower() for v in value])
        else:
            expr = field.ilike(str(value).lower())
        filters.append(expr)

    if not filters:
        raise NoFiltersProvidedError(f"No valid filters were provided. {criteria_model_dump=}", status_code=400)

    query = query.filter(and_(*filters)).order_by(db.Alarm.id.desc())

    if criteria.options and criteria.options.get("active_only", False):
        query = query.filter(db.Alarm.is_active.is_(True))

    query = query.order_by(db.Alarm.id.desc())

    if criteria.options and str(criteria.options.get("max_results", "")).isnumeric():
        query = query.limit(int(criteria.options["max_results"]))

    results = query.all()
    return results


def _get_field(field: str, models: list[str] | None):
    """Try to get the field from the valid classes."""
    cls: type[db.Alarm] | type[db.Enrichment] | type[db.Agent]
    if not models:
        return getattr(db.Alarm, field)
    elif len(models) == 1:
        model = models[0]
        if not model or model == "Alarm":
            cls = db.Alarm
        elif model == "Enrichment":
            cls = db.Enrichment
        elif model == "Agent":
            cls = db.Agent

        return getattr(cls, field)
    else:
        # More than one model. Then we do a coalesce following the priority Enrichment > Alarm
        if "Alarm" in models and "Enrichment" in models:
            return coalesce(getattr(db.Enrichment, field), getattr(db.Alarm, field))


def _get_field_extra(field_name: str):
    """Return the field's extra information or an empty dict if it's None.

    This allows us to do a `get` on the result without having to check if it's None.
    """
    return AlarmInfoCriteria.model_fields[field_name].json_schema_extra or {}


def _get_linked_id(alarm: db.Alarm) -> str | None:
    """Get the release ID or the incident ID from the alarm."""
    linked_sap_id = None
    if alarm.active_incident:
        linked_sap_id = alarm.active_incident.sap_id
    elif alarm.active_release:
        linked_sap_id = f"R{alarm.active_release.sap_id}"
    else:
        linked_sap_id = None
    return linked_sap_id


def _add_required_response_fields(alarms: list[db.Alarm]) -> list[AlarmInfoResponse]:
    """Add all fields that are expected in the response."""
    results: list[AlarmInfoResponse] = []
    for alarm in alarms:
        agent = alarm.agent
        if not agent:
            raise Exception("No agent on the alarm. Please report this to an administrator.")
        last_clear = alarm.last_clear
        linked_sap_id = _get_linked_id(alarm)
        results.append(
            AlarmInfoResponse(
                alarm_id=alarm.id,
                ci_id=alarm.ci_id,
                floc_id=db.get_alarm_field(alarm, "floc_id"),
                agent_name=agent.ucmdb_name,
                metric_type=alarm.metric_type,
                metric_name=alarm.metric_name,
                actionable=alarm.actionable,
                raise_time=alarm.first_raise_time,
                clear_time=last_clear and last_clear.clear_time or None,
                last_occurrence_time=db.get_alarm_field(alarm, "raise_time"),
                severity=db.get_alarm_field(alarm, "severity"),
                summary=db.get_alarm_field(alarm, "summary"),
                linked_sap_id=linked_sap_id,
                location_attribute=db.get_alarm_field(alarm, "location_attribute"),
                location_address=db.get_alarm_field(alarm, "location_address"),
                location_type=db.get_alarm_field(alarm, "location_type"),
                top_level=alarm.top_level,
                is_active=alarm.is_active,
            )
        )

    return results
