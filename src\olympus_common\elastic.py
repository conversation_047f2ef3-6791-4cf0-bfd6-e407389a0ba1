"""Module to provide elasticsearch capabilities to an application."""


def read_elastic_index(
    user: str,
    password: str,
    servers: list[str],
    index: str,
    query: dict,
    sort_query: tuple,
    search_size: int,
    pit_lifetime: str = "2m",
) -> list:
    """Execute the given EQL query on the given index.

    Parameters
    ----------
    user : str
        User to access the given elasticsearch index.
    password : str
        Password of the given user.
    servers : list[str]
        List of the elasticsearch servers.
    index : str
        Name of the index in elasticsearch.
    query : dict
        The EQL query as a json object.
    sort_query : tuple
        The EQL query needed to sort the result as a json object.
    search_size : int
        Max size of the result (in number of records)
    pit_lifetime : str, optional
        Lifetime of the point in time, by default "2m"

    Returns
    -------
    list
        List of the elasticsearch records corresponding to the EQL query.

    Raises
    ------
    ValueError
        If the search query did not yield any results.
    """
    from elasticsearch import Elasticsearch

    # TODO: Add certificates path for BDS infrastructure.

    es = Elasticsearch(hosts=servers, basic_auth=(user, password), verify_certs=False)
    point_in_time_response = es.open_point_in_time(index=index, keep_alive=pit_lifetime)
    pit_id = point_in_time_response["id"]

    results = es.search(query=query, size=search_size, pit=dict(point_in_time_response), sort=sort_query)
    hits: list = results["hits"]["hits"]
    results_length = len(hits)
    if results_length <= 0:
        raise ValueError("The search query did not yield any results.")
    sort_id = hits[-1]["sort"]
    while results_length > 0:
        results = es.search(
            query=query, size=search_size, pit=dict(point_in_time_response), sort=sort_query, search_after=sort_id
        )
        hits_next_page = results["hits"]["hits"]
        results_length = len(hits_next_page)
        if results_length <= 0:
            break
        hits.extend(hits_next_page)
        sort_id = hits_next_page[-1]["sort"]
    es.close_point_in_time(id=pit_id)
    return hits
