"""Static module for mon-dica."""

COLUMNS_RENAMING: dict[str, str] = {
    "SNMPv2-SMI::enterprises.49797.1.2": "application_version",
    "SNMPv2-SMI::enterprises.49797.1.12": "link_connection_state",
    "SNMPv2-SMI::enterprises.49797.1.6": "server_identity",
    "SNMPv2-SMI::enterprises.49797.1.7": "alarm_priority",
    "SNMPv2-SMI::enterprises.49797.1.8": "alarm_dateTime",
    "SNMPv2-SMI::enterprises.49797.1.23": "remote_identity",
    "SNMPv2-SMI::enterprises.49797.1.11": "trap_description",
    "SNMPv2-SMI::enterprises.49797.1.21": "connection_type",
    "SNMPv2-SMI::enterprises.49797.1.5": "sw_bus_component",
    "SNMPv2-SMI::enterprises.49797.1.1": "application_name",
    "SNMPv2-SMI::enterprises.49797.1.14": "primary_rate_state",
    "SNMPv2-SMI::enterprises.49797.1.4": "server_ip_address",
    "SNMPv2-SMI::enterprises.49797.1.15": "signalling_state",
}

COLUMNS_TO_KEEP = list(COLUMNS_RENAMING.keys())
