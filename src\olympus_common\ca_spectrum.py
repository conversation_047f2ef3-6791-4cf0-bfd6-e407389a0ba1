"""Metadata for different CA_Spectrum metrics.

Since both enrichment_client and mon_script need this data, it's housed in Olympus_common.
"""

from olympus_common.enums import Severity

CA_SPECTRUM_METRICS: dict[str, dict[str, str | int]] = {
    "00010009": {
        "alert_group": "DEVICE HAS STOPPED RESPONDING TO POLLS",
        "description": "Device is not reachable from the management stations and is probably down",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.MAJOR.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "0001009f": {
        "alert_group": "Devi<PERSON> has stopped responding",
        "description": "The node is unreachable",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "00010b10": {
        "alert_group": "WIDE-AREA LINK CONTACT LOST",
        "description": "A WAN link to other devices or other stations is down",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.MAJOR.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "00010f06": {
        "alert_group": "HIGH MEMORY UTILIZATION",
        "description": "The free memory on the router is low which can imply slower performance of the router",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
        "severity_orange_non_crit": Severity.MAJOR.value,
        "severity_orange_crit": Severity.MAJOR.value,
    },
    "00210009": {
        "alert_group": "REDUNDANT POWER SUPPLY FAILED",
        "description": "The redundant power supply is lost",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.CRITICAL.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "00210010": {
        "alert_group": "ONE OF THE FANS IN THE FAN ARRAY FAILED",
        "description": "A fan went down with the risk of overheating the device",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.CRITICAL.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "00210027": {
        "alert_group": "C HSRP STATE CHANGE",
        "description": "Hot standby protocol switched from active to standby meaning a redundant connection is lost",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "002104b4": {
        "alert_group": "cefcFRURemoved/cefcFRUInserted",
        "description": "cefcFRURemoved/cefcFRUInserted",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "00210a9c": {
        "alert_group": "CpmCPURisingThreshold",
        "description": "Dit event wordt getriggerd wanneer de CPU-drempelwaarde bereikt wordt van een firewall device.",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "00210c12": {
        "alert_group": "ciscoEnvMonSuppStatusChangeNotif",
        "description": "Een ciscoEnvMonSuppStatusChangeNotif melding wordt opgeroepen wanneer de redundante stroomvoorziening uitvalt.",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "00210c43": {
        "alert_group": "ciscoEnvMonRedundantSupplyNotification",
        "description": "ciscoEnvMonRedundantSupplyNotification",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "00210c45": {
        "alert_group": "ciscoEnvMonFanNotification",
        "description": "Een ciscoEnvMonFanNotification wordt verstuurd als een van de ventilatoren in de ventilator array (waar aanwezig) uitvalt.",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "00210cf1": {
        "alert_group": "STACK PORT CHANGE DETECTED",
        "description": "A member of the stack is no longer available",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
        "severity_orange_non_crit": Severity.MINOR.value,
        "severity_orange_crit": Severity.MAJOR.value,
    },
    "00210cf4": {
        "alert_group": "RING REDUNDANCY CHANGE DETECTED",
        "description": "A ring is broken so the redundant path is lost",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
        "severity_orange_non_crit": Severity.MINOR.value,
        "severity_orange_crit": Severity.MAJOR.value,
    },
    "00210d06": {
        "alert_group": "CISCO FLASH DEVICE REMOVED NOTIF",
        "description": "The flash card from a Cisco device is removed or corrupt. No immediate impact but if the router is rebooted there is no IOS to load nor any config",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "00210d1e": {
        "alert_group": "License usage warning",
        "description": "number of user license is about to expire",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "00210d20": {
        "alert_group": "License expiry warning",
        "description": "license is about to expire",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "00210d38": {
        "alert_group": "Mobility anchor control path down",
        "description": "successive ICMP ping attempts to the anchor fails and the anchor is conclusively down Variable cLMobilityAnchorIPAddress denotes the IP Address of the anchor",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "00210d3a": {
        "alert_group": "Mobility anchor data path down",
        "description": "successive EoIP ping attempts to the anchor fails and the anchor is conclusively down Variable cLMobilityAnchorIPAddress denotes the IP Address of the anchor",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "00210d3c": {
        "alert_group": "All anchors on wlan down",
        "description": "successive EoIP ping attempts to all the anchors on WLAN, denoted by cLMobilityAnchorWlanId, fails and all the anchors are conclusively down",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "00210e3e": {
        "alert_group": "HA status",
        "description": "generic failure between the active and standby unit",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "00210e76": {
        "alert_group": "CeSensorExtThresholdNotification",
        "description": "Dit event wordt geactiveerd bij een stroomstoring en/of temperatuur groter dan drempelwaarde",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "00220001": {
        "alert_group": "Link Up/Down",
        "description": "One of the interfaces of the FW goes down",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "00220011": {
        "alert_group": "BGP BACKWARD TRANS NOTIFICATION",
        "description": "BGP neighbour went down - BGP is used to connect to Internet and other Railways in other countries (eg robrufw13)",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
        "severity_orange_non_crit": Severity.MAJOR.value,
        "severity_orange_crit": Severity.MAJOR.value,
    },
    "011c004e": {
        "alert_group": "POWER SUPPLY TWO FAILURE",
        "description": "Power redundancy has been lost",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.CRITICAL.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "011c0488": {
        "alert_group": "MODULE DOWN DETECTED",
        "description": "Module is no longer visible in the device",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.MAJOR.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "03b10001": {
        "alert_group": "POWER SUPPLY IS NOT OPERATIONAL",
        "description": "Device {{m}} of type {{t}} is reporting a power supply failure from power supply {I 3}. The power supply has a bad DC output.",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.CRITICAL.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "03b7000c": {
        "alert_group": "BIGIP CPU TEMP HIGH",
        "description": "The CPU temperature is too high",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "03b7000d": {
        "alert_group": "BIGIP CPU FAN SPEED LOW",
        "description": "The CPU fan speed is too low",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "03b7000e": {
        "alert_group": "BIGIP CPU FAN SPEED BAD",
        "description": "The CPU fan speed signal is not being received",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "03b7000f": {
        "alert_group": "BIGIP CHASSIS TEMP HIGH",
        "description": "The chassis temperature is too high",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70010": {
        "alert_group": "BIGIP CHASSIS FAN BAD",
        "description": "The chassis fan status is bad",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "03b70011": {
        "alert_group": "BIGIP CHASSIS POWER SUPPLY BAD",
        "description": "The chassis power supply status is bad",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70012": {
        "alert_group": "BIGIP SERVICE DOWN",
        "description": "A service is DOWN",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b70013": {
        "alert_group": "BIGIP SERVICE UP",
        "description": "A service is UP",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b70014": {
        "alert_group": "BIGIP NODE DOWN",
        "description": "A node is DOWN",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b70015": {
        "alert_group": "BIGIP NODE UP",
        "description": "A node is UP",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b70016": {
        "alert_group": "BIGIP STANDBY",
        "description": "The system is going into standby mode",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70017": {
        "alert_group": "BIGIP ACTIVE",
        "description": "The system is going into active mode",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70018": {
        "alert_group": "BIGIP ACTIVE ACTIVE",
        "description": "The system is going into active-active mode",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70019": {
        "alert_group": "BIGIP FEATURE FAILED",
        "description": "A high availability feature triggered action failed",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b7001a": {
        "alert_group": "BIGIP FEATURE ONLINE",
        "description": "A high availability feature is now responding",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b7001b": {
        "alert_group": "BIGIP LICENSE FAILED",
        "description": "The license validation failed",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b7001c": {
        "alert_group": "BIGIP LICENSE EXPIRED",
        "description": "The license has expired",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b7001f": {
        "alert_group": "BIGIP ARP CONFLICT",
        "description": "There is an ARP conflict",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b70020": {
        "alert_group": "BIGIP NET LINK DOWN",
        "description": "An internal interface link is down. This is for L1 and L2. These are internal links within the box connecting the CPU and Switch subsystems which should never lose link. If they do it indicates a serious problem",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70021": {
        "alert_group": "BIGIP DISK PARTITION WARN",
        "description": "The disk partition free space is very limited which is less than a specified limit. By default the limit is set to 30% of total disk space",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70022": {
        "alert_group": "BIGIP DISK PARTITION GROWTH",
        "description": "The disk partition exceeds the specified growth limit. By default the limit is set to 5% of the total disk space",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70025": {
        "alert_group": "BIGIP LOG EMERG",
        "description": "The system is in an unusable situation",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70026": {
        "alert_group": "BIGIP LOG ALERT",
        "description": "Action must be taken immediately for the system to work properly",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70027": {
        "alert_group": "BIGIP LOG CRIT",
        "description": "The system is in a critical condition",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70028": {
        "alert_group": "BIGIP LOG ERR",
        "description": "The system has some error conditions",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "03b70029": {
        "alert_group": "BIGIP LOG WARNING",
        "description": "The system is experiencing some warning conditions",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b7002a": {
        "alert_group": "BIGIP PACKET REJECTED",
        "description": "Packets were rejected",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b7002b": {
        "alert_group": "BIGIP COMP LIMIT EXCEEDED",
        "description": "The compression license limit was exceeded",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b7002c": {
        "alert_group": "BIGIP SSL LIMIT EXCEEDED",
        "description": "The SSL license limit was exceeded either for TPS (Transactions Per Second) or for MPS (Megabits Per Second)",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b7002d": {
        "alert_group": "BIGIP EXTERNAL LINK CHANGE",
        "description": "An external interface link status changed to either DOWN or UP. This occurs when network cables are added or removed and/or the network is re-configured",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70053": {
        "alert_group": "BIGIP STAND BY FAIL",
        "description": "The Big IP is in the failover condition. This standby will not be able to go active",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70054": {
        "alert_group": "BIGIP INET PORT EXHAUSTION",
        "description": "The inet port is exhausted",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "03b70060": {
        "alert_group": "BIGIP BLADE NO POWER",
        "description": "A blade lost power. The blade may be pulled out",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70061": {
        "alert_group": "BIGIP CLUSTERD NO RESPONSE",
        "description": "The cluster daemon failed to respond for 10 or more seconds",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70062": {
        "alert_group": "BIGIP BLADE OFFLINE",
        "description": "A blade has failed - offline",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70065": {
        "alert_group": "BIGIP ALWAYS-ON MANAGEMENT CPU TEMP TOO HIGH",
        "description": "Always-On Management (AOM) reports the air temperature near the host CPU too high",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b70074": {
        "alert_group": "BIGIP VCMP GUEST POWERED OFF",
        "description": "A VCMP guest is powered off",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b70075": {
        "alert_group": "BIGIP VCMP GUEST HEART BEAT LOST",
        "description": "A virtual clustered multiprocessing (vCMP) guest heartbeat is lost",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70076": {
        "alert_group": "BIGIP VCMP GUEST HEART BEAT",
        "description": "A virtual clustered multiprocessing (vCMP) guest heartbeat is detected or regained",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "03b7007f": {
        "alert_group": "BIGIP BLADE POWERED OFF",
        "description": "Blade is about to be powered off",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70080": {
        "alert_group": "BIGIP SENSOR ALARM CRITICAL",
        "description": "Blade hardware sensor indicated critical alarm",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70081": {
        "alert_group": "BIGIP ALERT FAN TRAY BAD",
        "description": "Chassis fan tray is bad or removed",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "03b70085": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT CURRENT HIGH",
        "description": "Current is too high",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "03b70088": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT CURRENT LOW",
        "description": "Current is too low",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "04500006": {
        "alert_group": "SERVICE IS DOWN",
        "description": "SERVICE IS DOWN",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "04500008": {
        "alert_group": "SERVICE IS DEGRADED",
        "description": "SERVICE IS DEGRADED",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "0450000a": {
        "alert_group": "SERVICE IS SLIGHLY DEGRADED",
        "description": "SERVICE IS SLIGHLY DEGRADED",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "04b60015": {
        "alert_group": "Authentication server not responding",
        "description": "Radius server(s) do not respond to authentication requests from clients",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "04b60024": {
        "alert_group": "Temperature threshold",
        "description": "temperature sensor detects a high value",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "04b60028": {
        "alert_group": "POE controller has failed",
        "description": "Power Over Ethernet at the controller has failed",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "04b60034": {
        "alert_group": "Broadcast storm start",
        "description": "broadcast storm starts",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "04b60036": {
        "alert_group": "Link Failure",
        "description": "link is broken. In this situation, the setup does a failover",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "04b6003b": {
        "alert_group": "Fan Failure",
        "description": "Notification for fan failure",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "04b6003e": {
        "alert_group": "Power supply status change",
        "description": "power supply status is changed",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "05610002": {
        "alert_group": "Health check failure",
        "description": "A software health check failed at least once",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "05610026": {
        "alert_group": "Sensor status warning (voltage/temp/fan)",
        "description": "A hardware health check is in warning state",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "05610027": {
        "alert_group": "Sensor status critical (voltage/temp/fan)",
        "description": "A hardware health check is in warning state",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "05610029": {
        "alert_group": "Disk Status (8 (ioerror), 9 (unusable), 10 (unknown))",
        "description": "A disk crashed, this mainly server caching purposes",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "0561002d": {
        "alert_group": "Device under attack",
        "description": "The proxy has detected an attack.",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "0561003c": {
        "alert_group": "License status change",
        "description": "A license has updated or expired",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "0561003f": {
        "alert_group": "Health monitoring warning",
        "description": "The proxy is in warning state",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "05610040": {
        "alert_group": "Health monitoring critical",
        "description": "The proxy is in warning state",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "05b1000c": {
        "alert_group": "CPU is high",
        "description": "The CPU utilization is above the configured threshold",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "05b1000d": {
        "alert_group": "Memory is low",
        "description": "The memory utilization (RAM) is above the configured threshold",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "05b1000e": {
        "alert_group": "Log disk space is low",
        "description": "The amount of used disk space (HDD) is above the configured threshold.",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "05b10011": {
        "alert_group": "PSU Failure",
        "description": "Power supply failure detected",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "05b10027": {
        "alert_group": "HA State change",
        "description": "Failover: the specified cluster member has transitioned from a slave role to a master role",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "05b10028": {
        "alert_group": "HA cluster changed",
        "description": "Alarm is sent when the high-availability (HA) cluster member changes its state",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "05b10029": {
        "alert_group": "Heartbeat failure",
        "description": "The heartbeat device failure count has exceeded the configured threshold. The two members of a cluster cannot reach each other with pings",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "05b1002a": {
        "alert_group": "HA Member Unavailable",
        "description": "The specified device (by serial number) is moving to a down state",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "05b1002b": {
        "alert_group": "HA Member available",
        "description": "A new cluster member has joined the high-availability (HA) cluster",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "05b10033": {
        "alert_group": "AV conserve mode",
        "description": "The anti-virus engine has entered conservation mode due to a low memory condition. Triggered at 88% memory use",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "05b100f9": {
        "alert_group": "link-up(51)",
        "description": "Network interface: connection back available",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "05b100fa": {
        "alert_group": "link-down(52)",
        "description": "Network interface: connection lost",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "05b10152": {
        "alert_group": "Fan failure",
        "description": "A fan failure has been detected",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00004": {
        "alert_group": "Interface Up/down 100x/3h",
        "description": "An interface is very unstable",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
        "severity_orange_non_crit": Severity.MAJOR.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "fff00008": {
        "alert_group": "High CPU utilization",
        "description": "The CPU load is high. This can imply a slower performance of the device",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00174": {
        "alert_group": "Duplex mismatch discovered (Syslog)",
        "description": "A link is detecting Half duplex while the other end is in full duplex",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.MAJOR.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "fff0017a": {
        "alert_group": "More then 20communication link up/downs on device",
        "description": "A communication link went up/down 20 times in 1h",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
        "severity_orange_non_crit": Severity.MINOR.value,
        "severity_orange_crit": Severity.MAJOR.value,
    },
    "fff0017e": {
        "alert_group": "Link went down 5x/12h",
        "description": "A link went up/down for 5 times in 12 hours",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
        "severity_orange_non_crit": Severity.MAJOR.value,
        "severity_orange_crit": Severity.MAJOR.value,
    },
    "fff00182": {
        "alert_group": "BAD LINK DETECTED DT.R.",
        "description": "BAD LINK DETECTED DT.R.",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00183": {
        "alert_group": "BAD LINK DETECTED RPT SEGMENT",
        "description": "BAD LINK DETECTED RPT SEGMENT",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00184": {
        "alert_group": "BAD LINK DETECTED DEVICE",
        "description": "Interface to another network element is down",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
        "severity_orange_non_crit": Severity.MINOR.value,
        "severity_orange_crit": Severity.MAJOR.value,
    },
    "fff00189": {
        "alert_group": "Fan has failed 25x/1d",
        "description": "A fan fault was detected 25 times in 1 day",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
        "severity_orange_non_crit": Severity.MINOR.value,
        "severity_orange_crit": Severity.MAJOR.value,
    },
    "fff0018f": {
        "alert_group": "BAD LINK DETECTED RPT_SEGMENT WITH CONNECTED NODES",
        "description": "Interface to a repeater segment is down",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
        "severity_orange_non_crit": Severity.MINOR.value,
        "severity_orange_crit": Severity.MAJOR.value,
    },
    "fff00192": {
        "alert_group": "DOMRXLASERPOWERLOWALARM",
        "description": "The sensor value crossed the threshold listed in entSensorThresholdTable.",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.MINOR.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "fff00196": {
        "alert_group": "ENVIRONMENTAL MONITOR SUPPLY STATUS CHANGE NOTIFICATION",
        "description": "Status of power supply changed",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.CRITICAL.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "fff001a7": {
        "alert_group": "ibStateChangeEvent",
        "description": "trap is generated when a state change occurs on the infoblox",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff001b9": {
        "alert_group": "Ent Sensor Threshold notification",
        "description": "The sensor value crossed the threshold listed in entSensorThresholdTable.",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
        "severity_orange_non_crit": Severity.MINOR.value,
        "severity_orange_crit": Severity.CRITICAL.value,
    },
    "fff001c9": {
        "alert_group": "ibEquipmentFailureTrap",
        "description": "trap is generated when a hardware failure occurs on the infoblox",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff001ca": {
        "alert_group": "ibThresholdCrossingEvent",
        "description": "trap is generated when a threshold is breached on the infoblox",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff001cc": {
        "alert_group": "ibProcessingFailureTrap",
        "description": "trap is generated when a failure occurs in one of the software processes on the infoblox",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00201": {
        "alert_group": "Cell underflow at the state stage",
        "description": "Cell underflow at the state stage",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00207": {
        "alert_group": "BIGIP BLADE TEMP HIGH",
        "description": "Blade temperature is too high",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff0020b": {
        "alert_group": "BIGIP LIBHAL DISK BAY REMOVED",
        "description": "Disk sled was removed from a bay",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff0020d": {
        "alert_group": "BIGIP LIBHAL SSD LOGICAL DISK REMOVED",
        "description": "SSD logical disk was removed",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff0020e": {
        "alert_group": "BIGIP LIBHAL SSD PHYSICAL DISK REMOVED",
        "description": "SSD physical disk was removed",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff0020f": {
        "alert_group": "BIGIP MEMBER RATE",
        "description": "A pool member has exceeded the allowed rate",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff00210": {
        "alert_group": "BIGIP NODE RATE",
        "description": "A node has exceeded the allowed rate",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff00211": {
        "alert_group": "BIGIP RAID DISK FAILURE",
        "description": "Disk failure in a RAID disk array",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00212": {
        "alert_group": "BIGIP SSD MWI NEAR THRESHOLD",
        "description": "SSD disk wear-out indicator is near its threshold",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00213": {
        "alert_group": "BIGIP SSD MWI REACHED THRESHOLD",
        "description": "SSD disk wear-out indicator has reached its threshold",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00216": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT FAN SPEED LOW",
        "description": "Fan speed is too low",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00217": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT MILLI VOLTAGE HIGH",
        "description": "Milli-Voltage is too high",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00218": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT MILLI VOLTAGE LOW",
        "description": "Milli-Voltage is too low",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00219": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT POWER HIGH",
        "description": "Power is too high",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0021a": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT POWER LOW",
        "description": "Power is too low",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0021b": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT TEMP HIGH",
        "description": "Temperature is too high",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0021c": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT VOLTAGE HIGH",
        "description": "Voltage is too high",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0021d": {
        "alert_group": "BIGIP SYSTEM CHECK ALERT VOLTAGE LOW",
        "description": "Voltage is too low",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0021e": {
        "alert_group": "BIGIP UNSOLICITED REPLIES EXCEEDED THRESHOLD",
        "description": "The DNS cache object received unsolicited query replies exceeding a configured threshold",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00222": {
        "alert_group": "BIGIP VCMP ALERTS VCMP POWER ON",
        "description": "A VCMP guest is powered on from a suspended or powered off state",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff00223": {
        "alert_group": "BIGIP VIRTUAL RATE",
        "description": "A virtual has exceeded the allowed rate",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff0022b": {
        "alert_group": "Crashing/unstable module",
        "description": "Crashing/unstable module",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00238": {
        "alert_group": "Module PowerCycle",
        "description": "Module PowerCycle",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00241": {
        "alert_group": "CeRedunEventSwitchover",
        "description": "Failover van de een firewall deelnemer in de cluster. Meestal in combinatie met Link up/Down event.",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00243": {
        "alert_group": "Cold start",
        "description": "Reboot of one of the members",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00244": {
        "alert_group": "Warm start",
        "description": "Reboot of one of the members",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00252": {
        "alert_group": "CefcFRUInserted",
        "description": "Dit event wordt opgeroepen wanneer de interface zijn SFP ( Small Form-Factor Pluggable Interface) of fysieke verbinding toevoegt.",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00253": {
        "alert_group": "CefcFRURemoved",
        "description": "Dit event wordt opgeroepen wanneer de interface zijn SFP ( Small Form-Factor Pluggable Interface) of fysieke verbinding verliest.",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00268": {
        "alert_group": "Link Aggregation Control Protocol Error",
        "description": "Link Aggregation Control Protocol Error",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00269": {
        "alert_group": "sfEvent",
        "description": "sfEvent",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0026a": {
        "alert_group": "sfEventIpString",
        "description": "sfEventIpString",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0027f": {
        "alert_group": "sfEventImpact",
        "description": "sfEventImpact",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00282": {
        "alert_group": "sfPolicyEvent",
        "description": "sfPolicyEvent",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00285": {
        "alert_group": "sfImpactAlert",
        "description": "sfImpactAlert",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0028e": {
        "alert_group": "dceNetworkBasedMalwareEvent",
        "description": "dceNetworkBasedMalwareEvent",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00290": {
        "alert_group": "sfFirewallEvent",
        "description": "sfFirewallEvent",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00291": {
        "alert_group": "sfFirewallEventIpString",
        "description": "sfFirewallEventIpString",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00293": {
        "alert_group": "Policy Loaded",
        "description": "Dit event wordt getriggerd wanneer een policy wordt herladen.",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff00294": {
        "alert_group": "CP node disconnected",
        "description": "Dit event wordt geactiveerd als een firewall deelnemer (node) uitvalt.",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00295": {
        "alert_group": "CPU usage alert",
        "description": "Dit event wordt getriggered als de CPU treshhold van een firewall deelnemer gedurende meer dan 10min overschreden wordt.",  # noqa: E501
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00296": {
        "alert_group": "CPU Usage",
        "description": "Monitors CPU Usage",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff00297": {
        "alert_group": "Appliance Heartbeat",
        "description": "Monitors if the attached appliances are alive",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00298": {
        "alert_group": "Backlog Status",
        "description": "Backlog Status",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00299": {
        "alert_group": "Classic License Monitor",
        "description": "This module determines if sufficient Classic licenses remain.",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0029a": {
        "alert_group": "Card reset",
        "description": "Monitors the network card conditions requiring an automatic reset",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0029c": {
        "alert_group": "Disk Status",
        "description": "Disk Status",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0029d": {
        "alert_group": "Disk Usage",
        "description": "Monitors Disk Usage",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0029e": {
        "alert_group": "Hardware Alarms",
        "description": "Monitor any alarm sent by the operating system or network cards",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0029f": {
        "alert_group": "Health Monitor Process",
        "description": "Monitors the status of the Health Monitor itself",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002a0": {
        "alert_group": "Inline Link Mismatch Alarms",
        "description": "Alerts on inline pairs with mismatched link speeds",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002a1": {
        "alert_group": "Interface Status",
        "description": "Monitors if the interfaces are receiving traffic",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002a2": {
        "alert_group": "Intrusion and File Event Rate",
        "description": "Monitors if the interfaces are receiving traffic",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002a3": {
        "alert_group": "Memory Usage",
        "description": "Memory Usage: Critical>90%  80%<Warning<90%",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff002a4": {
        "alert_group": "Platform Faults",
        "description": "There is a platform fault",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff002a5": {
        "alert_group": "Power Supply",
        "description": "Monitors Power Supply",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002a6": {
        "alert_group": "Process Status",
        "description": "Monitor daemon processes",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002a7": {
        "alert_group": "Reconfiguring Detection",
        "description": "Reconfiguring Detection",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff002a8": {
        "alert_group": "Security Intelligence",
        "description": "Security Intelligence",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff002a9": {
        "alert_group": "Smart License Monitor",
        "description": "Monitors Smart Licensing Status",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff002aa": {
        "alert_group": "Threat Data Updates on Devices",
        "description": "Alerts if threat intelligence data on managed devices is not updated",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002ab": {
        "alert_group": "Time Series Data Monitor",
        "description": "Time Series Data Monitor",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff002ac": {
        "alert_group": "Time Synchronization Status",
        "description": "Monitors the time difference of managed devices",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff002ad": {
        "alert_group": "User Agent Status Monitor",
        "description": "User Agent Status Monitor",
        "clear_type": 1,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff002ae": {
        "alert_group": "VPN status",
        "description": "VPN Status",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002bb": {
        "alert_group": "The Crypto Device on a blade indicates a critical alarm",
        "description": "The Crypto Device on a blade indicates a critical alarm",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002bc": {
        "alert_group": "BIGIP System overload status",
        "description": "BIGIP System overload status",
        "clear_type": 1,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002d3": {
        "alert_group": "Possible DNS attack detected - Abnormal conditions: NXDOMAIN responses at xx%",
        "description": "Possible DNS attack detected. Abnormal conditions: NXDOMAIN responses at ..%",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff002d5": {
        "alert_group": "The NTP service is out of synchronization",
        "description": "The NTP service is out of synchronization",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002d7": {
        "alert_group": "LAN1 port link is down",
        "description": "LAN1 port link is down. Please check the connection",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002e1": {
        "alert_group": "SERVICE Seinhuis Uplinks, Discards >1%",
        "description": "SERVICE Seinhuis Uplinks, Discards >1%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002e2": {
        "alert_group": "SERVICE Seinhuis Uplinks, Errors >1%",
        "description": "SERVICE Seinhuis Uplinks, Errors >1%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002e3": {
        "alert_group": "SERVICE Seinhuis Uplinks, IfIn",
        "description": "SERVICE Seinhuis Uplinks, IfIn",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002e4": {
        "alert_group": "SERVICE Seinhuis Uplinks, IfOut",
        "description": "SERVICE Seinhuis Uplinks, IfOut",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002e5": {
        "alert_group": "SERVICE Seinhuis Access, Discards",
        "description": "SERVICE Seinhuis Access, Discards",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff002e6": {
        "alert_group": "SERVICE Seinhuis Access, Errors",
        "description": "SERVICE Seinhuis Access, Errors",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff002e7": {
        "alert_group": "SERVICE Seinhuis Access, IfIn",
        "description": "SERVICE Seinhuis Access, IfIn",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff002e8": {
        "alert_group": "SERVICE Seinhuis Access, IfOut",
        "description": "SERVICE Seinhuis Access, IfOut",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff002e9": {
        "alert_group": "No members available for pool",
        "description": "No members available for pool",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff002e9_infrabel/a1253-proxyi1-monitor_pool": {
        "alert_group": "No members available for pool",
        "description": "No members available for pool",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002e9_infrabel/a1253-proxyi2-monitor_pool": {
        "alert_group": "No members available for pool",
        "description": "No members available for pool",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002e9_infrabel/a1253-proxyi3-monitor_pool": {
        "alert_group": "No members available for pool",
        "description": "No members available for pool",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002e9_infrabel/a1253-proxyi-ftp_pool": {
        "alert_group": "No members available for pool",
        "description": "No members available for pool",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002ee": {
        "alert_group": "SERVICE Seinhuis Uplinks, Discards >0.5%",
        "description": "SERVICE Seinhuis Uplinks, Discards >0.5%",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff002ef": {
        "alert_group": "SERVICE Seinhuis Uplinks, Errors >0.5%",
        "description": "SERVICE Seinhuis Uplinks, Errors >0.5%",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff002f1": {
        "alert_group": "HA replication is offline",
        "description": "HA replication is offline",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002fd": {
        "alert_group": "Replication queue is offline",
        "description": "Replication queue is offline",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff002ff": {
        "alert_group": "The Grid member is not connected to the grid master",
        "description": "The Grid member is not connected to the grid master",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00303": {
        "alert_group": "Processor usage",
        "description": "processor usage exceeds the threshold",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00305": {
        "alert_group": "RAM usage in %",
        "description": "RAM usage exceeds the threshold",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00307": {
        "alert_group": "CPU temperature",
        "description": "unit temperature exceeds the threshold",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00309": {
        "alert_group": "Number of joined AP",
        "description": "large number of access point (> 100) are disconnected",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0030b": {
        "alert_group": "CPU Utilization",
        "description": "The CPU utilization is above 75%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0030d": {
        "alert_group": "Memory Utilization",
        "description": "The memory utilization (RAM) is above 75%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00310": {
        "alert_group": "Disk Usage",
        "description": "The amount of used disk space (HDD) is above 75%",
        "clear_type": 2,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff00314": {
        "alert_group": "Processor usage",
        "description": "CPU of appliance is going above critical",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00316": {
        "alert_group": "Memory usage",
        "description": "Memory of appliance is going above critical",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00319": {
        "alert_group": "Interface down",
        "description": "A network interface port is unavailable for traffic",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031c_31000": {
        "alert_group": "Could not initialize notification dispatcher",
        "description": "Could not initialize notification dispatcher",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_31001": {
        "alert_group": "Could not send configuration notification message",
        "description": "Could not send configuration notification message",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_31200": {
        "alert_group": "Encountered invalid/Null Log Record encountered",
        "description": "Invalid or null log record",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_31201": {
        "alert_group": "Encountered invalid or null system message",
        "description": "Could not create corresponding system message from opcode",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_31202": {
        "alert_group": "Encountered invalid or null user context",
        "description": "Encountered invalid or null user context",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_31203": {
        "alert_group": "Encountered error while recording the audit record for successful login",
        "description": "Encountered error while recording the audit record for successful login",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_31204": {
        "alert_group": "Encountered error while recording the audit record for failed login",
        "description": "Encountered error while recording the audit record for failed login",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_31205": {
        "alert_group": "Encountered error while recording the audit record for logout",
        "description": "Encountered error while recording the audit record for logout",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_31206": {
        "alert_group": "Encountered error while recording the audit record for failover mode",
        "description": "Encountered error while recording the audit record for failover mode",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_31207": {
        "alert_group": "Encountered error while recording the audit record for session timeout",
        "description": "Encountered error while recording the audit record for session timeout",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32012": {
        "alert_group": "Could not write to local storage file",
        "description": "Could not write to local storage CSV file",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32013": {
        "alert_group": "Could not create a local storage file",
        "description": "Could not create a local storage CSV file",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32014": {
        "alert_group": "Could not delete a local storage CSV file",
        "description": "Could not delete a local storage CSV file",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32026": {
        "alert_group": "Could not roll over local storage file",
        "description": "Could not roll over local storage CSV file",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32500": {
        "alert_group": "General database error",
        "description": "General database error",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32601": {
        "alert_group": "Could not start message bus",
        "description": "Could not start message bus",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32603": {
        "alert_group": "Dropped connection. Reconnecting",
        "description": "Dropped connection. Reconnecting",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32604": {
        "alert_group": "Unknown bus error",
        "description": "Unknown bus error",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32605": {
        "alert_group": "Unknown attribute",
        "description": "Unknown attribute",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_32606": {
        "alert_group": "Dropped unknown message type",
        "description": "Dropped unknown message type",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_33201": {
        "alert_group": "AD Operation failure",
        "description": "ISE failed during any of the following: While initiating an event to join Active Directory domain. While disconnecting from Active Directory domain. While getting status from Active Directory domain",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_33205": {
        "alert_group": "General PI error",
        "description": "Unexpected error found by the ISE web service provisioning component",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_33300": {
        "alert_group": "General GUI error",
        "description": "Unexpected error found by ISE graphical user interface",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_33452": {
        "alert_group": "Failed to clear OCSP cache",
        "description": "Failed to clear OCSP cache",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_33500": {
        "alert_group": "Could not initialize EAP-TLS",
        "description": "The EAP-TLS module could not initialize and will be disabled",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_33501": {
        "alert_group": "Could not initialize EAP-FAST",
        "description": "The EAP-FAST module could not initialize and will be disabled",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_33502": {
        "alert_group": "Could not initialize PEAP",
        "description": "The PEAP module could not initialize and will be disabled",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_33513": {
        "alert_group": "Could not initialize TEAP",
        "description": "The TEAP module could not initialize and will be disabled",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_34051": {
        "alert_group": "RT Control port is blocked",
        "description": "RT failed to open the RT Control port. RT Control services are not available. RT will try to open the port again",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_34110": {
        "alert_group": "Error processing the REST request",
        "description": "Server has encountered error while processing the REST request",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_34112": {
        "alert_group": "Invalid REST request data",
        "description": "REST Request data has invalid syntax",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_34117": {
        "alert_group": "Error connecting to remote feed URL",
        "description": "This message is generated when remote feed site is down",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_34118": {
        "alert_group": "Error processing package from Cisco download feed site",
        "description": "Error processing package from Cisco download feed site",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_34119": {
        "alert_group": "Profile received an error response from NAC Manager for notification event",
        "description": "Profiler sends a notification event to NAC Manager, but the notification fails because NAC Manager cannot process it. Check NAC Manager logs for details",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_34120": {
        "alert_group": "Profiler failed to get the connection to NAC Manager",
        "description": "Profiler sends a notification event to NAC Manager, but the notification fails because could not connect to NAC Manager",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_34155": {
        "alert_group": "Endpoint with the same Mac Address already exists",
        "description": "Endpoint with the same Mac Address already exists",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_34157": {
        "alert_group": "Could not initialize EAP-TTLS",
        "description": "The EAP-TTLS module could not initialize and will be disabled",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35011": {
        "alert_group": "License expired",
        "description": "A License in the ISE Deployment has expired",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35013": {
        "alert_group": "License deletion failed",
        "description": "License deletion failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35014": {
        "alert_group": "License create failed",
        "description": "License create failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35015": {
        "alert_group": "License update failed",
        "description": "License update failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35025": {
        "alert_group": "License expiring within 30 Days",
        "description": "License expiring within 30 Days",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35026": {
        "alert_group": "License Out of Compliance for 5 or more days",
        "description": "License Out of Compliance for 5 or more days",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35027": {
        "alert_group": "License Out of Compliance for 15 or more days",
        "description": "License Out of Compliance for 15 or more days",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35028": {
        "alert_group": "License Out of Compliance for 30 or more days",
        "description": "License Out of Compliance for 30 or more days",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35029": {
        "alert_group": "License Out of Compliance for more than 45 Days Services Configuration Disabled",
        "description": "License Out of Compliance for more than 45 Days Services Configuration Disabled",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35031": {
        "alert_group": "License exceeded 125% session usage",
        "description": "License exceeded 125% session usage",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35034": {
        "alert_group": "License expiring Within 30 Days",
        "description": "License expiring Within 30 Days",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35035": {
        "alert_group": "License expired",
        "description": "License expired",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35038": {
        "alert_group": "License expiring Within 30 Days",
        "description": "License expiring Within 30 Days",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_35039": {
        "alert_group": "License expired",
        "description": "License expired",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41014": {
        "alert_group": "Software version not found",
        "description": "The system call that obtains the ISE Software version failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41015": {
        "alert_group": "Could not run",
        "description": "The system call that was activated did not run correctly",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41016": {
        "alert_group": "could not read stdout",
        "description": "While running a system call the stdout of the system call could not be read",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41018": {
        "alert_group": "Service Selection Policy update failed",
        "description": "During system initialization the Default Service Selection Policy update failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41019": {
        "alert_group": "Could not add relation to Service Selection Policy",
        "description": "During system initialization the Default Service Selection Policy update failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41020": {
        "alert_group": "Could not initialize Service Selection Policy",
        "description": "During system initialization the Default Service Selection Policy update failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41021": {
        "alert_group": "Could not update ISE Node Object",
        "description": "Failed to update ISE Node with the local node information when the system started",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41022": {
        "alert_group": "An error occurred while collecting NodeInfo",
        "description": "Collection of the local node information failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41023": {
        "alert_group": "An error occurred while collecting replication status",
        "description": "Collection of the replication status failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41024": {
        "alert_group": "Error loading NodeInfo",
        "description": "The NodeInfo file did not load correctly",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41025": {
        "alert_group": "NodeInfo file contains incomplete information",
        "description": "NodeInfo file contains incomplete information and has loaded incorrectly",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41026": {
        "alert_group": "Management config directory could not be created",
        "description": "The Management config directory could not be created",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41027": {
        "alert_group": "NodeInfo file could not be created",
        "description": "NodeInfo file could not be created in the config directory",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41028": {
        "alert_group": "MAC Address not found during initialization",
        "description": "Machine Network Address could not be found in the system network interface output during initialization",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41029": {
        "alert_group": "ISE Node record not found in existing nodes. ISE cannot start",
        "description": "During system initialization the ISE Node record representing the local instance was not found in the existing nodes. ISE Management could not start",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41030": {
        "alert_group": "MAC Id not found in ACSNodeInfo",
        "description": "The Machine address field was not found in the ACSNodeInfo record in the database",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41031": {
        "alert_group": "Registering Secondary Hostname already exists in Primary database",
        "description": "An attempt is being made to register the Secondary hostname. However it already exists in the Primary database",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41032": {
        "alert_group": "Register failed since Secondary MAC address already exists in the Primary database",
        "description": "An attempt is being made to register the machine address of the Secondary hostname. However it already exists in the Primary database",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41033": {
        "alert_group": "Deregistration failed since Secondary ISE Node not found in the Primary database",
        "description": "ISE instance de-registration failed since the Secondary's ISE Node record was not found in Primary database",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41034": {
        "alert_group": "Activation failed since Secondary ISE Node is not found",
        "description": "Activation of the Secondary node from the Primary database failed because the Secondary ACSNode record was not found in the database",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41035": {
        "alert_group": "Remote host is not a Primary AcsNode",
        "description": "During a Distributed Management Remote operation connection to the Primary was not possible because the host is not a Primary instance",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41036": {
        "alert_group": "Cannot deregister a Primary ISE Node",
        "description": "The Primary instance of a deployment cannot be de-registered",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41037": {
        "alert_group": "ISE Deployment record cannot be found and therefore Primary initialization is incorrect",
        "description": "During system initialization the ISE Deployment record could not be found and the system could not start correctly",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41038": {
        "alert_group": "Interface configuration cannot be found",
        "description": "During the System call to obtain the Network Interface configuration a failure occurred",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41039": {
        "alert_group": "Network interface eth0 cannot be found",
        "description": "During the system call to obtain the Network Interface eth0 configuration a failure occurred and the interface was not found",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41040": {
        "alert_group": "Network interface eth0 hardware address cannot be found",
        "description": "During the system call to obtain the Network Interface eth0 configuration hardware address a failure occurred and the hardware address was not found",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41041": {
        "alert_group": "Network interface eth0 inet address cannot be found",
        "description": "During the System call to obtain the Network Interface eth0 configuration IP address a failure occurred and the IP address was not found",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41042": {
        "alert_group": "Network interface eth0 mask cannot be found",
        "description": "During the system call to obtain the Network Interface eth0 configuration subnet mask a failure occurred and the subnet mask was not found",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41043": {
        "alert_group": "Could not create ACSNodeInfo",
        "description": "The system failed to create AcsNodeInfo record and attach it to the AcsNode record for the instance",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41044": {
        "alert_group": "Failure to find the reconnection Acs Instance in the primary, please check that the Acs Instance exists in the Primary Acs Instance Listing page",  # noqa: E501
        "description": "During a Hardware Replacement or LocalMode reconnection the AcsNode record with the specified Replacement Keyword could not be found. This keyword is the hostname of the system by default",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41045": {
        "alert_group": "Failure. Specified replacement keyword is associated with a registered instance",
        "description": "During hardware replacement the specified replacement keyword is associated with an ISE instance that has already been registered",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_41064": {
        "alert_group": "The deployment Log Collector cannot be deregistered",
        "description": "The deployment cannot be left without a Log Collector configured. De-registering this node will remove the selected Log Collector",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60072": {
        "alert_group": "Profiler Feed Service reported that the Feed is unavailable",
        "description": "The Feed that was queried for was not known by the Profiler Feed Service",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60073": {
        "alert_group": "Querying the Profiler Feed Service resulted in an unexpected error",
        "description": "Received an unexpected error when querying the the Profiler Feed Service",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60074": {
        "alert_group": "Importing downloaded profiles from the Profiler Feed Service resulted in an unexpected error",
        "description": "Received an unexpected error when importing downloaded profiles from the Profiler Feed Service",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60095": {
        "alert_group": "ISE Backup has failed",
        "description": "ISE Backup has failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60098": {
        "alert_group": "ISE Log Backup has failed",
        "description": "ISE Log Backup has failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60101": {
        "alert_group": "ISE Restore has failed",
        "description": "ISE Restore has failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60103": {
        "alert_group": "Application installation failed",
        "description": "Application installation failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60106": {
        "alert_group": "Application remove failed",
        "description": "Application remove failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60107": {
        "alert_group": "Application upgrade failed",
        "description": "Application upgrade failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60112": {
        "alert_group": "Application patch remove has failed",
        "description": "Application patch remove has failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60126": {
        "alert_group": "Application patch installation failed",
        "description": "Application patch installation failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60127": {
        "alert_group": "Maximum number of concurrent CLI sessions has been reached",
        "description": "Maximum number of concurrent CLI sessions has been reached",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60128": {
        "alert_group": "Failure occurred trying to copy file in from ADEOS CLI",
        "description": "Failure occurred trying to copy file in from ADEOS CLI",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60129": {
        "alert_group": "Failure occurred trying to copy file out from ADEOS CLI",
        "description": "Failure occurred trying to copy file out from ADEOS CLI",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60133": {
        "alert_group": "ISE Support bundle generation from web UI has failed",
        "description": "ISE Support bundle generation from web UI has failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60152": {
        "alert_group": "Slow Replication",
        "description": "Replication is slow",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60157": {
        "alert_group": "Unable to copy the exported report file to configured repository",
        "description": "Copying the exported report file to configured repository failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60161": {
        "alert_group": "Failed to update Posture requirements from the remote feed URL",
        "description": "The Posture update from the remote feed URL has failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60164": {
        "alert_group": "NTP Service is down on the node",
        "description": "NTP Service is down on the node",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60165": {
        "alert_group": "NTP failed to sync with configured servers",
        "description": "NTP failed to sync with the configured servers",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60174": {
        "alert_group": "Could not add Certificate Revocation List",
        "description": "Could not add Certificate Revocation List. The Certificate Revocation List will not be used by ISE",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60175": {
        "alert_group": "Could not download Certificate Revocation List",
        "description": "Could not download Certificate Revocation List. The Certificate Revocation List will not be used by ISE",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60177": {
        "alert_group": "Application upgrade preparation failed",
        "description": "Application upgrade preparation failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60191": {
        "alert_group": "Insufficient Virtual Machine Resources on node",
        "description": "Insufficient Virtual Machine Resources on node",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60192": {
        "alert_group": "Firmware update required on node",
        "description": "Firmware update required on node",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60223": {
        "alert_group": "PAN HA Promotion request failed",
        "description": "PAN HA Promotion request failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60225": {
        "alert_group": "Unable to build the certificate chain",
        "description": "Two or more certificates have been found with same value of CN attribute in the subject field leading to certificate chain building error",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60228": {
        "alert_group": "MSE Server is unreachable",
        "description": "MSE Server is unreachable",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60234": {
        "alert_group": "The SXP connection has been disconnected",
        "description": "The SXP connection has been disconnected",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031c_60459": {
        "alert_group": "SXP binding binding not propagated because binding threshold has been reached",
        "description": "SXP binding threshold reached",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0031d_30000": {
        "alert_group": "Unknown fatal management error",
        "description": "MGMT fatal unknown error. To recover try to re-run ISE",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_31102": {
        "alert_group": "Applying configuration changes failed",
        "description": "Applying configuration changes failed. Runtime process will restart",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_31104": {
        "alert_group": "Start up configuration load failed",
        "description": "Start up configuration load failed. Runtime process will go down",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_31106": {
        "alert_group": "Mus",
        "description": "Configuration management could not translate configuration change. Runtime configuration changes will not take effect",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_31108": {
        "alert_group": "Cold configuration restart failed",
        "description": "Cold configuration restart failed. Runtime process will restart",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_31504": {
        "alert_group": "The cryptographic module could not initialize",
        "description": "The cryptographic module could not initialize",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_32016": {
        "alert_group": "System reached low disk space limit",
        "description": "System reached low disk space limit. Change local storage cleanup settings to free space",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_32017": {
        "alert_group": "Could not to open a UDP socket",
        "description": "Could not open a UDP socket",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_34005": {
        "alert_group": "Policy cache sync failed",
        "description": "Failed to synchronize policy cache",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_34123": {
        "alert_group": "The virtual memory usage is high indicating the process may be running out of memory resources",
        "description": "The virtual memory is high indicating the process may be running out of memory resources",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_34124": {
        "alert_group": "Due to low memory resources the amount of concurrent EAP sessions will be limited",
        "description": "Due to low memory resources the amount of concurrent EAP sessions will be limited",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_34125": {
        "alert_group": "Due to low memory resources a CRL could not be updated",
        "description": "Due to low memory resources a CRL could not be updated",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031d_60134": {
        "alert_group": "DNS Resolution failure",
        "description": "DNS Resolution failure on node",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0031e": {
        "alert_group": "CPU Utilization",
        "description": "The CPU utilization is above xx%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00321": {
        "alert_group": "CPU usage of an Infoblox device exceeds 90%",
        "description": "This alarm is generated when the CPU usage of an Infoblox device exceeds 90%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00323": {
        "alert_group": "Used memory of an Infoblox device exceeds 90%",
        "description": "This alarm is generated when the amount of used memory of an Infoblox device exceeds 90%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00325": {
        "alert_group": "BIGIP TMMERR DOS ATTACK START",
        "description": "Device under DOS attack",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00327": {
        "alert_group": "Memory usage exceeds 80%",
        "description": "Memory usage exceeds 80%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00329": {
        "alert_group": "State of CIMC connectivity changes",
        "description": "This event publishes notifications to any subscriber, when the state of DNA CIMC connectivity changes. i.e CIMC is configured or CIMC authentication is failing or CIMC IP is unreachable.",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0032a": {
        "alert_group": "State of hardware changes",
        "description": "This event publishes notifications to any subscriber, when the health state of any Hardware component changes. Hardware components supported are: CPU, Memory, Disk, NIC, Fan and Power Supply, and RAID Controller.",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0032b": {
        "alert_group": "State of managed services",
        "description": "The event is generated on state change of platform provided managed services (For eg: MongoDB, Minio, ElasticSearch etc)",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0032c": {
        "alert_group": "Backup failed",
        "description": "The event is generated on failure during backup operation",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0032d": {
        "alert_group": "Certificate (almost) expired",
        "description": "This event publishes notifications to any subscriber, when the status of the System Certificate (eg; Proxy Certificate, DR Certificate, Built-in Certificate) is expired, revoked or about to expire in less than 90 days.",  # noqa: E501
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0032f": {
        "alert_group": "The WLC no longer is able to communicate with the access point",
        "description": "The WLC no longer is able to communicate with the access point",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00330": {
        "alert_group": "CPU usage exceeds 60%",
        "description": "CPU usage exceeds 60%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00331": {
        "alert_group": "Memory Utilization",
        "description": "The memory utilization is above xx%",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00333": {
        "alert_group": "ciscoMemoryPoolValid",
        "description": "Memory validity check on internal errors",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00335": {
        "alert_group": "linkDown",
        "description": "linkDown",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00337": {
        "alert_group": "Attack event",
        "description": "Attack event",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00338": {
        "alert_group": "CPU high threshold",
        "description": "CPU high threshold",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00339": {
        "alert_group": "Memory low threshold",
        "description": "Memory low threshold",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0033a": {
        "alert_group": "High log disk usage",
        "description": "High log disk usage",
        "clear_type": 1,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff0033b": {
        "alert_group": "Card failure",
        "description": "Card failure",
        "clear_type": 1,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0033c": {
        "alert_group": "Instantaneous CPU utilization",
        "description": "Instantaneous CPU utilization",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0033e": {
        "alert_group": "Instantaneous MEM utilization",
        "description": "Instantaneous MEM utilization",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00340": {
        "alert_group": "Log disk usage",
        "description": "Log disk usage",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff00347": {
        "alert_group": "LAN2 port link is down",
        "description": "LAN2 port link is down. Please check the connection",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00349": {
        "alert_group": "HA port link is down",
        "description": "HA port link is down. Please check the connection",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0034b": {
        "alert_group": "MGMT port link is down",
        "description": "MGMT port link is down. Please check the connection",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0034d": {
        "alert_group": "DHCP Service Failed",
        "description": "DHCP Service Failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0034f": {
        "alert_group": "DNS Service Failed",
        "description": "DNS Service Failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00351": {
        "alert_group": "NTP Service Failed",
        "description": "NTP Service Failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00353": {
        "alert_group": "Reporting Service failed",
        "description": "Reporting Service failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00355": {
        "alert_group": "System has run out of memory",
        "description": "System has run out of memory",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00357": {
        "alert_group": "Primary drive is full",
        "description": "Primary drive is full",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00359": {
        "alert_group": "CPU usage above threshold",
        "description": "CPU usage above threshold",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0035b": {
        "alert_group": "System swap space usage exceeds the critical threshold value",
        "description": "System swap space usage exceeds the critical threshold value",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0035d": {
        "alert_group": "Root file system is full",
        "description": "Root file system is full",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0035f": {
        "alert_group": "Reporting is full",
        "description": "Reporting is full",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00361": {
        "alert_group": "System memory usage exceeds the critical threshold value",
        "description": "System memory usage exceeds the critical threshold value",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00363": {
        "alert_group": "System primary hard disk usage is over the threshold value",
        "description": "System primary hard disk usage is over the threshold value",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00365": {
        "alert_group": "Root file system disk usage is over threshold value",
        "description": "Root file system disk usage is over threshold value",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00367": {
        "alert_group": "Reporting drive usage is over threshold value",
        "description": "Reporting drive usage is over threshold value",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00369": {
        "alert_group": "Database capacity used is over the threshold value",
        "description": "Database capacity used is over the threshold value",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0036b": {
        "alert_group": "DNS security alert : actual DNS responses to invalid ports in the last minute",
        "description": "DNS security alert. There were actual DNS responses to invalid ports in the last minute",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0036c": {
        "alert_group": "A grid daemon failure has occurred",
        "description": "A grid daemon failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0036d": {
        "alert_group": "A controld failure has occurred",
        "description": "A controld failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0036e": {
        "alert_group": "A named daemon monitoring failure has occurred",
        "description": "A named daemon monitoring failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff0036f": {
        "alert_group": "A DHCP daemon monitoring failure has occurred",
        "description": "A DHCP daemon monitoring failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00370": {
        "alert_group": "Syslog daemon is not running",
        "description": "Syslog daemon is not running",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00371": {
        "alert_group": "An NTP daemon failure has occurred",
        "description": "An NTP daemon failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00372": {
        "alert_group": "Forwarder is running but it fails to connect to any of the indexers",
        "description": "Forwarder is running but it fails to connect to any of the indexers",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00373": {
        "alert_group": "An SSH daemon failure has occurred",
        "description": "An SSH daemon failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00374": {
        "alert_group": "An Infoblox Serial Console software failure has occurred",
        "description": "An Infoblox Serial Console software failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00375": {
        "alert_group": "DNS Health Check query failed",
        "description": "DNS Health Check query failed",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00376": {
        "alert_group": "An SNMP server failure has occurred",
        "description": "An SNMP server failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00377": {
        "alert_group": "A zone transfer failure occurred",
        "description": "A zone transfer failure occurred",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00378": {
        "alert_group": "An unbound daemon monitoring failure has occurred",
        "description": "An unbound daemon monitoring failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0037b": {
        "alert_group": "A power supply failure has occurred",
        "description": "A power supply failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0037d": {
        "alert_group": "Power supply 1 failure has occurred",
        "description": "Power supply 1 failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff0037f": {
        "alert_group": "Power supply 2 failure has occurred",
        "description": "Power supply 2 failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.MAJOR.value,
        "severity_nonprod": Severity.MAJOR.value,
    },
    "fff00381": {
        "alert_group": "A zone transfer failure has occurred",
        "description": "A zone transfer failure has occurred",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00382": {
        "alert_group": "The system stopped and started a process",
        "description": "The system stopped and started a process",
        "clear_type": 2,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff00383": {
        "alert_group": "A license is revoked",
        "description": "This trap is generated when a license is revoked",
        "clear_type": 2,
        "severity_prod": Severity.CRITICAL.value,
        "severity_nonprod": Severity.CRITICAL.value,
    },
    "fff00384": {
        "alert_group": "ClusterXL failover",
        "description": "ClusterXL failover",
        "clear_type": 2,
        "severity_prod": Severity.WARNING.value,
        "severity_nonprod": Severity.WARNING.value,
    },
    "fff003a0": {
        "alert_group": "Interface GigabitEthernet1/1 or 1/2 is down for more than 3 hours",
        "description": "Interface GigabitEthernet1/1 or 1/2 is down for more than 3 hours",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff003a4": {
        "alert_group": "Fortinet Interface down for more than 60 seconds",
        "description": "Fortinet Interface down for more than 60 seconds",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff003a6": {
        "alert_group": "AP disassociated from WLC for more than 5 minutes",
        "description": "AP disassociated from WLC for more than 5 minutes",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
    "fff003a7": {
        "alert_group": "AP disassociated from WLC more than 5 times within one hour",
        "description": "AP disassociated from WLC more than 5 times within one hour",
        "clear_type": 2,
        "severity_prod": Severity.MINOR.value,
        "severity_nonprod": Severity.MINOR.value,
    },
}
