"""Details Design module for mon-scada."""

import pandas as pd
from sqlalchemy.orm import Session

from mon_scada import common, csj, kst_bns, statics
from mon_scada import enums as scada_enums
from mon_scada.config import config
from olympus_common import db, enums
from olympus_common import pd as olympus_pd
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the SCADA Details Design."""
    agent_name = common.agent()
    agent_id = db.Agent.get_agent_id_from_name(agent_name, session)
    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    df = olympus_pd.clean_raw_dataframe(df=df, columns_renaming=statics.COLUMNS_RENAMING)

    # Hardcoded fields
    df["agent_id"] = agent_id
    df["manager"] = common.manager()
    df["node"] = common.node()
    df["node_alias"] = df["node"]
    df["handle_time"] = common.handle_time()
    df["additional_data"] = common.additional_data()
    df["action_class"] = common.action_class()
    df["clear_type"] = common.clear_type()
    df["metric_type"] = common.metric_type()
    df["delay"] = common.delay()

    # Common computed fields
    df["summary"] = df.apply(common.summary, axis=1)
    df["wake_up_time"] = df.apply(common.wake_up_time, axis=1)
    # df["event_type"] = df.apply(common.event_type, axis=1) the code has been removed on the passport.
    df["raise_time"] = df.apply(common.raise_time, axis=1)

    # CSJ computed fields
    if config.scada_cc == scada_enums.ScadaCC.CSJ.value:
        df["ci_id"] = df.apply(csj.ci_id, axis=1)
        df["metric_name"] = df.apply(csj.metric_name, axis=1)
        df["severity"] = df.apply(csj.severity, axis=1)
        df["clear_time"] = df.apply(csj.clear_time, axis=1)

    # KST_BNS computed fields
    elif config.scada_cc in [scada_enums.ScadaCC.KST.value, scada_enums.ScadaCC.BNS.value]:
        df["ci_id"] = df.apply(kst_bns.ci_id, axis=1)
        df["metric_name"] = df.apply(kst_bns.metric_name, axis=1)
        df["severity"] = df.apply(kst_bns.severity, axis=1)
        df["clear_time"] = df.apply(kst_bns.clear_time, axis=1)
    else:
        raise ValueError(f"scada_cc {config.scada_cc} does not exist.")

    return df
