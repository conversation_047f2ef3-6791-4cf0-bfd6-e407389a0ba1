"""Configuration for end-to-end tests."""

import json
from pathlib import Path

import pytest
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import text
from sqlalchemy.orm.session import close_all_sessions

from olympus_common.config import DatabaseConfig
from tests.end2end.utils import create_test_session, dummy_db_env, find_folder, replace_tokens


@pytest.fixture(autouse=True)
def _mock_env(mocker: MockerFixture):
    """Mock os.environ with DB-credentials and dummy-values for e2e tests.

    References
    ----------
    https://docs.pytest.org/en/6.2.x/fixture.html#autouse-fixtures-fixtures-you-don-t-have-to-request
    """
    mocker.patch.dict("os.environ", dummy_db_env(add_missing=True))


@pytest.fixture(autouse=True, scope="session")
def _database(session_mocker: MockerFixture):
    """Set up the database for the e2e tests.

    We tear down the database before the tests to ensure that we always start with a clean database.
    This is also useful to be able to inspect the database after a test has failed.

    The scope is set to session to ensure that the database is only set up once for all e2e tests.
    """
    session_mocker.patch.dict("os.environ", dummy_db_env(add_missing=False))
    tear_down_database()  # Ensure all data has been removed.
    set_up_database()
    seed_database()


def set_up_database():
    """Set up the database for the tests."""
    fp = find_folder(Path(__file__), "scripts/sql")

    dummy_db_details = dummy_db_env(add_missing=False)
    schema = dummy_db_details["DB_SCHEMA"]
    user = dummy_db_details["DB_USER"]

    texts = []
    for sqlfile in fp.glob("*.sql"):
        sqltext = "\n".join(replace_tokens([sqlfile], schema=schema, user=user))
        texts.append(sqltext)

    execute_many(texts)


def seed_database():
    """Seed the database with data for the tests."""
    fp = find_folder(Path(__file__), "data/seed")
    tablesfile = fp / "tables.json"
    if not tablesfile.exists():
        return  # No seed data available, skip seeding.

    tables_info = json.loads(tablesfile.read_text())
    dummy_db_details = dummy_db_env(add_missing=False)
    test_schema = dummy_db_details["DB_SCHEMA"]
    statements = []
    for table_info in tables_info:
        table_name = table_info["table"]
        source_schema = table_info["schema"]
        copy_stmt = f"INSERT INTO {test_schema}.{table_name} SELECT * FROM {source_schema}.{table_name}"  # noqa: S608
        statements.append(copy_stmt)
    execute_many(statements)

    # Always insert a dummy agent to be used for agent clears and other tests than service tests.
    stmt = f"INSERT INTO {test_schema}.s2110_agent (id, name, ucmdb_name) VALUES (1337, 'dummy_agent', 'dummy') "  # noqa: S608
    execute_one(stmt)


def tear_down_database():
    """Tear down the database after the tests.

    Since we do not have a migration strategy, we just truncate all tables in the test schema.
    """
    databaseconfig = DatabaseConfig()
    session = create_test_session()
    stmt = (
        f"SELECT table_name, table_type FROM information_schema.tables WHERE table_schema = '{databaseconfig.schema}'"  # noqa: S608
    )
    tables = session.execute(text(stmt)).fetchall()

    stmt = f"SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = '{databaseconfig.schema}'"  # noqa: S608
    seqs = session.execute(text(stmt)).fetchall()
    close_all_sessions()  # Close all sessions to ensure we can drop everyting without waiting for locks.

    statements = []
    for table_name, table_type in tables:
        if table_type == "BASE TABLE":
            stmt = f"DROP TABLE IF EXISTS {databaseconfig.schema}.{table_name} CASCADE"
        elif table_type == "VIEW":
            stmt = f"DROP VIEW IF EXISTS {databaseconfig.schema}.{table_name} CASCADE"
        statements.append(stmt)
    execute_many(statements)

    statements = [f"DROP SEQUENCE IF EXISTS {databaseconfig.schema}.{seq}" for (seq,) in seqs]
    execute_many(statements)


def copy_from_stdin(tablename: str, data: str):
    """Copy data from a string to the database.

    The data should be in CSV format.
    """
    session = create_test_session()
    cursor = session.connection().connection.cursor()

    try:
        with cursor.copy(f"COPY {tablename} FROM stdin WITH (FORMAT csv, HEADER true, DELIMITER ',');") as copy:
            copy.write(data)

        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()


def execute_one(stmt: str):
    """Execute a single statement on the database.

    We just execute_many with a single-item list to reduce code duplication.
    """
    execute_many([stmt])


def execute_many(stmts: list[str]):
    """Execute multiple statements on the database.

    The connection is closed after the statements are executed and committed.
    """
    session = create_test_session()
    try:
        for stmt in stmts:
            session.execute(text(stmt))
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()
