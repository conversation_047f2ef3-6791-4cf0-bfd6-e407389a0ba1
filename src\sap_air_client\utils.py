"""Module for useful functions."""

import json
import logging
import re
from collections.abc import Callable
from datetime import datetime
from json.decoder import <PERSON><PERSON><PERSON>ecodeError
from typing import Any, Coroutine, Optional

from requests import ConnectionError, HTTPError, RequestException, Response, Timeout
from sqlalchemy.orm import Session

from olympus_common import enums, icinga
from olympus_common.db import Alarm, AlarmIncident, AlarmRelease, Incident, Occurrence, Release, ReleaseCI
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import AlarmType, ClearType, MeasureType, Severity
from olympus_common.utils import now_naive
from sap_air_client.config import config
from sap_air_client.statics import DUMMY_FLOC_ID, EXPAND_DATA

client = icinga.IcingaClient(use_logging=True)


@CaptureSpan(MeasureType.CUSTOM.value)
def handle_sap_request(func: Callable[..., Response]) -> Callable[..., Coroutine]:
    """Decorate to run func as asynchronous and  handler exceptions after http requests for SAP."""

    async def wrapper(*args, **kwargs):
        try:
            response = func(*args, **kwargs)
            response.raise_for_status()
            if response.content:
                resp = parser_response(response.json())
                resp = remove_metadata(resp, EXPAND_DATA)
                if isinstance(resp, list) and len(resp) != 0:
                    return response.status_code, resp[0]
                return response.status_code, resp
            else:
                # return status code and empty dict for empty reponses due to patch request's nature
                return response.status_code, {}
        except HTTPError as errh:
            logging.exception(errh)
            return errh.response.status_code, errh.response.text
        except ConnectionError as errc:
            logging.exception(errc)
            return errc.response.status_code, errc.response.text
        except Timeout as errt:
            logging.exception(errt)
            return errt.response.status_code, errt.response.text
        except RequestException as err:
            raise Exception(f"Something went wrong with  the SAP request: {err}") from err

    return wrapper


@CaptureSpan(MeasureType.CUSTOM.value)
def handle_sap_create_request(func: Callable[..., Response]) -> Callable[..., Coroutine]:
    """Decorate to run func as asynchronous and  handler exceptions after http requests for SAP."""

    async def wrapper(*args, **kwargs):
        try:
            response = func(*args, **kwargs)
            if 400 <= response.status_code < 600:
                # Special treatment for some error codes.
                # We expect the "unknown floc id" to be received before the "release rejected".
                # If this order changes, we'll have to adapt.
                entity = args[1]
                floc_id = entity.floc_id
                if config.app_env != "prd" and is_error_code(response, "/IWFND/CM_BEC/029"):
                    # Unknown floc ID: replace with A2110 for non-prod. Otherwise crash.
                    entity.floc_id = DUMMY_FLOC_ID
                    response = func(*args, **kwargs)
                if 400 <= response.status_code < 600:
                    if is_error_code(response, "C2/009"):
                        # Release rejected: most likely, the floc ID is not linked to a cost object
                        # in SAP => Create the main order in non-released state.
                        entity.flag_main_order_release = False
                        response = func(*args, **kwargs)
                entity.floc_id = floc_id
            response.raise_for_status()
            resp = parser_response(response.json())
            resp = remove_metadata(resp, EXPAND_DATA)
            return response.status_code, resp[0]
        except JSONDecodeError as errj:
            logging.exception(errj)
            return 500, str(errj)
        except HTTPError as errh:
            logging.exception(errh)
            return errh.response.status_code, errh.response.text
        except ConnectionError as errc:
            logging.exception(errc)
            return errc.response.status_code, errc.response.text
        except Timeout as errt:
            logging.exception(errt)
            return errt.response.status_code, errt.response.text
        except RequestException as err:
            raise Exception(f"Something went wrong with  the SAP request: {err}") from err

    return wrapper


@CaptureSpan(MeasureType.CUSTOM.value)
def handle_air_request(func: Callable[..., Response]) -> Callable[..., Coroutine]:
    """Decorate to run func as asynchronous and  handler exceptions after http requests for AIR."""

    async def wrapper(*args, **kwargs) -> Any:
        try:
            response = func(*args, **kwargs)
            response.raise_for_status()
            return response.json()
        except HTTPError as errh:
            logging.exception(errh)
        except ConnectionError as errc:
            logging.exception(errc)
        except Timeout as errt:
            logging.exception(errt)
        except RequestException as err:
            logging.exception(err)
            raise Exception(f"Something went wrong with the air request: {err}") from err
        return {}

    return wrapper


@CaptureSpan(MeasureType.CUSTOM.value)
def parser_response(response: list[dict]) -> list[dict]:
    """Parse response."""
    data: list[dict] = []
    if isinstance(response, dict):
        if len(response) == 1:
            responses = response.values()
            for resp in responses:
                if isinstance(resp, dict) and "results" in resp.keys():
                    return resp["results"]
                data.append(resp)
        return data
    else:
        return response


@CaptureSpan(MeasureType.CUSTOM.value)
def remove_metadata(response: list[dict], expand: list | None = None) -> list[dict]:
    """Remove metadata in SAP responses."""
    data: list[dict] = []
    if isinstance(response, list) and len(response) != 0:
        for resp in response:
            if isinstance(resp, dict):
                resp.pop("__metadata", None)
                if expand and isinstance(expand, list):
                    for key in expand:
                        if key in resp.keys() and "results" in resp[key].keys():
                            val = resp[key]["results"]
                            if len(val) != 0:
                                for v in val:
                                    v.pop("__metadata", None)
                            resp[key] = val
            data.append(resp)
        return data
    else:
        return response


def get_clean_attachments_field(attachments: list):
    """Get essential fields from attachments."""
    for attachment in attachments:
        return {
            "Filename": attachment["Filename"],
            "CreationDate": convert_sap_date_string(attachment["CreationDate"]),
            "ChangedDate": convert_sap_date_string(attachment["ChangedDate"]),
        }


def construct_service_name(host_name: str | None, metric_type: str | None, metric_name: str | None) -> str:
    """Return the service name of this alarm."""
    return f"{host_name}!{metric_type}{metric_name}"


@CaptureSpan(MeasureType.CUSTOM.value)
def update_backend_alarm_incident_link(
    sap_incident_id: str,
    incident_id: int | None,
    alarm: Alarm,
    is_origin_alarm: bool,
    add_link: bool,
    session: Session,
    sap_status: str | None = None,
    user_id: str | None = None,
) -> None:
    """Update the link in the backend between an alarm and an incident."""
    # Look for the Incident object.
    if not incident_id:
        if not (incident := Incident.get_by_sap_id(sap_incident_id, session)):
            if not add_link:
                raise ValueError("No incident was found.")
            incident = Incident(sap_id=sap_incident_id, sap_status=sap_status)
            new_incident = incident.insert_object(session)
            if not new_incident:
                raise ValueError("Error while inserting the incident.")
            incident = new_incident

        incident_id = incident.id

    alarm_incident = AlarmIncident(
        alarm_id=alarm.id,
        incident_id=incident_id,  # type: ignore [arg-type]
        is_origin_alarm=is_origin_alarm,
        is_link_active=True,
    )

    if add_link:
        alarm_incident.link_user_id = user_id
        alarm_incident.link_time = now_naive()
        AlarmIncident.insert_one(alarm_incident, session)

        # Re-send the alarm to the UI, if the incident is resolved. If the incident object does not exist, it means
        # the incident_id was provided, i.e. we're in a "create SAP incident" situation, and the incident should
        # not be resolved at this point.
        if sap_status == "RESO":
            alarm.resend_to_ui(session)
    else:
        # For an unlink, also re-send the alarm to the UI as the alarm could be cleared already => Would then be closed
        # by icinga-events.
        alarm_incident.unlink_user_id = user_id
        alarm_incident.unlink_time = now_naive()
        alarm_incident.disable(session)
        alarm.resend_to_ui(session)


@CaptureSpan(MeasureType.CUSTOM.value)
def update_backend_sap_status(entity: Incident | Release, sap_status: str, db_session: Session) -> None:
    """Update the status of an incident in the backend."""
    if isinstance(entity, Incident):
        # Update the incident status in backend.
        Incident.update_sap_status(entity.sap_id, sap_status, db_session)
        # Re-send all the linked alarms to the UI.
        if alarms := AlarmIncident.get_alarms_linked_to_incident(sap_id=entity.sap_id, session=db_session):
            for alarm in alarms:
                alarm.resend_to_ui(session=db_session)
    if isinstance(entity, Release):
        # Update the Release status in backend.
        Release.update_sap_status(entity.sap_id, sap_status, db_session)
        # Re-send all the linked alarms to the UI
        if alarms := AlarmRelease.get_alarms_linked_to_release(sap_id=entity.sap_id, session=db_session):
            for alarm in alarms:
                alarm.resend_to_ui(session=db_session)


@CaptureSpan(MeasureType.CUSTOM.value)
def handle_update_sap_status(entity: Incident | Release | None, response: dict, db_session: Session) -> None:
    """Handle the update of the sap status."""
    if isinstance(entity, Incident):
        if ("StatOrdUsrSeq" in list(response.keys())) & (entity.sap_status != response["StatOrdUsrSeq"]):
            update_backend_sap_status(entity, response["StatOrdUsrSeq"], db_session)
    if isinstance(entity, Release):
        if ("UserStatus" in list(response.keys())) & (entity.sap_status != response["UserStatus"]):
            update_backend_sap_status(entity, response["UserStatus"], db_session)


@CaptureSpan(MeasureType.CUSTOM.value)
def is_error_code(response: Response, error_code: str) -> bool:
    """Check if the response has the provided error code."""
    error_content = response.content.decode("utf-8")
    error_json = json.loads(error_content)
    if "error" in error_json and "code" in error_json["error"] and error_json["error"]["code"] == error_code:
        return True
    return False


@CaptureSpan(MeasureType.CUSTOM.value)
def clear_alarm_manually(session: Session, alarm_id: int, user_id: str) -> None:
    """Clear an alarm manually to Icinga, and update the alarm and occurrence table."""
    if not (alarm := Alarm.get_alarm_by_id(session, alarm_id)):
        raise ValueError("Alarm not found")

    current_time = now_naive()
    new_occurrence = {
        "alarm_id": alarm.id,
        "raise_time": current_time,
        "clear_time": current_time,
        "handle_time": current_time,
        "severity": Severity.CLEARED.value,
        "event_type": AlarmType.RESOLUTION.value,
        "metric_type": alarm.metric_type,
        "metric_name": alarm.metric_name,
        "agent_id": alarm.agent_id,
        "ci_id": alarm.ci_id,
        "additional_data": {"cleared_by": user_id},
        "summary": "Manually cleared",
        "clear_type": ClearType.MANUALLY.value,
    }

    # insert the new occurrence
    Occurrence.insert_one(new_occurrence, session)


@CaptureSpan(MeasureType.CUSTOM.value)
def convert_sap_date_string(date_string: str | None) -> datetime | None:
    """Extract the date from the date string."""
    # extract this type of datetime format -> "/Date(1720508400000)/"
    if not date_string:
        return None
    match = re.search(r"Date\((\d+)\)", date_string)
    if not match:
        raise ValueError("Invalid date")
    timestamp = int(match.group(1)) / 1000
    return datetime.fromtimestamp(timestamp)


def convert_datetime_sap_to_str(date_str: str | None) -> str | None:
    """Convert the datetime to a string."""
    date_object = convert_sap_date_string(date_str)
    if not date_object:
        return None
    return date_object.strftime("%Y-%m-%d %H:%M:%S")


def sanitize_fields(response: dict[str, Any]) -> dict:
    """Clean the dict and returns the dict without the keys that present null values."""
    return {key: value for key, value in response.items() if value}


def update_icinga_sap_release(
    host_name: str | None,
    metric_type: str | None,
    metric_name: str | None,
    release_data: dict,
) -> None:
    """Update the icinga status of a SAP service."""
    client.update(
        object_type=enums.IcingaObjectType.SERVICE,
        name=icinga.encode_string(construct_service_name(host_name, metric_type, metric_name)),
        attrs={
            "attrs": icinga.format_icinga_attrs(
                module_name="Sap",
                attrs={
                    "Qmnum": "R%s" % release_data["Id"],
                    "StatOrdUsrSeqTxt": release_data["UserStatusText"],
                    "TTMainAlarm": enums.SapMainAlarm.NO,
                },
            )
        },
    )


@CaptureSpan(MeasureType.CUSTOM.value)
def create_release_object(release_data: dict, external_release_id: Optional[str], comment: Optional[str]) -> Release:
    """Create a Release object from release data."""
    return Release(
        sap_id=str(release_data["Id"]),
        comment=comment,
        sap_status=release_data["UserStatus"],
        external_id=external_release_id,
        start_time=convert_sap_date_string(release_data["PlannedStartDate"]),
        end_time=convert_sap_date_string(release_data["PlannedEndDate"]),
    )


@CaptureSpan(MeasureType.CUSTOM.value)
def create_and_link_release(
    session: Session,
    release_data: dict,
    alarm_id: int,
    external_release_id: Optional[str],
    comment: Optional[str],
    ci_id: Optional[str],
    floc_id: Optional[str],
    user_id: Optional[str],
) -> None:
    """Create release objects and link them to the alarm."""
    # Create Release object
    try:
        release = create_release_object(release_data, external_release_id, comment)

        if not (new_release := Release.insert_one(release, session)):
            # Release already exists, do nothing.
            raise ValueError("Release already exists")

        release_ci = ReleaseCI(release_id=new_release.id, floc_id=floc_id, ci_id=ci_id)
        release_ci.insert_object(session)

        # Create AlarmRelease object
        alarm_release = AlarmRelease(
            alarm_id=alarm_id,
            release_id=new_release.id,
            is_link_active=True,
            link_time=now_naive(),
            link_user_id=user_id,
        )
        AlarmRelease.insert_one(alarm_release, session)
    except Exception as exc:
        logging.error(f"create_and_link_release - Failed: {exc}")
        raise exc
