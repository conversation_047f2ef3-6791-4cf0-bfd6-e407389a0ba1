"""Static modules for mon-certificates.

This contains the static variables for mon-certificates.
"""

from mon_certificates.enums import ExpirySeverities, TalosContacts

# Global
MANAGER_NAME = "mon-certificates"
AGENT_NAME = "Certificates"
CI_ID = "Certificates_monitoring"
INFORMATIONAL_LIMIT = 75
WARNING_LIMIT = 45
CRITICAL_LIMIT = 21
EMERGENCY_LIMIT = 14
EXPIRED_LIMIT = 0
SEVERITY_FREQUENCIES = {
    ExpirySeverities.WARNING.value: 30,
    ExpirySeverities.CRITICAL.value: 1,
    ExpirySeverities.EMERGENCY.value: 1,
}

# SYSLOG
SYSLOG_LOGGER_NAME = "syslog_logger"
SYSLOG_HITS_FILENAME = "syslog_hits.log"
CHANNEL = "python"


# NACADCA
NACADCA_COLUMNS_RENAMING_DICT = {
    "_source.x509.subject.common_name": "common_name",
    "_source.x509.issuer.common_name": "issuer",
    "_source.x509.subject.organizational_unit": "organizational_unit",
    "_source.x509.expiry_days": "expiry_days",
    "_source.x509.not_before": "not_before",
    "_source.x509.not_after": "not_after",
    "_source.x509.subject.user.email": "recipients",
    "_source.x509.subject.manager": "manager",
    "_source.x509.subject.head_of": "head_of",
    "_source.x509.subject.team_lead": "team_lead",
}
NACADCA_GROUP_COLUMN = "organizational_unit"
NACADCA_RECIPIENTS_COLUMNS: dict = {
    ExpirySeverities.WARNING.value: ["recipients"],
    ExpirySeverities.CRITICAL.value: ["recipients"],
    ExpirySeverities.EMERGENCY.value: ["recipients"],
}
NACADCA_CC_RECIPIENTS_COLUMNS: dict = {
    ExpirySeverities.WARNING.value: ["team_lead"],
    ExpirySeverities.CRITICAL.value: ["manager"],
    ExpirySeverities.EMERGENCY.value: ["manager", "head_of"],
}

# TALOS
TALOS_COLUMNS_RENAMING_DICT = {
    "_source.x509.subject.common_name": "common_name",
    "_source.x509.issuer.common_name": "issuer",
    "_source.x509.expiry_days": "expiry_days",
    "_source.x509.not_after": "not_after",
    "_source.x509.application.code": "application_code",
}
TALOS_CONTACT_COLUMNS = {
    TalosContacts.PROGRAM_MANAGER.value: "buildProgramManager",
    TalosContacts.PROJECT_LEADER.value: "buildProjectLeader",
    TalosContacts.APPLICATION_MANAGER.value: "runApplicationManager",
}
TALOS_GROUP_COLUMN = "application_code"
TALOS_RECIPIENTS_COLUMNS: dict = {
    ExpirySeverities.WARNING.value: ["project_leader", "application_manager"],
    ExpirySeverities.CRITICAL.value: ["project_leader", "application_manager"],
    ExpirySeverities.EMERGENCY.value: ["project_leader", "application_manager"],
}
TALOS_CC_RECIPIENTS_COLUMNS: dict = {
    ExpirySeverities.WARNING.value: ["program_manager"],
    ExpirySeverities.CRITICAL.value: ["program_manager"],
    ExpirySeverities.EMERGENCY.value: ["program_manager"],
}
