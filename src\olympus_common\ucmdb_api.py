"""Module to handle calls to the UCMDB API."""

from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any

import requests
from cachetools import TTLCache, cached

from olympus_common.config import ExternalAPIConfig


@dataclass
class UcmdbSession:
    """Class to handle calls to UCMDB."""

    token: str = ""
    token_expiration: datetime = datetime.now()

    def get_header(self, config: ExternalAPIConfig) -> dict:
        """Get the authentication header for UCMDB."""
        if self.token_expiration <= datetime.now():
            user_credentials = get_ucmdb_credentials(config)
            response = requests.post(user_credentials["token_url"], data=user_credentials, timeout=30)
            response.raise_for_status()  # Potentially raises requests.HTTPError
            self.token = response.json()["access_token"]  # Potentially raises requests.JSONDecodeError
            self.token_expiration = datetime.now() + timedelta(0, config.token_expiration)

        return {"authorization": f"Bearer {self.token}"}


def _get_data_from_ucmdb_key(*args, endpoint: str, params: str, config: ExternalAPIConfig, **kwargs):
    return f"{config.api_url}/{endpoint}?{params}"


@cached(cache=TTLCache(maxsize=512, ttl=900), key=_get_data_from_ucmdb_key)
def get_data_from_ucmdb(endpoint: str, params: str, headers: dict, config: ExternalAPIConfig) -> Any:
    """Get data from the ucmdb api."""
    response = requests.get(url=f"{config.api_url}/{endpoint}?{params}", headers=headers, timeout=30)
    response.raise_for_status()
    return response.json()


def post_data_to_ucmdb(endpoint: str, headers: dict, config: ExternalAPIConfig, **kwargs) -> Any:
    """Post data to the ucmdb api.

    Notes
    -----
    `kwargs` should be used to pass additional parameters to the requests.post function.
    This is done to allow this function to be used for `json` and `files` parameters as well as `data`.
    """
    response = requests.post(url=f"{config.api_url}/{endpoint}", headers=headers, timeout=30, **kwargs)
    response.raise_for_status()
    return response.json()


def get_ucmdb_credentials(config: ExternalAPIConfig) -> dict:
    """Construct the UCMDB credentials."""
    return {
        "token_url": config.token_url,
        "grant_type": "client_credentials",
        "client_id": config.client_id,
        "client_secret": config.client_secret,
        "scope": config.scope,
    }
