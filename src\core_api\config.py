"""Configuration module for core-api."""

from dataclasses import dataclass

from olympus_common.config import BaseServiceConfig, DatabaseConfig, JWKConfig, ServerConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(BaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for core-api."""

    database_config: DatabaseConfig = dataclass_field(DatabaseConfig)
    server_config: ServerConfig = dataclass_field(ServerConfig)
    service_name: str = env_field("OLYMPUS_SERVICE_NAME", default="core_api")
    app_env: str = env_field("APP_ENV", default="dev")
    jwk_config: JWKConfig = dataclass_field(JWKConfig)


config = Config()
