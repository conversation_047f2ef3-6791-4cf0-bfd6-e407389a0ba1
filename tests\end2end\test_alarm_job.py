"""Tests for alarm jobs that required a database connection."""

import uuid

import pytest

from olympus_common.db import Alarm, AlarmJob, create_session


@pytest.mark.e2e
def test_alarm_job_updates_expected_ids():
    """Test that the alarm job updates all TO_DO records and those in_error that have not maxed their retry_count."""
    session = create_session("test_alarm_job")
    session.begin()
    alarm_id = _create_test_alarm(session, "alarm1")
    alarm_id_2 = _create_test_alarm(session, "alarm2")  # Second alarm to ensure only one is updated
    job_name = "ui_sending"
    _a2_job_0 = _create_test_alarm_job(session, alarm_id=alarm_id_2, job_name=job_name, job_status=0)
    a_job_0 = _create_test_alarm_job(session, alarm_id=alarm_id, job_name=job_name, job_status=0)
    _a_job_1 = _create_test_alarm_job(session, alarm_id=alarm_id, job_name=job_name, job_status=1).id
    _a_job_2 = _create_test_alarm_job(session, alarm_id=alarm_id, job_name=job_name, job_status=3).id
    _a_job_3_0 = _create_test_alarm_job(session, alarm_id=alarm_id, job_name=job_name, job_status=2).id
    session.commit()
    updated_ids = a_job_0.update(session)
    assert updated_ids == [a_job_0.id], "Update failed to return the correct IDs"


def _create_test_alarm(session, name):
    """Create a test alarm."""
    alarm = Alarm(agent_id=1337, ci_id=name, metric_type="test", metric_name="NOK", is_active=True)
    alarm.identifier = f"{name}/{uuid.uuid4()}"
    alarm = alarm.insert_object(session)
    if not alarm:
        pytest.fail("Failed to create test alarm")
    return alarm.id


def _create_test_alarm_job(session, alarm_id, job_name, job_status):
    """Create a test alarm job."""
    job = AlarmJob(alarm_id=alarm_id, job_name=job_name, job_status=job_status)
    job = job.insert_object(session)
    if not job:
        pytest.fail("Failed to create test alarm job")
    return job
