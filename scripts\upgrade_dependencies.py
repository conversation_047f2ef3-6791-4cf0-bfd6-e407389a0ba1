"""Upgrade the package's current dependencies.

NOTE: This upgrades the dependencies outside their specified boundaries.
"""

import subprocess
import tomllib
from collections.abc import MutableMapping
from pathlib import Path
from typing import Any


def _toml_file(path: Path | None = None) -> MutableMapping[str, Any]:
    if path is None:
        path = Path.cwd()
    path_glob = path.glob("pyproject.toml")
    current_toml = next(path_glob, None)
    if current_toml is None:
        raise FileNotFoundError(f"pyproject.toml was not found in {path}")
    return tomllib.loads(current_toml.read_text())


def _poetry_add_latest(packages: MutableMapping[str, Any], dev: bool = False) -> None:
    if not packages:
        return

    if dev:
        initial_command = ["poetry", "add", "--group", "dev"]
    else:
        initial_command = ["poetry", "add"]

    required: list[str] = []
    optional: list[str] = []
    for name, details in packages.items():
        title = f"{name}@latest"
        if isinstance(details, dict):
            if extras := details.get("extras"):
                extras_str = ",".join(extras)
                title = f"{name}[{extras_str}]@latest"

            if details.get("optional"):
                optional.append(title)
                continue
        required.append(title)

    if required:
        required_command = initial_command + required
        print("Running", required_command)
        subprocess.run(required_command)  # noqa: S603

    if optional:
        optional_command = initial_command + optional + ["--optional"]
        print("Running", optional_command)
        subprocess.run(optional_command)  # noqa: S603


def main() -> None:
    """Upgrade the poetry-managed project's dependencies."""
    pyproject_toml = _toml_file()
    tool_poetry = pyproject_toml["tool"]["poetry"]
    dependencies: MutableMapping[str, Any] = tool_poetry["dependencies"]
    dependencies.pop("python", None)
    _poetry_add_latest(dependencies)
    dev_dependencies = tool_poetry["group"]["dev"]["dependencies"]
    _poetry_add_latest(dev_dependencies, dev=True)


if __name__ == "__main__":
    main()
