"""Logger module for fwd-scom-optic.

This module is used to send alarms using a syslog handler.
"""

import logging
import os
from logging.handlers import SysLogHandler

from fwd_scom_optic import static
from fwd_scom_optic.config import config
from mon_scom import utils


def log_alarm(**data) -> None:
    """Send an alarm message to a distant syslog.

    Create an alarm message with the given information.
    Then send a message into the distant syslog correspondring to the given information.
    """
    data.setdefault("environment", config.app_env)
    syslog_logger = create_syslog_logger()
    message = _construct_message(**data)
    extra = _construct_extra(**data)
    syslog_logger.info(message, extra=extra)


def create_syslog_logger():
    """Create a logger to send events to a distant syslog with the defined format.

    Notes
    -----
    To keep a track of what was sent to the syslog, the logger also writes the logs into the console.
    """
    logger = logging.getLogger(static.SYSLOG_LOGGER_NAME)
    logger.propagate = False
    if not logger.hasHandlers():
        logger.setLevel(logging.INFO)
        formatter = _get_formatter()
        syslog_handler = SysLogHandler(
            address=(config.syslog_host, config.syslog_port), facility=static.SYSLOG_FACILITY
        )
        syslog_handler.setFormatter(formatter)
        logger.addHandler(syslog_handler)
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    return logger


def _construct_extra(**kwargs) -> dict:
    """Construct the extra information to send to the syslog.

    We do not need to add a logtime manually as syslog automatically adds it.
    """
    extra = {
        "hostname": static.HOSTNAME,
        "tag": static.TAG,
        "id": os.getpid(),
        "state": static.STATE,
        "channel": static.CHANNEL,
        "ci_id": kwargs.get("ci_id", "N/A"),
        "environment": kwargs.get("environment", "N/A"),
        "function": kwargs.get("metric_name", "N/A"),
    }
    return extra


def _construct_message(**kwargs) -> str:
    """Construct the message to send to the syslog."""
    message_data = {}
    context = kwargs.get("Context", "N/A")
    if context != "N/A":
        context_data = _get_context_data(context)
        message_data.update(context_data)

        param5 = utils.get_context_param_x(context, 5)
        if param5:
            message_data["context_Param5"] = param5

    keys = [
        "Description",
        "DisplayString",
        "Id",
        "Category",
        "MonitoringObjectDisplayName",
        "MonitoringObjectFullName",
        "MonitoringObjectHealthState",
        "Name",
        "NetbiosComputerName",
        "Priority",
        "ResolutionState",
        "Severity",
        "TimeRaisedUTC",
        "TimeResolvedUTC",
    ]
    message_data.update({key: kwargs[key] for key in keys if kwargs.get(key)})
    message_data = _limit_message_data(message_data)
    return ";;".join([f"{key}={value}" for key, value in message_data.items()])


def _limit_message_data(data: dict) -> dict:
    """Limit the message data to the maximum length.

    Only the first 2056 characters in the OPTIC optic.log events are processed by the OPTIC Syslog probe.
    To ensure the OPTIC parsing is successful, we limit certain fields to a specific amount of characters.
    """
    max_length_mapping = {
        "context_Data": 100,
        "context_EventDescription": 256,
        "context_Param5": 150,
        "Description": 600,
    }
    for key, max_length in max_length_mapping.items():
        if key not in data:
            continue
        data[key] = data[key][:max_length]
    return data


def _get_context_data(context: str):
    """Get the context information from the given root."""
    data = {}
    context_keys = [
        "BinaryPathName",
        "Data",
        "EventDescription",
        "LoggingComputer",
        "Name",
    ]
    root = utils.to_xml(context)
    for key in context_keys:
        element = root.find(key)
        if element is None or element.text is None:
            continue
        data[f"context_{key}"] = element.text.strip()
    return data


def _get_formatter():
    """Return the formatter to use."""
    format_string = (
        "%(hostname)s %(tag)s: [ID %(id)s local6.%(state)s] "
        "OPTIC_%(channel)s %(ci_id)s %(environment)s %(function)s %(asctime)s : %(message)s"
    )
    return logging.Formatter(format_string, datefmt="%Y/%m/%d %H:%M:%S")
