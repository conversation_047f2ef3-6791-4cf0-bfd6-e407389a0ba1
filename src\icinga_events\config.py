"""Configuration module for icinga-events."""

from dataclasses import dataclass

from olympus_common.config import DatabaseServiceConfig
from olympus_common.dataclass import env_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for the icinga-event module."""

    thread_number: int = env_field("THREAD_NUMBER", astype=int, default="16")
    max_retries: int = env_field("MAX_RETRIES", astype=int, default="1")
    app_env: str = env_field("APP_ENV", astype=str, default="dev")


config = Config()  # Initialize the singleton at import time.

config.logger_config.logs_folder.mkdir(exist_ok=True, parents=True)
