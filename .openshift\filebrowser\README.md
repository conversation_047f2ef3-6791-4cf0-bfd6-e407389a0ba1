# Introduction 
This folder contains the files for the deployment of a filebrowser permitting to see and maintain files on the different PVC of Olympus project

# Openshift documentation
https://confluence.infrabel.be/display/OKB/How-to+access+your+data+from+a+persistent+volume+claim+using+a+browser

# Add new PVC
To add a PVC you need to update to `.openshift/03-deployment.yml` file.</br>
In this file you need to mention the corresponding pvc in the section `spec.template.spec.volumes` as follow:</br>
```yaml
- name: my-pvc-to-add
  persistentVolumeClaim:
    claimName: my-pvc-to-add-#{appEnv}#
```
After that you need to mount the pvc on the filebrowser pod. To do that you need to update to `.openshift/03-deployment.yml`, in the section `spec.template.spec.containers` you can add:
```yaml
volumeMounts:
- mountPath: "/data/my-pvc-to-add"
  name: my-pvc-to-add
```
## IMPORTANT
* The `name` in the second part of the modification needs to match the `name` on the first part.
* All PVCs must use the replaceToken `#{appEnv}#`.
* For more readability we choose to also use the `name` after `/data/` in the mountPath.
* For a all new deployments, please change the password as described in [the openshift documentation](https://confluence.infrabel.be/display/OKB/How-to+access+your+data+from+a+persistent+volume+claim+using+a+browser#Howtoaccessyourdatafromapersistentvolumeclaimusingabrowser-Changethedefaultadminpassword).

# Deployment
The deployment is managed by an Azure pipeline.</br>
To deploy the filebrowser you can:
1. Use a branch if `main` is too far ahead or has commits that should not be released:
    * create a new branch beginning by release/sprint\<sprint number>.
    * create a cherry PR to a release branch.
2. Use Azure pipelines to create a release from the `main` branch:
    * Browse to [`Azure DevOps`](https://dev.azure.com/INFRABEL/a2110-olympus/_release?_a=releases&view=all) and find the Filebrowser release
    * Click the `Create release` button.

# Visit the filebrowser
Navigate to the `host` mentioned in `.openshift/filebrowser/05-route.yml`.<br/>
* For `dev`: [`filebrowser-a2110-olympus-dev-monitoring.apps.nonprod-ocp.infrabel.be`](https://filebrowser-a2110-olympus-dev-monitoring.apps.nonprod-ocp.infrabel.be/)
* For `acc`: [`filebrowser-a2110-olympus-acc-monitoring.apps.nonprod-ocp.infrabel.be`](https://filebrowser-a2110-olympus-acc-monitoring.apps.nonprod-ocp.infrabel.be/)