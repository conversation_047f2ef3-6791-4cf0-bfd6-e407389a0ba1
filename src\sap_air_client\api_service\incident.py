"""Module to interact with SAP Incidents."""

import datetime as dt
from dataclasses import dataclass, field

import urllib3
from requests import Response, Session

from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import MeasureType
from sap_air_client.config import config
from sap_air_client.models import SapDataToUpdate, SapEntityIncident
from sap_air_client.statics import EXPAND_DATA, ODATA_HEADER
from sap_air_client.utils import handle_sap_create_request, handle_sap_request

urllib3.disable_warnings()


@dataclass
class Incident:
    """Represent the Object to interact with SAP Incidents endpoint."""

    incident_set: str = "IncidentSet"
    feedback_set: str = "ProvideFeedback"
    endpoint: str = config.sap.incident_endpoint
    incident_path: str = str(endpoint) + str(incident_set)
    feedback_path: str = str(endpoint) + str(feedback_set)
    expand: list[str] = field(default_factory=lambda: EXPAND_DATA)
    class_name: str = "Sap"
    token_expiration: dt.datetime = dt.datetime.now()

    session: Session = Session()
    session.verify = False
    session.auth = (config.sap.user, config.sap.password)
    session.headers.update(ODATA_HEADER)

    @property
    def get_token(self) -> str:
        """Get token from SAP endpoint.

        Do not use self._request as it would cause a recursion error.
        """
        response = self.session.get(url=config.sap.incident_endpoint, headers=ODATA_HEADER)
        return response.headers["x-csrf-token"]

    @CaptureSpan(MeasureType.REQUEST.value)
    def _login(self) -> None:
        """Add token in the header if it is not yet present."""
        if self.token_expiration <= dt.datetime.now():
            self.session.headers["x-csrf-token"] = self.get_token
            self.token_expiration = dt.datetime.now() + dt.timedelta(minutes=25)

    @CaptureSpan(MeasureType.REQUEST.value)
    def _request(self, method: str, *args, **kwargs) -> Response:
        """Make a request to SAP endpoint, login if needed."""
        self._login()
        return self.session.request(method, *args, **kwargs)

    @handle_sap_request
    def get_from_link(self, link: str) -> Response:
        """Get data from SAP endpoint from given link."""
        return self._request("get", link)

    @handle_sap_request
    def get_entity(self, qmnum: int) -> Response:
        """Get a specific incident from SAP endpoint."""
        if self.expand and isinstance(self.expand, list):
            return self._request(
                "get",
                self.incident_path
                + "('{qmnum}')?${key}={value}".format(qmnum=str(qmnum), key="expand", value=",".join(self.expand)),
            )
        else:
            incident_path_url = f"{self.incident_path}('{qmnum}')"
            return self._request("get", incident_path_url)

    @handle_sap_request
    def get_entities(self, work_center: str, open_incident: str, completed: str) -> Response:
        """Get all entities from SAP endpoint."""
        params = {"$filter": f"WorkCenter eq '{work_center}' and Open eq {open_incident} and Completed eq {completed}"}
        if self.expand and isinstance(self.expand, list):
            params["$expand"] = ",".join(self.expand)
        return self._request("get", self.incident_path, params=params)

    @handle_sap_create_request
    def create_entity(self, entity: SapEntityIncident) -> Response:
        """Create a SAP entity."""
        data = {
            "Qmtxt": entity.get_qmtxt(),
            "Qmgrp": entity.main_service,
            "Qmcod": entity.main_sub_service,
            "Urgency": entity.urgency,
            "Priok": entity.priok,
            "FlagMainOrderCreate": entity.flag_main_order_create,
            "FlagMainOrderRelease": entity.flag_main_order_release,
            "ApiOrigin": entity.api_origin,
            "WorkCenter": entity.get_work_center(),
            "FlocId": entity.floc_id or entity.identification,
            "FlocDescription": str(entity.floc_description),
            "EquipmentId": entity.equipment_id,
            "toLongText": entity.get_long_text(),
            "toIncidentPartners": entity.get_default_incident_partners(),
        }
        return self._request("post", self.incident_path, json=data)

    @handle_sap_request
    def update_entity(self, entity: SapDataToUpdate) -> Response:
        """Update a sap main incident to add something as a log."""
        path = (
            f"{self.feedback_path}?Qmnum='{entity.qmnum}'&LongText='{entity.long_text}'&"
            f"ConfirmationText='{entity.confirmation_text}'&Reason='{entity.reason}'"
        )
        return self._request("post", path)

    @handle_sap_request
    def release_main_order_incident(self, qmnum: int) -> Response:
        """Release the main order of an incident."""
        data = {"FlagMainOrderRelease": True, "FlagMainOrderCreate": True}
        request_url = f"{self.incident_path}('{qmnum}')"
        return self._request("patch", request_url, json=data)


odata_incident = Incident()
