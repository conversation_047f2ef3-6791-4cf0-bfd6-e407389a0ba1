"""Test utils for mon_scom."""

import datetime

import pytest

from mon_scom.utils import (
    escape_backslash,
    get_acode,
    get_context_param_x,
    get_description_part,
    re_search_group,
    str_to_timestamp,
)


def test_get_acode():
    assert get_acode("A1234 Monitoring Object") == "A1234"
    assert get_acode("Monitoring Object A567") == "A567"
    assert get_acode("No ACode Here") == "N/A"
    assert get_acode("Multiple A123 A456 Codes") == "A123"
    assert get_acode("a123 Monitoring Object") == "A123"


def test_get_context_param_x():
    context = "<Context><Params><Param>Param1</Param><Param>Param2</Param></Params></Context>"
    assert get_context_param_x(context, 0) == "Param1"
    assert get_context_param_x(context, 1) == "Param2"
    assert get_context_param_x(context, 2) == ""
    context = "<Context><Params></Params></Context>"
    assert get_context_param_x(context, 0) == ""


def test_str_to_timestamp():
    date_str = "15/12/2023 14:15:09"
    expected_timestamp = int(datetime.datetime.timestamp(datetime.datetime.strptime(date_str, "%d/%m/%Y %H:%M:%S")))
    assert str_to_timestamp(date_str) == expected_timestamp
    assert str_to_timestamp("") == ""


def test_escape_backslash():
    assert escape_backslash("C:\\Windows\\System32") == "C:\\\\Windows\\\\System32"
    assert escape_backslash("No backslashes here") == "No backslashes here"


def test_re_search_group():
    pattern = r"\d{4}-\d{2}-\d{2}"
    text = "Date: 2023-12-15"
    assert re_search_group(pattern, text, "N/A") == "2023-12-15"
    text = "No date here"
    assert re_search_group(pattern, text, "N/A") == "N/A"


@pytest.mark.parametrize(
    ("description", "partname", "expected"),
    [
        ("Some text ::__PartName__=Value:: more text", "PartName", "Value"),
        ("Some text ::__PartName__=:: more text", "PartName", ""),
        ("Some text without the part", "PartName", ""),
        (
            (
                "Custom::__AlertGroup__=EventLog::__CI_ID__=AUDIOCODES_TEAMSCALL::__MonitoredElementName__=AUDIOCODES_"
                "TEAMSCALL_130::__Platform__=Windows::__Summary__= 1\n is not running. ID=130 ::"
            ),
            "Summary",
            " 1\n is not running. ID=130 ",
        ),
    ],
)
def test_get_description_part(description, partname, expected):
    """Test get_description_part function."""
    result = get_description_part(description, partname)
    assert result == expected
