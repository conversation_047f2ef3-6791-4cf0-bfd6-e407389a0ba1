"""<PERSON>ript to create an OpenShift Deployment from a template."""

import argparse
import json
from pathlib import Path
from typing import Optional


def main():
    """CLI entry point for creating an OpenShift Deployment from a template."""
    parser = argparse.ArgumentParser(description="Create an OpenShift Deployment from a template.")
    parser.add_argument("service_name", help="The name of the service.")
    parser.add_argument("--container_port", "-p", help="The container port to use.", type=int, default=None)
    parser.add_argument("--request_memory", "-r", help="The request memory to use.", type=str, default="512Mi")
    parser.add_argument("--limit_memory", "-l", help="The limit memory to use.", type=str, default="4Gi")
    parser.add_argument(
        "--extra_name",
        "-e",
        help=(
            "An extra part to the name (ie: #{zabbixCC}#), the #{olympusServiceNameKebab}# is replaced with this extra "
            "part included."
        ),
        type=str,
        default=None,
    )
    args = parser.parse_args()
    process_template(args.service_name, args.container_port, args.request_memory, args.limit_memory, args.extra_name)


def process_template(
    service_name: str,
    container_port: Optional[int] = None,
    request_memory: str = "64Mi",
    limit_memory: str = "512Mi",
    extra_name: Optional[str] = None,
) -> None:
    """Create an OpenShift Deployment from a template."""
    openshift_root = Path(__file__).parent.parent / ".openshift"
    template_path = openshift_root / "templates" / "deployment.template.json"
    template = json.loads(template_path.read_text())
    container_spec = template["spec"]["template"]["spec"]["containers"][0]
    if container_port is not None:
        container_spec["ports"] = [{"containerPort": container_port, "protocol": "TCP"}]
    container_spec["resources"] = {
        "requests": {"memory": request_memory},
        "limits": {"memory": limit_memory},
    }
    if extra_name is not None:
        str_to_replace = "#{olympusServiceNameKebab}#"
        full_name = f"{str_to_replace}-{extra_name}".lower()
        template["metadata"]["name"] = template["metadata"]["name"].replace(str_to_replace, full_name)
        template["spec"]["selector"]["matchLabels"]["app"] = template["spec"]["selector"]["matchLabels"]["app"].replace(
            str_to_replace, full_name
        )
        template["spec"]["template"]["metadata"]["labels"]["app"] = template["spec"]["template"]["metadata"]["labels"][
            "app"
        ].replace(str_to_replace, full_name)
        container_spec["envFrom"][0]["configMapRef"]["name"] = container_spec["envFrom"][0]["configMapRef"][
            "name"
        ].replace(str_to_replace, full_name)
        container_spec["envFrom"][1]["secretRef"]["name"] = container_spec["envFrom"][1]["secretRef"]["name"].replace(
            str_to_replace, full_name
        )
        container_spec["name"] = container_spec["name"].replace(str_to_replace, full_name)
        container_spec["volumeMounts"][0]["subPath"] = container_spec["volumeMounts"][0]["subPath"].replace(
            "#{olympusServiceName}#", "#{olympusServiceName}#_" + extra_name
        )

    destination_path = openshift_root / service_name / "03-deployment.json"
    destination_path.write_text(json.dumps(template, indent=2))


if __name__ == "__main__":
    main()
