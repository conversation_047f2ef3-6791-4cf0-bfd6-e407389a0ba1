{"inputs": [{"id": 1, "agent_id": 1, "agent": {"id": 1, "name": "ADVA", "ucmdb_name": "ADVA", "ci_enrichment": false, "metric_enrichment": false, "topology_enrichment": false, "action_class": null}, "ci_id": "ADVA_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 959, "idle_time_seconds": null, "is_active": true}, {"id": 2, "agent_id": 2, "agent": {"id": 2, "name": "BigData", "ucmdb_name": "prometheus", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "iictmismlv016", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 360, "idle_time_seconds": null, "is_active": true}, {"id": 3, "agent_id": 2, "agent": {"id": 2, "name": "BigData", "ucmdb_name": "prometheus", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "iictnismlv016", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 360, "idle_time_seconds": null, "is_active": true}, {"id": 4, "agent_id": 2, "agent": {"id": 2, "name": "BigData", "ucmdb_name": "prometheus", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "iictmismlv017", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 360, "idle_time_seconds": null, "is_active": true}, {"id": 5, "agent_id": 2, "agent": {"id": 2, "name": "BigData", "ucmdb_name": "prometheus", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "iictnismlv017", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 360, "idle_time_seconds": null, "is_active": true}, {"id": 6, "agent_id": 3, "agent": {"id": 3, "name": "CA_Spectrum", "ucmdb_name": "CA_Spectrum", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "Spectrum_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "idle_time", "period_seconds": null, "idle_time_seconds": 3600, "is_active": true}, {"id": 7, "agent_id": 4, "agent": {"id": 4, "name": "C-NMS", "ucmdb_name": "C-NMS", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "C-NMS_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 420, "idle_time_seconds": null, "is_active": true}, {"id": 8, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTBIAPWV018", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 9, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTCIAPWV507", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 10, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTCIAPWV553", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 11, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTLIAPWS002", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 12, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTMIAPWS013", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 13, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTMIAPWS014", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 14, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTMIAPWS015", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 15, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS026", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 16, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS027", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 17, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS028", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 18, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS029", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 19, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS031", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 20, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS032", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 21, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS033", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 22, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS034", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 23, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS035", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 24, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS036", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 25, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS037", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 26, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS038", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 27, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS039", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 28, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS040", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 29, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS041", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 30, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS042", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 31, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS043", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 32, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS046", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 33, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS047", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 34, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS050", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 35, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS052", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 36, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS053", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 37, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS054", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 38, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS055", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 39, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS056", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 40, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS057", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 41, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS058", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 42, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS059", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 43, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS060", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 44, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS061", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 45, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS062", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 46, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS063", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 47, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS064", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 48, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS065", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 49, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS066", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 50, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS067", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 51, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS076", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 52, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS077", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 53, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS080", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 54, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS084", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 55, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS085", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 56, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS086", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 57, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTOIAPWS087", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 58, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTSIAPWV007", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 59, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTZIAPWS040", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 60, "agent_id": 5, "agent": {"id": 5, "name": "DICA", "ucmdb_name": "DICA", "ci_enrichment": true, "metric_enrichment": false, "topology_enrichment": true, "action_class": null}, "ci_id": "IICTZIAPWS041", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 901, "idle_time_seconds": null, "is_active": true}, {"id": 61, "agent_id": 6, "agent": {"id": 6, "name": "EMMA", "ucmdb_name": "EMMA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "EMMA_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 301, "idle_time_seconds": null, "is_active": true}, {"id": 62, "agent_id": 8, "agent": {"id": 8, "name": "GSX_Monitor", "ucmdb_name": "GSX", "ci_enrichment": false, "metric_enrichment": false, "topology_enrichment": false, "action_class": null}, "ci_id": "GSX_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 900, "idle_time_seconds": null, "is_active": true}, {"id": 63, "agent_id": 10, "agent": {"id": 10, "name": "local6", "ucmdb_name": "Local6", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "a1617-mail_gw-prod", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 900, "idle_time_seconds": null, "is_active": true}, {"id": 64, "agent_id": 10, "agent": {"id": 10, "name": "local6", "ucmdb_name": "Local6", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "SYSLOG_GW_PRODMON", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 900, "idle_time_seconds": null, "is_active": true}, {"id": 65, "agent_id": 10, "agent": {"id": 10, "name": "local6", "ucmdb_name": "Local6", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "SYSLOG_GW_PRODMON_DRP", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 900, "idle_time_seconds": null, "is_active": true}, {"id": 66, "agent_id": 10, "agent": {"id": 10, "name": "local6", "ucmdb_name": "Local6", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "SNMP_GW_PRODMON", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 900, "idle_time_seconds": null, "is_active": true}, {"id": 67, "agent_id": 10, "agent": {"id": 10, "name": "local6", "ucmdb_name": "Local6", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "SNMP_GW_PRODMON_DRP", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 900, "idle_time_seconds": null, "is_active": true}, {"id": 68, "agent_id": 11, "agent": {"id": 11, "name": "LucentOMS", "ucmdb_name": "LucentOMS", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "LucentOMS_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 69, "agent_id": 12, "agent": {"id": 12, "name": "MAPIC", "ucmdb_name": "MAPIC", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "MAPIC_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 900, "idle_time_seconds": null, "is_active": true}, {"id": 70, "agent_id": 13, "agent": {"id": 13, "name": "MileStone", "ucmdb_name": "MileStone", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "MileStone_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "idle_time", "period_seconds": null, "idle_time_seconds": 903, "is_active": true}, {"id": 71, "agent_id": 14, "agent": {"id": 14, "name": "NetAct", "ucmdb_name": "Netact", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "NetAct_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 1020, "idle_time_seconds": null, "is_active": true}, {"id": 72, "agent_id": 15, "agent": {"id": 15, "name": "NFM-P", "ucmdb_name": "NFM-P", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "NFM-P_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 180, "idle_time_seconds": null, "is_active": true}, {"id": 73, "agent_id": 16, "agent": {"id": 16, "name": "OCC", "ucmdb_name": "OCC", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "OCC_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 600, "idle_time_seconds": null, "is_active": true}, {"id": 74, "agent_id": 17, "agent": {"id": 17, "name": "OpenShift", "ucmdb_name": "OpenShift", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "ocp.infrabel.be", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 300, "idle_time_seconds": null, "is_active": true}, {"id": 75, "agent_id": 17, "agent": {"id": 17, "name": "OpenShift", "ucmdb_name": "OpenShift", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "ocp-drp.infrabel.be", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 300, "idle_time_seconds": null, "is_active": true}, {"id": 76, "agent_id": 17, "agent": {"id": 17, "name": "OpenShift", "ucmdb_name": "OpenShift", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "ocp.hr-rail.be", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 300, "idle_time_seconds": null, "is_active": true}, {"id": 77, "agent_id": 18, "agent": {"id": 18, "name": "PEM", "ucmdb_name": "PEM", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "PEM_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 1020, "idle_time_seconds": null, "is_active": true}, {"id": 78, "agent_id": 19, "agent": {"id": 19, "name": "SCADA", "ucmdb_name": "SCADA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "SCADA_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 360, "idle_time_seconds": null, "is_active": true}, {"id": 79, "agent_id": 20, "agent": {"id": 20, "name": "SCOM", "ucmdb_name": "SCOM", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "SCOM_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 360, "idle_time_seconds": null, "is_active": true}, {"id": 80, "agent_id": 21, "agent": {"id": 21, "name": "SkyWalker", "ucmdb_name": "SkyWalker", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "SkyWalker_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "idle_time", "period_seconds": null, "idle_time_seconds": 903, "is_active": true}, {"id": 81, "agent_id": 22, "agent": {"id": 22, "name": "SolMan", "ucmdb_name": "SolMan", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "Solman_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 360, "idle_time_seconds": null, "is_active": true}, {"id": 82, "agent_id": 26, "agent": {"id": 26, "name": "vRealize", "ucmdb_name": "vRealize", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "vRealize_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 900, "idle_time_seconds": null, "is_active": true}, {"id": 83, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "PROXY-DMZINF4-DRP-NPRD", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 84, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "iictbimwlv008", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 85, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "PROXY-DMZINF4-MUI-NPRD", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 86, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "iictzimwlv008", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 87, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "PROXY-DMZIOT-MUI-NPRD", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 88, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "PROXY-INFRABEL-ENG", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 89, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "PROXY-ITECH-DRP-NPRD", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 90, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "iictbimwlv014", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 91, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "PROXY-ITECH-MUI-NPRD", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 92, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "iictyimwlv014", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 93, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "QMHA00P", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 94, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "QMIA00P", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 95, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "QMIA01P", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 96, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "QMIA02P", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 97, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "QMIA03P", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 98, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "QMIA04P", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 99, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "QMIA0EP", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 100, "agent_id": 27, "agent": {"id": 27, "name": "WebSphere-MQ", "ucmdb_name": "WebSphere-MQ", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "QMID00P", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 660, "idle_time_seconds": null, "is_active": true}, {"id": 101, "agent_id": 28, "agent": {"id": 28, "name": "Zabbix", "ucmdb_name": "Zabbix", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "Zabbix-Server_iictyizblv033", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 70, "idle_time_seconds": null, "is_active": true}, {"id": 102, "agent_id": 28, "agent": {"id": 28, "name": "Zabbix", "ucmdb_name": "Zabbix", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "Zabbix-Server_iictyizblv031", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 70, "idle_time_seconds": null, "is_active": true}, {"id": 103, "agent_id": 28, "agent": {"id": 28, "name": "Zabbix", "ucmdb_name": "Zabbix", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "Zabbix-Server_iictyizblv032", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 70, "idle_time_seconds": null, "is_active": true}, {"id": 104, "agent_id": 28, "agent": {"id": 28, "name": "Zabbix", "ucmdb_name": "Zabbix", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "Zabbix-Server_iictzizblv031", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 70, "idle_time_seconds": null, "is_active": true}, {"id": 105, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A1112_SWI", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 106, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A1250_SWI", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 107, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A171_SWI", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 108, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A117_MD_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 109, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A117_MD_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 110, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1183_SMT_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 111, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1183_SMT_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 112, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1183_SMT_PROD3", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 113, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1183_TMS_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 114, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1183_TMS_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 115, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1183_TMS_PROD3", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 116, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1183_TPS_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 117, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1183_TPS_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 118, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1183_TPS_PROD3", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 119, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1278_SIGW_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 120, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1278_SIGW_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 121, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1368_CI_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 122, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A144_KUB_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 123, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A144_KUB_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 124, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1465_CG_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 125, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1465_CG_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 126, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1719_TREX_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 127, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1719_TREX_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 128, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1719_TREX_PROD3", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 129, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1723_OBD_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 130, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1723_OBD_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 131, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1723_S4RT_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 132, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1723_S4RT_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 133, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1895_HA_PROD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 134, "agent_id": 29, "agent": {"id": 29, "name": "HA", "ucmdb_name": "HA", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "HA_A1895_HA_PROD2", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 720, "idle_time_seconds": null, "is_active": true}, {"id": 135, "agent_id": 30, "agent": {"id": 30, "name": "DCMS", "ucmdb_name": "DCMS", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A2103_iictxiapwv013", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "idle_time", "period_seconds": null, "idle_time_seconds": 1500, "is_active": true}, {"id": 136, "agent_id": 30, "agent": {"id": 30, "name": "DCMS", "ucmdb_name": "DCMS", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A2137_iictciapwv573", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "idle_time", "period_seconds": null, "idle_time_seconds": 1500, "is_active": true}, {"id": 137, "agent_id": 24, "agent": {"id": 24, "name": "StruxureWare", "ucmdb_name": "Struxurware", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "Struxurware_hb", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 300, "idle_time_seconds": null, "is_active": true}, {"id": 138, "agent_id": 23, "agent": {"id": 23, "name": "Stonebranch", "ucmdb_name": "Stonebranch", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "Stonebranch_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 310, "idle_time_seconds": null, "is_active": true}, {"id": 139, "agent_id": 23, "agent": {"id": 23, "name": "Stonebranch", "ucmdb_name": "Stonebranch", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "DAL2005D000_ACC", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 310, "idle_time_seconds": null, "is_active": true}, {"id": 140, "agent_id": 23, "agent": {"id": 23, "name": "Stonebranch", "ucmdb_name": "Stonebranch", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A2005_C0000_HeartbeatUAC_ACC-BUILD", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 310, "idle_time_seconds": null, "is_active": true}, {"id": 141, "agent_id": 23, "agent": {"id": 23, "name": "Stonebranch", "ucmdb_name": "Stonebranch", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A2005_C0000_HeartbeatUAC_ACC-RUN", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 310, "idle_time_seconds": null, "is_active": true}, {"id": 142, "agent_id": 23, "agent": {"id": 23, "name": "Stonebranch", "ucmdb_name": "Stonebranch", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "DPL2005D000_PRD", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 310, "idle_time_seconds": null, "is_active": true}, {"id": 143, "agent_id": 23, "agent": {"id": 23, "name": "Stonebranch", "ucmdb_name": "Stonebranch", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A2005_C0000_HeartbeatUAC_PRD-BUILD", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 310, "idle_time_seconds": null, "is_active": true}, {"id": 144, "agent_id": 23, "agent": {"id": 23, "name": "Stonebranch", "ucmdb_name": "Stonebranch", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A2005_C0000_HeartbeatUAC_PRD-RUN", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 310, "idle_time_seconds": null, "is_active": true}, {"id": 145, "agent_id": 23, "agent": {"id": 23, "name": "Stonebranch", "ucmdb_name": "Stonebranch", "ci_enrichment": true, "metric_enrichment": true, "topology_enrichment": true, "action_class": null}, "ci_id": "A2005_C0000_HeartbeatUAC_TST-RUN", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "period", "period_seconds": 310, "idle_time_seconds": null, "is_active": true}, {"id": 146, "agent_id": 33, "agent": {"id": 33, "name": "AIRCO", "ucmdb_name": "AIRCO", "ci_enrichment": false, "metric_enrichment": false, "topology_enrichment": false, "action_class": null}, "ci_id": "AIRCO_HB", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "heartbeat_type": "idle_time", "period_seconds": null, "idle_time_seconds": 300, "is_active": true}], "outputs": []}