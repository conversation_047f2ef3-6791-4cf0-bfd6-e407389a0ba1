import socket

from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>


def mock_things(mocker: <PERSON><PERSON><PERSON>ix<PERSON>, file_content_json: dict):
    """Mock the things specified in the test-file under the mock key.

    The key should be the name of the object you want to mock.
    The value should be the keyword arguments passed to mocker.patch.
    (e.g. {"db.Agent.get_agent_id_from_name": {"return_value": 1}})
    The key "side_effect" is special, it should be a string that will be evaluated to a side_effect.
    (e.g. {"socket.gethostbyaddr": {"side_effect": "_gethostbyaddr"}}),
    you will then need to define the function _gethostbyaddr in the file where this function is defined.

    The rule of thumb is: If you need a global mock, use return_value, if you need a specific mock per input, use
    side_effect and provide a function that behaves like the mocked function.
    """
    mock_data: dict | None = file_content_json.get("mock")
    if not mock_data:
        return
    for key, value in mock_data.items():
        if "side_effect" in value:
            value["side_effect"] = eval(value["side_effect"])  # noqa: S307
        mocker.patch(key, **value)


def gethostbyaddr_side_effect(host: str) -> list:
    """Get the host by address.

    This function is intended to be used as a side_effect for the socket.gethostbyaddr function.
    """
    host_map = {
        "************": ["sta_fgzh_fgsp_a.msnet.railb.be", [], "************"],
        "************": ["sta_fgzh_fgsp_c.msnet.railb.be", [], "************"],
    }
    response = host_map.get(host)
    if not response:
        raise socket.herror
    return response
