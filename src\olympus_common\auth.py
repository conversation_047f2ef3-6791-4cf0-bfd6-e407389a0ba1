"""File that holds the logic to authenticate requests.

The authentication is written for APIs using FastAPI.
The middleware is used to authenticate requests using JWTs. The JWTs are validated
using a JWK configuration.
"""

import logging
from functools import partial

import jwt
from fastapi import FastAPI, HTTPException, Request
from fastapi.openapi.utils import get_openapi
from fastapi.responses import JSONResponse
from fastapi.routing import APIRoute, APIRouter
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from starlette.middleware.base import BaseHTTPMiddleware, DispatchFunction, RequestResponseEndpoint
from starlette.routing import Route
from starlette.types import AS<PERSON>App

from olympus_common.config import JWKConfig

logger = logging.getLogger(__name__)

security = HTTPBearer()


def validate_credentials(credentials: HTTPAuthorizationCredentials, config: JWKConfig):
    """Validate the given credentials from the request."""
    jwk_client = jwt.PyJWKClient(config.uri, cache_keys=config.cache_keys)
    token = credentials.credentials
    try:
        signing_key = jwk_client.get_signing_key_from_jwt(token)
        payload = jwt.decode(token, signing_key.key, algorithms=config.algorithms, audience=config.valid_audiences)
        return payload
    except jwt.PyJWTError as exc:
        raise HTTPException(status_code=401, detail=f"Could not validate credentials: {exc}") from exc


class AuthMiddleware(BaseHTTPMiddleware):
    """Represent HTTP middleware to authenticate requests.

    Add this middleware to an app using `app.add_middleware(AuthMiddleware)`.

    The advantage of using this middleware is that it will implicitly
    validate the credentials for each endpoint.

    Without using this middleware, we'd have to call `validate_credentials`
    in every endpoint that requires authentication. This middleware abstracts
    that logic away from the endpoints, making them simpler and easier to read.
    """

    def __init__(self, app: ASGIApp, config: JWKConfig, dispatch: DispatchFunction | None = None) -> None:
        super().__init__(app, dispatch=dispatch)
        self.config = config
        self.public_routes = get_public_routes(app.app)  # type: ignore[attr-defined]

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint):
        """Dispatch the request, validating the credentials.

        Notes
        -----
        By raising an HTTPException if credentials were not extracted from the request,
        we disable auto_error handling in FastAPI. This is because we want to handle
        the error ourselves, returning a 401 response instead of a 403 response.
        """
        if request.url.path in self.public_routes:
            return await call_next(request)

        try:
            credentials = await security(request)
            if not credentials:
                # This should never happen since we initialize the HTTPBearer without arguments
                # causing auto_error to be True, so credentials are either returned or an error
                # is raised, but to satisfy mypy we do make the check here and raise if credentials
                # should ever be None.
                raise HTTPException(status_code=401, detail="Could not validate credentials")
            validate_credentials(credentials, self.config)
        except HTTPException as e:
            return JSONResponse(status_code=401, content={"detail": e.detail})
        return await call_next(request)


def get_public_routes(router: APIRouter | FastAPI):
    """Return public routes from the APIRouter.

    The default docs routes are considered public.
    If a route has a "public" tag, it is also considered public.

    Notes
    -----
    The docs routes are instances of Route, not APIRoute, so they do not have tags.
    """
    public_routes = []
    default_route_names = ["swagger_ui_html", "swagger_ui_redirect", "redoc_html", "openapi"]
    for route in router.routes:
        if isinstance(route, APIRoute) and "public" in route.tags:
            public_routes.append(route.path)
        elif isinstance(route, Route) and route.name in default_route_names:
            public_routes.append(route.path)
    return public_routes


def _custom_openapi(app: FastAPI):
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(title=app.title, version=app.version, routes=app.routes)

    openapi_schema["components"]["securitySchemes"] = {"JWT": {"type": "http", "scheme": "bearer"}}

    public_routes = get_public_routes(app)
    for route in app.routes:
        if not isinstance(route, Route) or route.path in public_routes or not route.methods:
            continue

        for method in route.methods:
            openapi_schema["paths"][route.path][method.lower()]["security"] = [{"JWT": []}]

    app.openapi_schema = openapi_schema
    return app.openapi_schema


def add_security(app: FastAPI, config: JWKConfig):
    """Add authentication to the FastAPI app.

    This function adds the AuthMiddleware to the app, which will validate
    the JWT for each request. It also adds a custom OpenAPI schema
    to include security schemes for JWTs, to effectively enables the green
    button in the swagger UI to authenticate requests.
    """
    app.add_middleware(AuthMiddleware, config=config)
    app.openapi = partial(_custom_openapi, app=app)  # type: ignore[method-assign]
