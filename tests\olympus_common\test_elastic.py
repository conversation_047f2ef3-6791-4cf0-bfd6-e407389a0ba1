from itertools import groupby
from typing import Any
from uuid import uuid4

from pytest_mock import Mo<PERSON><PERSON><PERSON><PERSON>

from olympus_common import elastic

ElasticHitType = dict[str, dict[str, list[dict[str, Any]]]]


def dummy_hit(source: Any) -> dict[str, str]:
    return {"sort": "dummy_sort", "_source": source}


def dummy_hits(number_of_hits: int) -> ElasticHitType:
    hitlist = [dummy_hit(str(uuid4())) for _ in range(number_of_hits)]
    return {"hits": {"hits": hitlist}}


def dummy_elastic_kwargs() -> dict:
    return dict(
        user="dummy_user",
        password="dummy_password",  # noqa: S106
        servers="dummy_servers",
        index="dummy_index",
        query="dummy_query",
        sort_query="dummy_sort_query",
        search_size="dummy_search_size",
        pit_lifetime="dummy_pit_lifetime",
    )


def expected_result(hits: list[ElasticHitType]) -> list[dict[str, str]]:
    """Return a unique list of hits.

    Notes
    -----
    The current implementation of read_elastic_data will extend the original hits with any additionally fetched hits.
    While this is not important for a user to know, it is important to know this when testing the function.
    To comply with this implementation-detail in tests, we make the end result unique. Since a dict is not hashable,
    the list(set(list(mylist))) trick cannot be used. Since dictionaries are orderable types, we can use groupby and
    keep only its keys
    """
    result = []
    for hit in hits:
        result.extend(hit["hits"]["hits"])

    return [k for k, _ in groupby(result)]


def test_read_elastic_index(mocker: MockerFixture):
    es_mocked = mocker.patch("elasticsearch.Elasticsearch")
    open_point_in_time_mocked = mocker.patch.object(es_mocked.return_value, "open_point_in_time")
    open_point_in_time_mocked.return_value = {"id": "dummy"}
    close_point_in_time_mocked = mocker.patch.object(es_mocked.return_value, "close_point_in_time")

    search_mocked = mocker.patch.object(es_mocked.return_value, "search")
    first_hit: ElasticHitType = dummy_hits(2)
    second_hit: ElasticHitType = dummy_hits(1)
    third_hit: ElasticHitType = dummy_hits(0)
    hits = [first_hit, second_hit, third_hit]
    search_mocked.side_effect = hits

    elastic_kwargs = dummy_elastic_kwargs()
    data = elastic.read_elastic_index(**elastic_kwargs)
    expected_data = expected_result(hits)

    assert open_point_in_time_mocked.call_count == 1
    assert search_mocked.call_count == 3
    assert close_point_in_time_mocked.call_count == 1
    assert data == expected_data
