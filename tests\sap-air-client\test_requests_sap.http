# Test our FastAPI endpoints

###
GET https://127.0.0.1:8000/sap/entity/51023290
Accept: application/json

###
GET https://127.0.0.1:8000/sap/entity/icinga/51023307
Accept: application/json

###
GET https://127.0.0.1:8000/sap/entities/T314A
Accept: application/json

###
GET https://127.0.0.1:8000/sap/entities/T314A?open=true&completed=false
Accept: application/json

### Get request for Olympus DEV SERVER
GET https://*************:8000/sap/entity/icinga/51021846
Accept: application/json

### Get request on openshift dev server
GET https://a2110-sap-air-client-acc.apps.nonprod-ocp.infrabel.be:80/sap/entity/51023325
Accept: application/json

### Get request on openshift dev server
GET https://a2110-sap-air-client-acc.apps.nonprod-ocp.infrabel.be/sap/entity/icinga/51023340
Accept: application/json



### Send POST request on Olympus DEV SERVER
POST https://127.0.0.1:8000/sap/entity/create
Content-Type: application/json

{
  "metric_name": "10009",
  "identification": "lpschar26",
  "source": "CA_Spectrum",
  "description": "Wed 29 Nov, 2023 - 12:00:36 The condition causing the loss of contact on the device model has cleared ( name - lpschar26.network.railb.be, type - Pingable ). (event [0x00010d30])",
  "raised_at": "",
  "last_occurred_at": "2023-11-29 12:02:20",
  "tally": "",
  "category": "Hardware",
  "ip_address": "",
  "AlertKey": "Pingable - lpschar26.network.railb.be",
  "Subzone": "",
  "metric_type": "/HardwareEvent/",
  "model": "/TRANSMISSION/IP DEVICE/",
  "CreatedById": "",
  "CreatedByName": "",
  "EquipmentDescription": "",
  "EquipmentId": "",
  "FlocDescription": "lpschar26",
  "FlocId": "IP008679",
  "LastModifiedById": "",
  "LastModifiedByName": "",
  "LongText": "Olympus Action",
  "Severity": "3",
  "location_address": "",
  "location_attribute": "",
  "location_category": "",
  "location_type": "",
  "a_codes": [
    "A2024"
  ],
  "subgroups": "tst",
  "main_service": "620IPN",
  "main_sub_service": "0200",
  "run_procedure": true
}


### Send POST request on Olympus DEV SERVER
# POST https://*************:8000/sap_air/entity/create
POST https://localhost:8000/sap/entity/create
Content-Type: application/json

{
  "metric_name": "ip",
  "identification": "nfs-xp01-ld",
  "source": "Zabbix",
  "description": "Used disk space on pool ip-sappo-pool in % is now 81 % !",
  "raised_at": "",
  "last_occurred_at": "2024-03-13 16:06:44",
  "tally": "",
  "TopLevel": "",
  "category": "Hardware",
  "ip_address": "************;************",
  "AlertKey": "IP",
  "Subzone": "",
  "metric_type": "/FileSystem/PoolDiskSpaceUsed/",
  "model": "Oracle SPARC T5-4",
  "CreatedById": "",
  "CreatedByName": "",
  "EquipmentDescription": "",
  "EquipmentId": "",
  "FlocDescription": "nfs-xp01-ld",
  "FlocId": "UCMDB_ACC_SERV006396",
  "LastModifiedById": "",
  "LastModifiedByName": "",
  "LongText": "Olympus Action",
  "Severity": "3",
  "location_address": "",
  "location_attribute": "",
  "location_category": "",
  "location_type": ""
}


### Send POST request on openshift dev server
POST https://a2110-sap-air-client-dev.apps.nonprod-ocp.infrabel.be/sap/entity/create
Content-Type: application/json

{
  "metric_name": "ip",
  "identification": "nfs-xp01-ld",
  "source": "Zabbix",
  "description": "Used disk space on pool ip-sappo-pool in % is now 81 % !",
  "raised_at": "",
  "last_occurred_at": "2024-03-13 16:06:44",
  "tally": "",
  "TopLevel": "",
  "category": "Hardware",
  "ip_address": "************;************",
  "AlertKey": "IP",
  "Subzone": "",
  "metric_type": "/FileSystem/PoolDiskSpaceUsed/",
  "model": "Oracle SPARC T5-4",
  "CreatedById": "",
  "CreatedByName": "",
  "EquipmentDescription": "",
  "EquipmentId": "",
  "FlocDescription": "nfs-xp01-ld",
  "FlocId": "UCMDB_ACC_SERV006396",
  "LastModifiedById": "",
  "LastModifiedByName": "",
  "LongText": "Olympus Action",
  "Severity": "3",
  "location_address": "",
  "location_attribute": "",
  "location_category": "",
  "location_type": ""
}


### Send POST request on Olympus DEV SERVER
POST https://*************:8000/sap/entity/create
Content-Type: application/json

{
  "metric_name": "ip",
  "identification": "nfs-xp01-ld",
  "source": "Zabbix",
  "description": "Used disk space on pool ip-sappo-pool in % is now 81 % !",
  "raised_at": "",
  "last_occurred_at": "2024-03-13 16:06:44",
  "tally": "",
  "TopLevel": "",
  "category": "Hardware",
  "ip_address": "************;************",
  "AlertKey": "IP",
  "Subzone": "",
  "metric_type": "/FileSystem/PoolDiskSpaceUsed/",
  "model": "Oracle SPARC T5-4",
  "CreatedById": "",
  "CreatedByName": "",
  "EquipmentDescription": "",
  "EquipmentId": "",
  "FlocDescription": "nfs-xp01-ld",
  "FlocId": "UCMDB_ACC_SERV006396",
  "LastModifiedById": "",
  "LastModifiedByName": "",
  "LongText": "Olympus Action",
  "Severity": "3",
  "location_address": "",
  "location_attribute": "",
  "location_category": "",
  "location_type": ""
}


### Send POST request with json body
POST https://localhost:8000/air/entity/create
Content-Type: application/json

{
  "identification": "iictyimylc021",
  "metric_type": "/System/ProcessAvailability/",
  "metric_name": "DOWN",
  "ACodes": [
    "A2113"
  ],
  "SAPIncidentID": "51234567"
}


### Send POST request on local host
POST https://localhost:8000/sap/entity/link
Content-Type: application/json

{
  "Qmnum": "51022454",
  "LongText": "metric_type 00010b10 on (CI) has been linked",
  "ConfirmationText": "Linked",
  "Reason": "W001",
  "FlocId": "IP009104",
  "metric_type": "/HardwareEvent/",
  "metric_name": "00010b10"
}


### Send POST request on local
POST https://localhost:8000/sap/entity/unlink
Content-Type: application/json

{
  "Qmnum": "51022454",
  "LongText": "metric_type 00010b10 on (CI) has been unlinked",
  "ConfirmationText": "Unlinked",
  "Reason": "W001",
  "FlocId": "IP009104",
  "metric_type": "/HardwareEvent/",
  "metric_name": "00010b10",
  "alarm_id": ""
}

### Send POST request on Olympus DEV SERVER
POST https://a2110-sap-air-client-dev.apps.nonprod-ocp.infrabel.be/sap/entity/link
Content-Type: application/json

{
  "Qmnum": "51022454",
  "LongText": "metric_type 00010b10 on (CI) has been linked",
  "ConfirmationText": "Linked",
  "Reason": "W001",
  "FlocId": "IP009104",
  "metric_type": "/HardwareEvent/",
  "metric_name": "00010b10"
}


### Send POST request on Olympus DEV SERVER
POST https://a2110-sap-air-client-dev.apps.nonprod-ocp.infrabel.be/sap/entity/unlink
Content-Type: application/json

{
  "Qmnum": "51133039",
  "LongText": "metric_type 00010b10 on (CI) has been unlinked",
  "ConfirmationText": "Unlinked",
  "Reason": "W001",
  "FlocId": "IP007093",
  "metric_type": "/HardwareEvent/",
  "metric_name": "fff003a6",
  "alarm_id": "42020922"
}


### Get request on openshift dev server
POST https://a2110-sap-air-client-acc.apps.nonprod-ocp.infrabel.be/alarm/clear?alarm_id=14988238
Accept: application/json

### Send POST request on Olympus DEV SERVER
POST http://localhost:8000/sap/release/link
Content-Type: application/json

{
  "release_number": "51200376",
  "alarm_id": "28755246"
}