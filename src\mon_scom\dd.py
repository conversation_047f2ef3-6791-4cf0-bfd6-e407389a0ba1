"""Details Design module for mon-scom."""

import datetime
import re

import pandas as pd
from sqlalchemy.orm import Session

from mon_scom import static, utils
from mon_scom.config import config
from olympus_common import db, enums
from olympus_common import pd as olympus_pd
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the SCOM Details Design."""
    if config.debug:
        pd.set_option("display.max_columns", None)

    agent_id = db.Agent.get_agent_id_from_name("SCOM", session)

    transformed = _transform(df, agent_id)

    # Drop rows with all NA's
    transformed = _drop_empty_rows(transformed)

    return transformed


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    # DisplayString is NaN sometimes so we first fillna
    df = df.fillna("N/A")

    # Select and rename columns
    df_records: pd.DataFrame = olympus_pd.clean_raw_dataframe(df, static.COLUMNS_RENAMING, static.COLUMNS_TO_KEEP)

    # Hardcoded values
    df_records["agent_id"] = agent_id
    df_records["manager"] = _manager()
    df_records["action_class"] = _scope()
    df_records["additional_data"] = _additional_data()
    df_records["handle_time"] = _handle_time()

    # Computed values
    df_records[
        [
            "wake_up_time",
            "event_id",
            "event_type",
            "actionable",
            "raise_time",
            "clear_time",
            "clear_type",
            "metric_type",
            "metric_class",
            "metric_name",
            "alert_key",
            "alert_group",
            "node",
            "node_alias",
            "ci_id",
            "summary",
            "severity",
        ]
    ] = df_records.apply(_parse_scom, axis=1)

    return df_records


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_data() -> str | None:
    """Return the extended attributes for the event."""
    return None


# This table maps the severity of non-error alerts in SCOM to a severity
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _alert_to_severity(alert: str) -> int:
    match alert:
        case "Information":
            return enums.Severity.INDETERMINATE.value
        case "Warning":
            return enums.Severity.WARNING.value
        case _:
            return enums.Severity.INDETERMINATE.value


# This maps the severity of those alerts on par with its monitor health to a severity
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _monitor_health_to_severity(monitor_health: str) -> int:
    match monitor_health:
        case "Success":
            return enums.Severity.CLEARED.value
        case "Uninitialized":
            return enums.Severity.INDETERMINATE.value
        case "Warning":
            return enums.Severity.WARNING.value
        case _:
            return enums.Severity.INDETERMINATE.value


# This maps the severity of error kind of alerts with specified priority in SCOM to a severity
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _error_to_severity(error: str) -> int:
    match error:
        case "High":
            return enums.Severity.CRITICAL.value
        case "Normal":
            return enums.Severity.CRITICAL.value
        case "Low":
            return enums.Severity.CRITICAL.value
        case _:
            return enums.Severity.INDETERMINATE.value


# ignored: summary (semi), severity (semi), actionable !!!
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _parse_scom(row: pd.Series) -> pd.Series:
    raise_time_utc_str = row["TimeRaised"]
    raise_time_utc = (
        datetime.datetime.strptime(raise_time_utc_str, "%d/%m/%Y %H:%M:%S").replace(tzinfo=None)
        if raise_time_utc_str != ""
        else None
    )

    clear_time_utc_str = row["TimeResolved"]
    clear_time_utc = (
        datetime.datetime.strptime(clear_time_utc_str, "%d/%m/%Y %H:%M:%S").replace(tzinfo=None)
        if clear_time_utc_str != ""
        else None
    )
    raise_time = raise_time_utc
    wake_up_time = raise_time
    clear_time = clear_time_utc
    resolution_state = str(row["ResolutionState"])
    name = row["Name"]
    severity_str = row["Severity"]
    severity = enums.Severity.INDETERMINATE.value
    priority = row["Priority"]
    id = row["Id"]
    netbios_computername = row["NetbiosComputerName"]
    description = row["Description"]
    display_string = row["DisplayString"]
    monitoring_object_display_name: str = row["MonitoringObjectDisplayName"]
    monitoring_object_health_state = row["MonitoringObjectHealthState"]
    context_name = utils.get_context_name(row["Context"])
    managementpack_category_type = row["Category"]
    context_binary_packname = utils.get_context_part(row["Context"], "BinaryPathName")
    context_logging_computer = utils.get_context_part(row["Context"], "LoggingComputer")
    context_event_description = utils.get_context_part(row["Context"], "EventDescription")
    context_data = utils.get_context_part(row["Context"], "Data")
    context_param5 = utils.get_context_param_x(row["Context"], 5)
    iis_pool_site = False
    summary = ""
    cust_summary = ""
    men = ""
    node = ""
    node_alias = ""
    element_name = ""
    platform = ""
    cust_platform = ""
    metric_type = ""
    metric_name = ""
    cust_metric_name = ""
    ci_id = ""
    cust_ci_id = ""
    alert_key = ""
    alert_key_upper = ""
    alert_group = ""
    cust_alert_group = ""
    alert_group_upper = ""
    process = ""
    service = ""
    service_name = ""
    event_type = enums.AlarmType.PROBLEM.value
    clear_type = enums.ClearType.AUTOMATICALLY.value
    actionable: bool = True

    metric_class = "200200"

    # Map SCOM alert severity to severity
    if severity_str == "MatchMonitorHealth":
        # Severity is based on monitor health
        if monitoring_object_health_state == "Error":
            # Severity is based on alert priority
            severity = _error_to_severity(priority)
        else:
            severity = _monitor_health_to_severity(monitoring_object_health_state)

    elif severity_str == "Error":
        # Severity is based on alert priority
        severity = _error_to_severity(priority)
    else:
        severity = _alert_to_severity(severity_str)

    # Determine the type of this event
    if resolution_state == "255":
        event_type = enums.AlarmType.RESOLUTION.value
    elif resolution_state == "0":
        event_type = enums.AlarmType.PROBLEM.value
    else:
        event_type = enums.AlarmType.PROBLEM.value

    # Construct summary of event
    if description == "null":
        summary = f"{name} - {context_event_description}"
    elif description == "":
        summary = f"{name} - {context_event_description}"
    else:
        summary = f"{name} - {description}"

    # Problem and Resolution
    if resolution_state == "0":
        # Problem
        event_type = enums.AlarmType.PROBLEM.value
        raise_time = raise_time_utc

    elif resolution_state == "255":
        # Resolution
        event_type = enums.AlarmType.RESOLUTION.value
        severity = enums.Severity.INDETERMINATE.value
        raise_time = raise_time_utc
        clear_time = clear_time_utc

    else:
        # shouldn't exist
        event_type = enums.AlarmType.PROBLEM.value

    netbios_computername = netbios_computername.upper()
    if match := re.search(r"(.*)\.MS", netbios_computername):
        node = match.group()
        node_alias = node
        ci_id = node
    else:
        node = netbios_computername
        node_alias = node
        ci_id = node

    event_id = id

    if re.search(r"^(.* )?(IIS [0-9]+|Internet Information Services 2003) .+", display_string):
        alert_key = re.sub(r"^(.* )?(IIS [0-9]+|Internet Information Services 2003) (.+)", r"\3", display_string)

        if context_name == "":
            element_name = monitoring_object_display_name
        else:
            element_name = context_name

        platform = f"IIS (Windows) - {element_name}"

    elif re.search(r"Clustered role .* is moving to cluster node", description):
        alert_key = "Cluster Switch"
    elif re.search(r"^\[Skype\]", display_string):
        alert_key = "Skype"
    else:
        alert_key = display_string

    # remove trailing spaces, tabs or dots
    # to interpret \t as a tab we have to enclose the string in single quotes and not double quotes
    # \s isn't supported to reference whitespaces

    alert_key = re.sub(r"[ \t\.]+$", "", alert_key)  # remove trailing spaces, tabs or dots
    alert_key_upper = alert_key.upper()

    alert_group = managementpack_category_type

    if alert_key_upper == "":
        metric_name = "NA"
        metric_type = "NA"
    else:
        key = alert_key_upper if alert_key_upper in static.metric_lookup_dict else "Default"
        metric_lookup = static.metric_lookup_dict[key]

        men = metric_lookup["metric_name"]
        metric_type = metric_lookup["metric_type"]

    if metric_name == "":
        metric_name = men

    # Use the values provided by
    # Custom::__AlertGroup__=<>::__CI_ID__=<>::__MonitoredElementName__=<>
    # ::__Platform__=<>::__Summary__=<> #######

    cust_alert_group = utils.get_description_part(description, "AlertGroup")

    if cust_alert_group != "":
        alert_group = cust_alert_group
        alert_group_upper = alert_group.upper()

        key = alert_group_upper if alert_group_upper in static.custom_metric_lookup_dict else "Default"
        custom_metric_lookup = static.custom_metric_lookup_dict[key]

        metric_type = custom_metric_lookup["metric_type"]
        metric_class = custom_metric_lookup["class"]

    cust_metric_name = utils.get_description_part(description, "MonitoredElementName")

    if cust_metric_name != "":
        metric_name = cust_metric_name

    cust_ci_id = utils.get_description_part(description, "CI_ID")

    if cust_ci_id != "":
        ci_id = cust_ci_id
        alert_key = ci_id + metric_name

    cust_platform = utils.get_description_part(description, "Platform")

    if cust_platform != "":
        platform = cust_platform

    cust_summary = utils.get_description_part(description, "Summary")

    if cust_summary != "":
        summary = cust_summary

    # AlertKey dependent settings

    # Logical Disk alarms
    if re.search(r"^LOGICAL DISK FREE SPACE IS LOW", alert_key_upper) or re.search(
        r"^FREE SPACE \(%\) FOR CLUSTER DISK ALERT", alert_key_upper
    ):
        platform = "Windows"
        alert_group = f"{alert_group}-{platform}"
        # Add $monitoringObjectDisplayName to alert_key to add the disk
        alert_key = f"{alert_key}-{monitoring_object_display_name}"

    # Custom Application Process failed alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION PROCESS FAILED"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "ProcessName")

        alert_group = f"{alert_group}-{platform}"

    # Custom Application Service failed alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION SERVICE FAILED"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "ServiceName")

        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # Custom Application Share failed alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION SHARE FAILED"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "ShareName")

        alert_group = f"{alert_group}-{platform}"

    # Custom Application EventLog failed alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION EVENTLOG FAILED"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "EventLogName")
        alert_group = f"{alert_group}-{platform}"

    # Custom Application FileContent alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION FILECONTENT"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "FileContentPathName")
        alert_group = f"{alert_group}-{platform}"

    # Custom Application FileWatcher alarms
    elif alert_key_upper.startswith("CUSTOM APPLICATION FILEWATCHER"):
        # already done before ci_id = extract($description, "::__CI_ID__=(.*?)::")
        if platform == "":
            platform = utils.get_description_part(description, "FileWatcherPathName")
        alert_group = f"{alert_group}-{platform}"

    # IIS Application Pool is unavailable
    # IIS Web Site is unavailable
    # IIS FTP Site is unavailable
    elif alert_key_upper.startswith("APPLICATION POOL IS UNAVAILABLE"):
        ci_id = f"IIS_Webapp_{element_name}_{node}"
        metric_class = "200203"
        iis_pool_site = True

    elif alert_key_upper.startswith("WEB SITE IS UNAVAILABLE"):
        ci_id = f"IIS_Website_{element_name}_{node}"
        iis_pool_site = True

    elif alert_key_upper.startswith("FTP SITE IS UNAVAILABLE"):
        ci_id = f"IIS_FTPSite_{element_name}_{node}"
        iis_pool_site = True

    if iis_pool_site:
        if platform == "":
            platform = monitoring_object_display_name
        alert_group = f"{alert_group}-{platform}"

    # IIS 'Web Server is unavailable' alarms
    elif alert_key_upper.startswith("WEB SERVER IS UNAVAILABLE"):
        ci_id = "IIS_" + node

        if platform == "":
            platform = utils.re_search_group(r".* -k (.*)$", context_binary_packname, "")

        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # IIS 'The FTP service is unavailable' alarms
    elif alert_key_upper.startswith("THE FTP SERVICE IS UNAVAILABLE") or alert_key_upper.startswith(
        "FTP SERVER IS UNAVAILABLE"
    ):
        ci_id = "IIS_FTP_" + node
        platform = f"IIS (Windows) - {metric_name}"
        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # IIS 'server role is unavailable' alarms
    elif alert_key_upper.startswith("SERVER ROLE IS UNAVAILABLE"):
        ci_id = "IIS_" + node
        if platform == "":
            platform = context_name

        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # Cluster Switch alarms
    elif alert_key_upper.startswith("CLUSTER SWITCH"):
        alert_group = "Cluster Switch"
        severity = enums.Severity.WARNING.value  # Warning regardless the $severity value in the SCOM event
        if ci_id in static.NA_VALUES:
            ci_id = "A1580"

    # Health Service Heartbeat Failure
    elif alert_key_upper.startswith("HEALTH SERVICE HEARTBEAT FAILURE"):
        platform = "Windows"
        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # Print Spooler:  Service Status Monitor
    elif alert_key_upper.startswith("PRINT SPOOLER:  SERVICE STATUS MONITOR"):
        ci_id = "PRINTERSPOOLER_" + node
        metric_name = context_name
        platform = f"PRINTERSPOOLER (Windows) - {metric_name}"
        alert_group = f"{alert_group}-{platform}"
        metric_class = "200202"

    # Exchange Health Set
    elif alert_key_upper.startswith("EXCHANGE HEALTH SET"):
        ci_id = "EXCHANGE_" + node
        metric_name = "ExchangeHealthSet"
        platform = "Exchange (Windows) - ExchangeHealthSet"
        alert_key = f"{alert_key}-{event_id}"

    # Skype
    elif alert_key_upper.startswith("SKYPE"):
        ci_id = "SKYPE_" + node
        metric_name = "skype"
        platform = "Exchange (Windows) - skype"
        alert_key = f"{alert_key}-{event_id}"

    # Active Directory
    elif alert_key_upper.startswith("AD"):
        ci_id = utils.re_search_group(r"^(.*?) ", alert_key_upper, "").strip()
        platform = "Windows"
        alert_key = f"{alert_key}-{event_id}"

    # MSSQL
    elif alert_key_upper.startswith("MSSQL"):
        if alert_key_upper.startswith("MSSQL ON WINDOWS: DATABASE LOG FILE IS FULL"):
            # e.g.
            # description = "Event ID: 9002. The transaction log for database \'MS487_Zonal\'
            # is full due to \'LOG_BACKUP\'."
            # displayString = "MSSQL on Windows: Database log file is full.
            # Back up the transaction log for the database to free up some log space"
            # monitoringObjectDisplayName = "IICTZISQWS001\\ISICTR02A"
            # monitoringObjectFullName = "Microsoft.SQLServer.Windows.DBEngine:IICTZISQWS001.msnet.railb.be.ISICTR02A"
            # CI_ID = ISICTR02A\MS487_Zonal
            # Node = IICTZISQWS001
            computer = utils.re_search_group(r"(^[^\\]*)", monitoring_object_display_name, "")
            instance = utils.re_search_group(r"([^\\]*)$", monitoring_object_display_name, "")
            database = utils.re_search_group(r"database[ \\']*([^ \\']*)", monitoring_object_display_name, "")
            ci_id = f"{instance}\\{database}"
            node = computer
            node_alias = node
            summary = description
            # Add ci_id to alert_key since node differs from ci_id
            alert_key = f"{alert_key}-{ci_id}"

        elif alert_key_upper.startswith("MSSQL ON WINDOWS: DATABASE"):
            # e.g.
            # description = Database "Test_IICT311" on SQL Server instance "ISICTR03A",
            # computer "IICTCISQWC202.msnet.railb.be
            # displayString = "MSSQL on Windows: Database is in offline/recovery pending/suspect/emergency state"
            # monitoringObjectFullName
            # = Microsoft.SQLServer.Windows.Database:IICTCISQWC202.msnet.railb.be.ISICTR03A;Test_IICT311
            # CI_ID = ISICTR03A\Test_IICT311
            # Node = IICTCISQWC202
            computer = utils.re_search_group(r":([^\.]*)\.", monitoring_object_display_name, "")
            instance = utils.re_search_group(r"([^\.]*);", monitoring_object_display_name, "")
            database = utils.re_search_group(r";(.*)", monitoring_object_display_name, "")
            ci_id = f"{instance}\\{database}"
            node = computer
            node_alias = node
            summary = description
            # Add ci_id to alert_key since node differs from ci_id
            alert_key = f"{alert_key}-{ci_id}"

        else:
            # e.g. monitoringObjectDisplayName : IICTZISQWC002\ISA11520A
            # CI_ID = ISA11520A
            # Node = IICTZISQWC002
            ci_id = utils.re_search_group(r"([^\\]*)$", monitoring_object_display_name, "")
            node = utils.re_search_group(r"(^[^\\]*)", monitoring_object_display_name, "")
            node_alias = node
            # Add ci_id to alert_key since node differs from ci_id
            alert_key = f"{alert_key}-{ci_id}"

    # MonitoredElementName
    if metric_name == "<VAR>":
        if re.search(r"^FREE SPACE \(%\) FOR CLUSTER DISK ALERT", alert_key_upper):
            # Example : MonitoringObjectDisplayName=Cluster Disk 23_\\?\Volume{809630aa-d440-49cd-ba23-e81411f28df6}
            #           @MonitoredElementName=Cluster Disk 23
            if match := re.search(r"^(.*)_.*$", monitoring_object_display_name):
                metric_name = match.group(1)
        elif re.search("DISK", alert_key_upper):
            # Example : DisplayString=Logical Disk Free Space is low
            #           MonitoringObjectDisplayName=O:\MountPoint2
            metric_name = monitoring_object_display_name
        else:
            metric_name = platform

    # SET NODE VALUE
    if node in static.NA_VALUES:
        context_logging_computer = context_logging_computer.upper()
        if match := re.search(r"(.*)\.MS", context_logging_computer):
            node = match.group(1)
            node_alias = node

            if ci_id in static.NA_VALUES:
                ci_id = node
        else:
            node = context_logging_computer
            node_alias = node
            if ci_id in static.NA_VALUES:
                ci_id = node

    if node in static.NA_VALUES:
        monitoring_object_display_name = monitoring_object_display_name.upper()
        if match := re.search(r"(.*)\.MS", monitoring_object_display_name):
            node = match.group(1)
            node_alias = node
            if ci_id in static.NA_VALUES:
                ci_id = node
        else:
            node = monitoring_object_display_name
            node_alias = node
            if ci_id in static.NA_VALUES:
                ci_id = node

    if alert_key_upper.startswith("A868 HEARTBEAT CONNECTION CHECK INFRABEL TO TIVOLI"):
        event_type = enums.AlarmType.HEARTBEAT.value
        severity = enums.Severity.INDETERMINATE.value
        clear_type = enums.ClearType.MANUALLY.value
        actionable = False
        if node in ["IICTCIAPWV436", "IICTCIAPWV437"]:
            # Only the production nodes should be monitored for heartbeat.
            ci_id = "SCOM_HB"
        else:
            ci_id = node
        alert_key = "A868 HeartBeat Alarm connection check Infrabel to Tivoli"
        alert_group = node
        metric_name = "Heartbeat"
        metric_type = "/ApplicationEvent/"
        # For heartbeats, the TimeRaised field is always populated with the same value,
        # so we have to use the LastModified field instead.
        raise_time_utc_str = row["LastModified"]
        raise_time = (
            datetime.datetime.strptime(raise_time_utc_str, "%d/%m/%Y %H:%M:%S").replace(tzinfo=None)
            if raise_time_utc_str != ""
            else None
        )

    if alert_group_upper == "HEARTBEAT":
        event_type = enums.AlarmType.HEARTBEAT.value
        severity = enums.Severity.INDETERMINATE.value
        clear_type = enums.ClearType.MANUALLY.value
        actionable = False
        if node in ["IICTCIAPWV436", "IICTCIAPWV437"]:
            # Only the production nodes should be monitored for heartbeat.
            ci_id = "SCOM_HB"
        else:
            ci_id = node
        alert_key = alert_key + "HeartBeat Alarm"
        alert_group = node
        metric_name = "Heartbeat"
        metric_type = "/ApplicationEvent/"
        # For heartbeats, the TimeRaised field is always populated with the same value,
        # so we have to use the LastModified field instead.
        raise_time_utc_str = row["LastModified"]
        raise_time = (
            datetime.datetime.strptime(raise_time_utc_str, "%d/%m/%Y %H:%M:%S").replace(tzinfo=None)
            if raise_time_utc_str != ""
            else None
        )

    # SET Resolution Type

    if managementpack_category_type == "EventCollection":
        clear_type = enums.ClearType.MANUALLY.value
    elif not (
        alert_key_upper.startswith("A868 HEARTBEAT ALARM CONNECTION CHECK INFRABEL TO TIVOLI")
        or alert_group_upper.startswith("HEARTBEAT")
    ):
        clear_type = enums.ClearType.AUTOMATICALLY.value

    if managementpack_category_type == "SecurityHealth":
        node = utils.re_search_group(r"(.*)\.", monitoring_object_display_name, "")
        node_alias = node
        if ci_id in static.NA_VALUES:
            ci_id = node

    # SET Summary
    if description == "NULL" or description == "":
        summary = re.sub(r"^Event Description: ", "", context_event_description)
    else:
        description = re.sub(r"^Event Description: ", "", context_event_description)
        if summary == "":
            summary = re.sub(r"^(.* named )[^ ]+( .*)$", r"\1" + monitoring_object_display_name + r"\2", description)

    summary = f"{summary} / {context_data}"

    # SET Platform Value For SERVICE && PROCESS Alarms

    process = display_string.upper()

    if re.search(r"PROCESS", process):
        if re.search(r"\.vbs", context_param5):
            if platform == "":
                platform_val = utils.re_search_group(r".*nologo(.*)\.vbs", context_param5, "")
                platform_val = utils.re_search_group(r"^..(.*)", platform, "")
                extn = ".vbs"
                platform_val = platform_val + extn
                platform = platform_val.lstrip()

            alert_group = f"{alert_group}-{platform}"
            metric_class = "200201"

        elif re.search(r"\.js", context_param5):
            if platform == "":
                platform_val = utils.re_search_group(r".*nologo(.*)\.js", context_param5, "")
                platform_val = utils.re_search_group(r"^..(.*)", platform, "")
                extn = ".js"
                platform_val = platform_val + extn
                platform = platform_val.lstrip()

            alert_group = f"{alert_group} - {platform}"
            metric_class = "200201"

        elif re.search(r"\.java", context_param5):
            if platform == "":
                platform_val = utils.re_search_group(r".*nologo(.*)\.java", context_param5, "")
                platform_val = utils.re_search_group(r"^..(.*)", platform, "")
                extn = ".java"
                platform_val = platform_val + extn
                platform = platform_val.lstrip()

            alert_group = f"{alert_group}-{platform}"
            metric_class = "200201"

        elif re.search(r"\.bat", context_param5):
            if platform == "":
                platform_val = utils.re_search_group(r".*nologo(.*)\.bat", context_param5, "")
                platform_val = utils.re_search_group(r"^..(.*)", platform, "")
                extn = ".bat"
                platform_val = platform_val + extn
                platform = platform_val.lstrip()

            alert_group = f"{alert_group}-{platform}"
            metric_class = "200201"

        elif re.search(r"\.jar", context_param5):
            if platform == "":
                platform_val = utils.re_search_group(r".*nologo(.*)\.jar", context_param5, "")
                platform_val = utils.re_search_group(r"^..(.*)", platform, "")
                extn = ".jar"
                platform_val = platform_val + extn
                platform = platform_val.lstrip()

            alert_group = f"{alert_group} - {platform}"
            metric_class = "200201"

        if metric_name == "<VAR>":
            metric_name = platform

    service = display_string.upper()
    service_name = context_name

    if re.search(r".*SERVICE STOPPED$", service):
        metric_class = "200202"
        if metric_name == "lanmanserver":
            platform = "Windows"

        if platform == "":
            if service_name != "":
                platform = service_name

            elif metric_name != "":
                platform = metric_name

        if platform == "":
            platform = "Service Name is Missing from Event"
        else:
            alert_group = f"{alert_group}-{platform}"

        if metric_name == "<VAR>":
            if re.search("DISK", alert_key_upper):
                metric_name = monitoring_object_display_name
            else:
                metric_name = platform

    if metric_name == "NA":
        if re.search(r".*SERVICE STOPPED$", service):
            if metric_name == "lanmanserver":
                platform = "Windows"

            if metric_type == "":
                metric_type = "/System/ServiceAvailability/"

            if metric_name == "":
                metric_name = utils.re_search_group(r"^([A-Za-z0-9]+)$", service_name, "")

            if platform == "":
                platform = metric_name

    if platform == "":
        platform = "Windows"

    if metric_name == "":
        metric_name = "NA"

    if ci_id in static.NA_VALUES:
        ci_id = "NA"

    # Still if Node has FQDN Name

    if node != "" and re.search(r"\.MSNET\.", node):
        node = utils.re_search_group(r"(.*)\.MS", node, "")
        node_alias = node
        if ci_id in static.NA_VALUES:
            ci_id = node

    # details($*)
    if severity < 2:
        actionable = False

    wake_up_time = raise_time

    return pd.Series(
        (
            wake_up_time,
            event_id,
            event_type,
            actionable,
            raise_time,
            clear_time,
            clear_type,
            metric_type,
            metric_class,
            metric_name,
            alert_key,
            alert_group,
            node,
            node_alias,
            ci_id,
            summary,
            severity,
        )
    )


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _drop_empty_rows(df: pd.DataFrame) -> pd.DataFrame:
    """Drop the empty rows of the dataframe."""
    return df[(df["metric_name"] != static.NA_STR) | (df["metric_type"] != static.NA_STR)]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the manager for the DD."""
    return "mon-scom"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time() -> datetime.datetime:
    """Determine the handle time, i.e. the time when the alarm is handled by Olympus."""
    return olympus_utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the scope related to the EMS."""
    return enums.Scope.IT.value
