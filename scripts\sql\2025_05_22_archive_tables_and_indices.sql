-- Archive tables
CREATE TABLE IF NOT EXISTS s2110_alarm_arc
(
    id bigint NOT NULL,
    last_occurrence_id bigint,
    last_problem_id bigint,
    last_clear_id bigint,
    agent_id bigint,
    manager text COLLATE pg_catalog."default",
    node text COLLATE pg_catalog."default",
    tally bigint,
    event_url text COLLATE pg_catalog."default",
    metric_type text COLLATE pg_catalog."default",
    metric_name text COLLATE pg_catalog."default",
    wake_up_time timestamp without time zone,
    is_active boolean,
    first_raise_time timestamp without time zone,
    platform text COLLATE pg_catalog."default",
    node_alias text COLLATE pg_catalog."default",
    action_class text COLLATE pg_catalog."default",
    event_id text COLLATE pg_catalog."default",
    actionable boolean,
    clear_type text COLLATE pg_catalog."default",
    ci_id text COLLATE pg_catalog."default",
    top_level text COLLATE pg_catalog."default",
    closing_time timestamp without time zone,
    identifier text COLLAT<PERSON> pg_catalog."default",
    identifier_hash text COLLATE pg_catalog."default",
    CONSTRAINT s2110_alarm_arc_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS s2110_occurrence_arc
(
    id bigint NOT NULL,
    alarm_id bigint,
    summary text COLLATE pg_catalog."default",
    severity smallint,
    event_type text COLLATE pg_catalog."default",
    raise_time timestamp without time zone,
    clear_time timestamp without time zone,
    additional_data jsonb,
    handle_time timestamp without time zone,
    ci_id text COLLATE pg_catalog."default",
    agent_id bigint,
    metric_type text COLLATE pg_catalog."default",
    metric_name text COLLATE pg_catalog."default",
    identifier text COLLATE pg_catalog."default",
    CONSTRAINT s2110_occurrence_arc_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS s2110_alarm_enrichment_arc
(
    id bigint NOT NULL,
    alarm_id bigint NOT NULL,
    enrichment_id bigint NOT NULL,
    last_enrichment boolean,
    CONSTRAINT s2110_alarm_enrichment_arc_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS s2110_enrichment_arc
(
    id bigint NOT NULL,
    location_category text COLLATE pg_catalog."default",
    brand text COLLATE pg_catalog."default",
    ci_uuid text COLLATE pg_catalog."default",
    ci_type text COLLATE pg_catalog."default",
    critical_ci text COLLATE pg_catalog."default",
    identification text COLLATE pg_catalog."default",
    ip_address text COLLATE pg_catalog."default",
    location_address text COLLATE pg_catalog."default",
    location_attribute text COLLATE pg_catalog."default",
    floc_id text COLLATE pg_catalog."default",
    ci_enrichment boolean,
    top_level_impact boolean,
    instructions text COLLATE pg_catalog."default",
    metric_enrichment boolean,
    top_level text COLLATE pg_catalog."default",
    enrichment_time timestamp without time zone,
    severity smallint,
    linked_environments text COLLATE pg_catalog."default",
    floc_class text COLLATE pg_catalog."default",
    subgroups text COLLATE pg_catalog."default",
    model text COLLATE pg_catalog."default",
    actionable boolean,
    source text COLLATE pg_catalog."default",
    metric_category text COLLATE pg_catalog."default",
    action_class text COLLATE pg_catalog."default",
    actions_enabled boolean,
    metric_description text COLLATE pg_catalog."default",
    metric_short_name text COLLATE pg_catalog."default",
    location_type text COLLATE pg_catalog."default",
    dashboards text COLLATE pg_catalog."default",
    metric_service text COLLATE pg_catalog."default",
    metric_sub_service text COLLATE pg_catalog."default",
    main_service text COLLATE pg_catalog."default",
    main_sub_service text COLLATE pg_catalog."default",
    CONSTRAINT s2110_enrichment_arc_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS s2110_alarm_incident_arc
(
    id bigint NOT NULL,
    alarm_id bigint,
    incident_id bigint,
    link_time timestamp without time zone,
    is_origin_alarm boolean,
    is_link_active boolean,
    link_user_id text COLLATE pg_catalog."default",
    unlink_user_id text COLLATE pg_catalog."default",
    unlink_time timestamp without time zone,
    CONSTRAINT s2110_alarm_incident_arc_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS s2110_alarm_job_arc
(
    id bigint NOT NULL,
    alarm_id bigint NOT NULL,
    job_name text COLLATE pg_catalog."default" NOT NULL,
    job_status smallint,
    job_error text COLLATE pg_catalog."default",
    job_retries bigint,
    job_time timestamp without time zone,
    nb_success bigint,
    CONSTRAINT s2110_alarm_job_arc_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS s2110_alarm_action_arc
(
    id bigint NOT NULL,
    alarm_id bigint,
    action_id bigint,
    is_first_ack boolean,
    CONSTRAINT s2110_alarm_action_arc_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS s2110_alarm_release_arc
(
    id bigint NOT NULL,
    alarm_id bigint,
    release_id bigint,
    link_time timestamp without time zone,
    is_link_active boolean DEFAULT true,
    link_user_id text COLLATE pg_catalog."default",
    unlink_user_id text COLLATE pg_catalog."default",
    unlink_time timestamp without time zone,
    CONSTRAINT s2110_alarm_release_arc_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

CREATE TABLE IF NOT EXISTS s2110_user_action_arc
(
    id bigint NOT NULL,
    action_type text COLLATE pg_catalog."default",
    creation_time timestamp without time zone,
    comment text COLLATE pg_catalog."default",
    user_id text COLLATE pg_catalog."default",
    CONSTRAINT s2110_user_action_arc_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

-- Indices on live tables

CREATE INDEX s2110_occurrence_ni
    ON s2110_occurrence USING btree
    (agent_id ASC NULLS LAST, metric_name ASC NULLS LAST, metric_type ASC NULLS LAST, ci_id ASC NULLS LAST, event_type)
    WITH (deduplicate_items=True)
    TABLESPACE s2110_index;
    
CREATE INDEX s2110_alarm_ni
    ON s2110_alarm USING btree
    (agent_id ASC NULLS LAST, ci_id ASC NULLS LAST, metric_name ASC NULLS LAST, metric_type ASC NULLS LAST, identifier ASC NULLS LAST, is_active)
    WITH (deduplicate_items=True)
    TABLESPACE s2110_index;
    
CREATE INDEX s2110_alarm_job_job_status
    ON s2110_alarm_job USING btree
    (job_name ASC NULLS LAST, job_status)
    WITH (deduplicate_items=True)
    TABLESPACE s2110_index;
    
-- Indices on archive tables

CREATE INDEX IF NOT EXISTS s2110_alarm_arc_occurrence_clear_fk_i
    ON s2110_alarm_arc USING btree
    (last_clear_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_arc_occurrence_last_fk_i
    ON s2110_alarm_arc USING btree
    (last_problem_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_arc_occurrence_problem_fk_i
    ON s2110_alarm_arc USING btree
    (last_problem_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_arc_agent
    ON s2110_alarm_arc USING btree
    (agent_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_arc_ni
    ON s2110_alarm_arc USING btree
    (agent_id ASC NULLS LAST, ci_id COLLATE pg_catalog."default" ASC NULLS LAST, metric_name COLLATE pg_catalog."default" ASC NULLS LAST, metric_type COLLATE pg_catalog."default" ASC NULLS LAST, identifier COLLATE pg_catalog."default" ASC NULLS LAST, is_active ASC NULLS LAST)
    WITH (deduplicate_items=True)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_occurrence_arc_alarm
    ON s2110_occurrence_arc USING btree
    (alarm_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_action_arc_first_ack_ni
    ON s2110_alarm_action_arc USING btree
    (alarm_id ASC NULLS LAST, is_first_ack ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_action_arc_user_action
    ON s2110_alarm_action_arc USING btree
    (action_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_enrichment_arc_enrichment
    ON s2110_alarm_enrichment_arc USING btree
    (enrichment_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_enrichment_arc_last_enrichment
    ON s2110_alarm_enrichment_arc USING btree
    (alarm_id ASC NULLS LAST, last_enrichment ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_incident_arc_active_ni
    ON s2110_alarm_incident_arc USING btree
    (alarm_id ASC NULLS LAST, is_link_active ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_incident_arc_incident
    ON s2110_alarm_incident_arc USING btree
    (incident_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_job_arc_job_status
    ON s2110_alarm_job_arc USING btree
    (job_name COLLATE pg_catalog."default" ASC NULLS LAST, job_status ASC NULLS LAST)
    WITH (deduplicate_items=True)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_job_arc_alarm
    ON s2110_alarm_job_arc USING btree
    (alarm_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_release_arc_active_ni
    ON s2110_alarm_release_arc USING btree
    (alarm_id ASC NULLS LAST, is_link_active ASC NULLS LAST)
    TABLESPACE s2110_index;
    
CREATE INDEX IF NOT EXISTS s2110_alarm_release_arc_release
    ON s2110_alarm_release_arc USING btree
    (release_id ASC NULLS LAST)
    TABLESPACE s2110_index;
    
-- Clean up duplicate index
DROP INDEX IF EXISTS s2110_idx_fk_alarm_action_to_alarm;
    
-- Drop unused field
ALTER TABLE IF EXISTS s2110_enrichment DROP CONSTRAINT IF EXISTS s2110_enrichment_alarm_fk;
DROP INDEX IF EXISTS s2110_enrichment_alarm_fk_i;
ALTER TABLE IF EXISTS s2110_enrichment DROP COLUMN IF EXISTS alarm_id;
    
-- View
CREATE OR REPLACE VIEW s2110_detailed_alarm_vw
 AS (
SELECT al.id AS alarm_id,
    ag.name AS agent_name,
    al.ci_id,
    al.metric_type,
    al.metric_name,
    al.first_raise_time,
    e.floc_id,
    al.is_active,
    aj_enrichment.job_status AS enrichment_status,
    aj_ui_sending.job_status AS ui_sending_status,
    r.sap_id AS sap_release_id,
        CASE
            WHEN (al.last_clear_id = al.last_occurrence_id) THEN true
            ELSE false
        END AS is_cleared,
    al.node,
    COALESCE(e.severity, last_problem.severity) AS severity,
    e.location_attribute AS location_short,
    e.location_type,
    COALESCE(e.action_class, al.action_class) AS action_class,
    COALESCE(e.top_level, al.top_level) AS top_level,
    last_problem.summary,
    ceiling((EXTRACT(epoch FROM (al.closing_time - al.first_raise_time)) / (60)::numeric)) AS duration_minutes,
    (al.closing_time - al.first_raise_time) AS duration,
        CASE
            WHEN (ua.creation_time IS NOT NULL) THEN GREATEST(ceiling((EXTRACT(epoch FROM (ua.creation_time - al.first_raise_time)) / (60)::numeric)), (0)::numeric)
            ELSE NULL::numeric
        END AS time_to_acknowledge_minutes,
        CASE
            WHEN ((ua.creation_time IS NOT NULL) AND (ua.creation_time > al.first_raise_time)) THEN (ua.creation_time - al.first_raise_time)
            ELSE NULL::interval
        END AS time_to_acknowledge,
        CASE
            WHEN (last_clear.clear_time IS NOT NULL) THEN GREATEST(ceiling((EXTRACT(epoch FROM (last_clear.clear_time - al.first_raise_time)) / (60)::numeric)), (0)::numeric)
            ELSE NULL::numeric
        END AS time_to_clear_minutes,
        CASE
            WHEN ((last_clear.clear_time IS NOT NULL) AND (last_clear.clear_time > al.first_raise_time)) THEN (last_clear.clear_time - al.first_raise_time)
            ELSE NULL::interval
        END AS time_to_clear,
    i.sap_id AS sap_incident_id,
    i.user_id AS incident_created_by,
    ai.link_time AS incident_link_time,
    NULL::text AS incident_closed_by,
    NULL::text AS incident_close_time,
    ua.user_id AS acknowledged_by,
    ar.link_time AS release_link_time,
    COALESCE(e.actionable, al.actionable) AS actionable,
    e.ci_type,
        CASE
            WHEN (aa.id IS NOT NULL) THEN true
            ELSE false
        END AS is_acknowledged,
    e.location_address,
    NULL::text AS comments,
    last_problem.raise_time AS last_occurrence_time,
    last_clear.clear_time,
    e.critical_ci,
    e.instructions,
    e.dashboards,
    al.identifier
   FROM (((((((((((((s2110_alarm al
     JOIN s2110_agent ag ON ((al.agent_id = ag.id)))
     JOIN s2110_alarm_job aj_enrichment ON (((al.id = aj_enrichment.alarm_id) AND (aj_enrichment.job_name = 'enrichment'::text))))
     JOIN s2110_alarm_job aj_ui_sending ON (((al.id = aj_ui_sending.alarm_id) AND (aj_ui_sending.job_name = 'ui_sending'::text))))
     LEFT JOIN s2110_alarm_enrichment ae ON (((al.id = ae.alarm_id) AND ae.last_enrichment)))
     LEFT JOIN s2110_enrichment e ON ((ae.enrichment_id = e.id)))
     LEFT JOIN s2110_occurrence last_problem ON ((al.last_problem_id = last_problem.id)))
     LEFT JOIN s2110_occurrence last_clear ON ((al.last_clear_id = last_clear.id)))
     LEFT JOIN s2110_alarm_incident ai ON (((al.id = ai.alarm_id) AND ai.is_link_active)))
     LEFT JOIN s2110_incident i ON ((ai.incident_id = i.id)))
     LEFT JOIN s2110_alarm_release ar ON (((al.id = ar.alarm_id) AND ar.is_link_active)))
     LEFT JOIN s2110_release r ON ((ar.release_id = r.id)))
     LEFT JOIN s2110_alarm_action aa ON (((al.id = aa.alarm_id) AND aa.is_first_ack)))
     LEFT JOIN s2110_user_action ua ON ((aa.action_id = ua.id)))
UNION ALL
SELECT al.id AS alarm_id,
    ag.name AS agent_name,
    al.ci_id,
    al.metric_type,
    al.metric_name,
    al.first_raise_time,
    e.floc_id,
    al.is_active,
    aj_enrichment.job_status AS enrichment_status,
    aj_ui_sending.job_status AS ui_sending_status,
    r.sap_id AS sap_release_id,
        CASE
            WHEN (al.last_clear_id = al.last_occurrence_id) THEN true
            ELSE false
        END AS is_cleared,
    al.node,
    COALESCE(e.severity, last_problem.severity) AS severity,
    e.location_attribute AS location_short,
    e.location_type,
    COALESCE(e.action_class, al.action_class) AS action_class,
    COALESCE(e.top_level, al.top_level) AS top_level,
    last_problem.summary,
    ceiling((EXTRACT(epoch FROM (al.closing_time - al.first_raise_time)) / (60)::numeric)) AS duration_minutes,
    (al.closing_time - al.first_raise_time) AS duration,
        CASE
            WHEN (ua.creation_time IS NOT NULL) THEN GREATEST(ceiling((EXTRACT(epoch FROM (ua.creation_time - al.first_raise_time)) / (60)::numeric)), (0)::numeric)
            ELSE NULL::numeric
        END AS time_to_acknowledge_minutes,
        CASE
            WHEN ((ua.creation_time IS NOT NULL) AND (ua.creation_time > al.first_raise_time)) THEN (ua.creation_time - al.first_raise_time)
            ELSE NULL::interval
        END AS time_to_acknowledge,
        CASE
            WHEN (last_clear.clear_time IS NOT NULL) THEN GREATEST(ceiling((EXTRACT(epoch FROM (last_clear.clear_time - al.first_raise_time)) / (60)::numeric)), (0)::numeric)
            ELSE NULL::numeric
        END AS time_to_clear_minutes,
        CASE
            WHEN ((last_clear.clear_time IS NOT NULL) AND (last_clear.clear_time > al.first_raise_time)) THEN (last_clear.clear_time - al.first_raise_time)
            ELSE NULL::interval
        END AS time_to_clear,
    i.sap_id AS sap_incident_id,
    i.user_id AS incident_created_by,
    ai.link_time AS incident_link_time,
    NULL::text AS incident_closed_by,
    NULL::text AS incident_close_time,
    ua.user_id AS acknowledged_by,
    ar.link_time AS release_link_time,
    COALESCE(e.actionable, al.actionable) AS actionable,
    e.ci_type,
        CASE
            WHEN (aa.id IS NOT NULL) THEN true
            ELSE false
        END AS is_acknowledged,
    e.location_address,
    NULL::text AS comments,
    last_problem.raise_time AS last_occurrence_time,
    last_clear.clear_time,
    e.critical_ci,
    e.instructions,
    e.dashboards,
    al.identifier
   FROM (((((((((((((s2110_alarm_arc al
     JOIN s2110_agent ag ON ((al.agent_id = ag.id)))
     JOIN s2110_alarm_job_arc aj_enrichment ON (((al.id = aj_enrichment.alarm_id) AND (aj_enrichment.job_name = 'enrichment'::text))))
     JOIN s2110_alarm_job_arc aj_ui_sending ON (((al.id = aj_ui_sending.alarm_id) AND (aj_ui_sending.job_name = 'ui_sending'::text))))
     LEFT JOIN s2110_alarm_enrichment_arc ae ON (((al.id = ae.alarm_id) AND ae.last_enrichment)))
     LEFT JOIN s2110_enrichment_arc e ON ((ae.enrichment_id = e.id)))
     LEFT JOIN s2110_occurrence_arc last_problem ON ((al.last_problem_id = last_problem.id)))
     LEFT JOIN s2110_occurrence_arc last_clear ON ((al.last_clear_id = last_clear.id)))
     LEFT JOIN s2110_alarm_incident_arc ai ON (((al.id = ai.alarm_id) AND ai.is_link_active)))
     LEFT JOIN s2110_incident i ON ((ai.incident_id = i.id)))
     LEFT JOIN s2110_alarm_release_arc ar ON (((al.id = ar.alarm_id) AND ar.is_link_active)))
     LEFT JOIN s2110_release r ON ((ar.release_id = r.id)))
     LEFT JOIN s2110_alarm_action_arc aa ON (((al.id = aa.alarm_id) AND aa.is_first_ack)))
     LEFT JOIN s2110_user_action_arc ua ON ((aa.action_id = ua.id)))
)
     ;