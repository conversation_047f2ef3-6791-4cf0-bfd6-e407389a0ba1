"""Utils module for mon-dica."""

from olympus_common import enums
from olympus_common.elastic_apm import CaptureSpan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def is_heartbeat(description: str) -> bool:
    """Check if it is an heartbeat event."""
    return True if description.startswith("HeartBeat") else False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def conversion_to_severity(alarm_priority: int) -> int:
    """Convert the alarm from the snmp trap to the severity level."""
    match alarm_priority:
        case 1:
            return enums.Severity.CRITICAL.value
        case 2:
            return enums.Severity.WARNING.value
        case 3:
            return enums.Severity.INDETERMINATE.value
        case 4:
            return enums.Severity.INDETERMINATE.value
        case _:
            raise ValueError(f"Unexpected value for alarm_priority: {alarm_priority}")


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def conversion_to_alarmtype(alarm_priority: int) -> str:
    """Convert the alarm from the snmp trap to the alarm type."""
    match alarm_priority:
        case 1:
            return enums.AlarmType.PROBLEM.value
        case 2:
            return enums.AlarmType.PROBLEM.value
        case 3:
            return enums.AlarmType.PROBLEM.value
        case 4:
            return enums.AlarmType.RESOLUTION.value
        case _:
            raise ValueError(f"Unexpected for alarm_priority: {alarm_priority}")
