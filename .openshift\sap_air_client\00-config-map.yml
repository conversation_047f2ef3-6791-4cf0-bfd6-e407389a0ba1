kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-sap-air-client-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/a2110_olympus/run.py
  DEBUG: "0"
  LOGS_FOLDER: "/data/logs"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  OLYMPUS_HOST: "#{olympusHost}#"
  OlYMPUS_PORT: "#{olympusPort}#"
  APP_ENV: "#{appEnv}#"

  #Kafka variables
  KAFKA_TOPICS: >-
    #{kafkaTopics}#
  KAFKA_BOOTSTRAP_SERVERS: >-
    #{kafkaBootstrapServers}#
  KAFKA_ENVIRONMENT: >-
    #{kafkaEnvironment}#
  KAFKA_CLIENT_ID_SUFFIX: "sap-air-client"
  #Sap variables
  SAP_HOST: "#{sapHost}#"
  SAP_SERVICE_ROOT: "#{sapServiceRoot}#"
  SAP_INCIDENT_RESOURCE_PATH: "#{sapIncidentResourcePath}#"
  SAP_RELEASE_RESOURCE_PATH: "#{sapReleaseResourcePath}#"

  #Air variables
  AIR_TOKEN_URL: "#{airTokenUrl}#"
  AIR_SCOPE: "#{airScope}#"
  AIR_GRANT_TYPE: "#{airGrantType}#"
  AIR_URL_CON: "#{airUrlCon}#"

  #Icinga variables
  ICINGA_API_ENDPOINT: "#{icingaApiEndpoint}#"

  #Postgres DB Credential
  DB_HOST: "#{databaseHost}#"
  DB_PORT: "#{databasePort}#"
  DB_NAME: "#{databaseName}#"
  DB_SCHEMA: "#{databaseSchema}#"

  # Auth variables
  JWK_URI: "#{jwkUri}#"
  JWK_VALID_AUDIENCES: >-
    #{jwkValidAudiences}#

  # Elastic APM Credential
  EXTERNAL_TOKEN_EXPIRATION: "3600"
  ELASTIC_APM_SERVER_URL: "#{elasticApmServerUrl}#"
  ELASTIC_APM_SERVICE_NAME: "#{olympusServiceName}#"
  ELASTIC_APM_VERIFY_SERVER_CERT: "#{elasticApmVerifyServerCert}#"
  ENABLE_ELASTIC_APM: "#{enableElasticApm}#"
  ELASTIC_APM_ENVIRONMENT: "#{elasticApmEnvironment}#"
  ELASTIC_APM_LOG_LEVEL: "#{elasticApmLogLevel}#"
