"""Module to provide pandas helpers to an application."""

import logging
from pathlib import Path

import pandas as pd
from pandas._libs.missing import NAType
from tabulate import tabulate


def clean_raw_dataframe(
    df: pd.DataFrame,
    columns_renaming: dict[str, str] | None = None,
    columns_to_keep: list[str] | None = None,
    drop_if_all_nan: list[str] | None = None,
    log_max_rows: int = 10,
) -> pd.DataFrame:
    """Create a subset of the original dataframe to keep only relevant columns.

    The columns are renamed to have a more human-readable name.

    Parameters
    ----------
    df : pd.DataFrame
        The dataframe containing the raw data.
    columns_renaming : dict[str, str] | None, optional
        The columns renaming. If this is not provided, no renaming is done. By default None
    columns_to_keep : list[str] | None, optional
        The columns to keep, if this is None or empty, the keys of the columns_renaming are used. By default None
    drop_if_all_nan : list[str] | None, optional
        The columns that should not all contain NaN values, rows that have NaN values for all items in the list
        are dropped. By default None
    log_max_rows : int
    Maximum amount of dropped rows to print. This is limited to avoid logging big dataframes

    Notes
    -----
    The slicing is done BEFORE renaming. So `columns_to_keep` should use the old column-names.
    drop_if_all_nan should be the name of the columns AFTER renaming.

    Returns
    -------
    pd.DataFrame
        The dataframe containing only the columns (with the new names) that needed to be renamed.
    """
    if not columns_to_keep and columns_renaming:
        columns_to_keep = list(columns_renaming.keys())
    if columns_to_keep:
        df = df[columns_to_keep]
    if columns_renaming:
        df = df.rename(columns=columns_renaming)
    if drop_if_all_nan:
        # log rows that will be dropped due to all NaN values in specific columns
        rows_to_drop = df[df[drop_if_all_nan].isna().all(axis=1)]
        if not rows_to_drop.empty:
            logging.error(
                f"Dropping {len(rows_to_drop)} rows because all columns {drop_if_all_nan} are NaN. "
                f"rows: {rows_to_drop.head(log_max_rows).to_dict('records')}"
            )

        df = df.dropna(subset=drop_if_all_nan, how="all")
    return df


def df_to_excel(df: pd.DataFrame, dir: Path, name: str = "output.xlsx", index: bool = False):
    """Export the provided df to an excel file at dir/name.xlsx.

    Parameters
    ----------
    df : pd.DataFrame
        The dataframe which should be exported
    dir : Path
        The parent-directory to which the provided df should be exported. Will be created if it does not exist.
    name : str, optional
        The filename for the exported excel file. If the extension is not `.xlsx`, it will be added.
        By default "output.xlsx".
    index : bool, optional
        Passed directly to pandas.to_excel and decides whether or not row names are also exported, by default False
    """
    filename = dir / name
    if not filename.suffix == ".xlsx":
        filename = filename.with_suffix(filename.suffix + ".xlsx")
    dir.mkdir(exist_ok=True, parents=True)
    df.to_excel(filename, index=index)
    print(f"[+] Data exported to {filename}.")


def tabulate_and_to_excel(
    df: pd.DataFrame, dir: Path, name: str = "output.xlsx", index: bool = False, tablfmt: str = "psql"
):
    """Print the provided df by using the third-party library tabulate and then export it to an excel file.

    Parameters
    ----------
    df : pd.DataFrame
        The dataframe which should be exported.
    dir : Path
        The parent-directory to which the provided df should be exported. Will be created if it does not exist.
    name : str, optional
        The filename for the exported excel file. If the extension is not `.xlsx`, it will be added.
        By default "output.xlsx".
    index : bool, optional
        Passed directly to pandas.to_excel and decides whether or not row names are also exported, by default False.
    tablfmt : str, optional
        Passed directly to tabulate and decides the format of the printed data, by default psql.
    """
    pd.set_option("display.max_columns", None)
    print(tabulate(df, df.columns, tablefmt=tablfmt))  # type: ignore[arg-type]
    df_to_excel(df, dir, name=name, index=index)


def check_field_in_df(df: pd.DataFrame, field: str, value: str | NAType | None = None) -> pd.DataFrame:
    """Check the presence of a specific field and set it with the value if it's not there."""
    if field not in df.columns:
        df[field] = value
    return df


def ensure_required_fields(df: pd.DataFrame, fields: list[str]) -> pd.DataFrame:
    """Check if the required fields are present in the dataframe."""
    for field in fields:
        df = check_field_in_df(df=df, field=field)
    return df
