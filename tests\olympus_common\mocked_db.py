"""Mocked db module for the tests."""

from typing import Any

from sqlalchemy.orm import DeclarativeBase, Mapped, Session, mapped_column


class MockedDBModule:
    """Mock the database so that we don't need our tests to rely on mariadb Server."""

    class Base(DeclarativeBase):
        """Represent a table."""

        pass

    class Agent(Base):
        """Represent an agent."""

        __tablename__ = "dummy_table"

        id: Mapped[int] = mapped_column(primary_key=True)

        @classmethod
        def get_by_name(cls, name: str, session: Session | None) -> Any:
            """Select an agent based on its name."""
            return cls(id=0)

        @classmethod
        def get_agent_id_from_name(cls, name: str, session: Session) -> int:
            """Get the agent ID from the agent name.

            This mocked method will always return 0.
            """
            return 0

    class AgentHeartbeat(Base):
        """Represent an agent's heartbeat."""

        __tablename__ = "dummy_table_agent_heartbeat"

        id: Mapped[int] = mapped_column(primary_key=True)

        @classmethod
        def get_heartbeat_cis(cls, agent_name: str, session: Session | None) -> list[str] | None:
            """Return an empty list for testing."""
            return ["DPL2005D000", "A2005_C0000_HeartbeatUAC_PRD-BUILD", "A2005_C0000_HeartbeatUAC_PRD-RUN"]
