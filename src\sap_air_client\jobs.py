"""Job modules for the sap-air-client."""

import asyncio
import json
import logging
from http import H<PERSON><PERSON>tat<PERSON>
from typing import Any

from fastapi.responses import JSONResponse
from requests import HTTPError

from olympus_common import enums, icinga
from olympus_common.datareaders import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from olympus_common.db import AlarmRelease, Incident, Release, create_session
from olympus_common.elastic_apm import elastic_apm, trace_scan
from olympus_common.enums import MeasureType
from sap_air_client.api_service.air import air
from sap_air_client.api_service.incident import odata_incident
from sap_air_client.api_service.release import odata_release
from sap_air_client.config import config
from sap_air_client.jobs_operations import (
    persist_user_action,
    process_incident_creation,
    process_release_link,
    process_unlink_incident,
    process_unlink_release,
    run_air_procedure,
)
from sap_air_client.models import (
    AlarmReleaseLink,
    AlarmUserEntity,
    SapDataToUpdate,
    SapDataView,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    UserActionAdd,
)
from sap_air_client.statics import EMPTY_AIR_DATA, EMPTY_SAP_INCIDENT_DATA, EMPTY_SAP_RELEASE_DATA
from sap_air_client.utils import (
    clear_alarm_manually,
    construct_service_name,
    handle_update_sap_status,
    sanitize_fields,
    update_backend_alarm_incident_link,
)
from sap_air_client.validation_operation import (
    validate_alarm_exists,
    validate_create_incident_params,
    validate_no_incident_link,
    validate_release_exists,
    validate_release_ids,
    validate_release_main_order,
    validate_release_status,
    validate_unlink_incident,
    validate_unlink_release,
)

client = icinga.IcingaClient(use_logging=True)


@trace_scan(MeasureType.CUSTOM.value)
async def get_and_update_incident(incident_id: int, incident_entity: Incident | None = None) -> JSONResponse:
    """Get and update a specific incident from SAP endpoint."""
    status_code, data = await odata_incident.get_entity(incident_id)
    if status_code in [HTTPStatus.OK.value, HTTPStatus.CREATED.value]:
        # Update incident status in the DB
        db_session = create_session(config.service_name)
        try:
            if not incident_entity:
                incident_entity = Incident.get_by_sap_id(data["Qmnum"], db_session)
            handle_update_sap_status(incident_entity, data, db_session)
            # Update incident status in the frontend
            services = client.get_all(
                enums.IcingaObjectType.SERVICE.value, filters=f'match("{incident_id}", service.vars.Sap.Qmnum)'
            )
            data_to_update = SapDataToUpdate(**data)
            for service in services:
                if (
                    data["StatOrdUsrSeq"] == enums.IncidentStatus.RESOLVED.value
                    and service["attrs"]["state"] == enums.IcingaServiceStatus.OK.value
                ):
                    # Update in Icinga to clear SAP and AIR data
                    sap_dict = icinga.format_icinga_attrs(module_name="Sap", attrs=EMPTY_SAP_INCIDENT_DATA)
                    air_dict = icinga.format_icinga_attrs(module_name="AIReactivity", attrs=EMPTY_AIR_DATA)
                    client.update(
                        object_type=enums.IcingaObjectType.SERVICE.value,
                        name=icinga.encode_string(service["name"]),
                        attrs={"attrs": sap_dict | air_dict},
                    )
                elif data["StatOrdUsrSeqTxt"] != service["attrs"]["vars"]["Sap"]["StatOrdUsrSeqTxt"]:
                    # Update SAP status in Icinga
                    client.update(
                        object_type=enums.IcingaObjectType.SERVICE.value,
                        name=icinga.encode_string(service["name"]),
                        attrs={
                            "attrs": icinga.format_icinga_attrs(
                                module_name="Sap", attrs=data_to_update.incident_status()
                            )
                        },
                    )
            db_session.commit()
        except Exception as exc:
            db_session.rollback()
            error_msg = (
                f"Get and update incident - Failed for incident ID : {incident_id} on exception :  {exc.__str__()}"
            )
            logging.error(error_msg)
            elastic_apm.capture_exception()
            return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
        finally:
            db_session.close()
    return JSONResponse(status_code=HTTPStatus.OK.value, content={"success": data})


@trace_scan(MeasureType.CUSTOM.value)
async def get_incident_for_ui(entity_id: int) -> Any:
    """Get a specific incident from SAP incident endpoint."""
    status_code, data = await odata_incident.get_entity(entity_id)

    if status_code not in [HTTPStatus.OK.value, HTTPStatus.CREATED.value]:
        error_msg = f"Get entity for Icinga - Failed for incident ID: {entity_id}"
        logging.error(error_msg)
        return JSONResponse(status_code=status_code, content={"error": error_msg})

    services = client.get_all(
        enums.IcingaObjectType.SERVICE.value, filters=f'match("{entity_id}", service.vars.Sap.Qmnum)'
    )
    data_to_update = SapDataToUpdate(**data)
    for service in services:
        if data["StatOrdUsrSeqTxt"] != service["attrs"]["vars"]["Sap"]["StatOrdUsrSeqTxt"]:
            client.update(
                object_type=enums.IcingaObjectType.SERVICE.value,
                name=icinga.encode_string(service["name"]),
                attrs={"attrs": icinga.format_icinga_attrs(module_name="Sap", attrs=data_to_update.incident_status())},
            )
    # Update incident status in the DB
    db_session = create_session(config.service_name)
    try:
        get_incident_db = Incident.get_by_sap_id(data["Qmnum"], db_session)
        handle_update_sap_status(get_incident_db, data, db_session)
        sap_view_entity = SapDataView(**data)
        db_session.commit()
    except Exception as exc:
        error_msg = f"Get entity for Icinga - Failed for incident ID : {entity_id} on exception :  {exc.__str__()}"
        logging.error(error_msg)
        db_session.rollback()
        elastic_apm.capture_exception()
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
    finally:
        db_session.close()

    return sap_view_entity.model_dump()


def get_release_for_ui(entity_id: int) -> Any:
    """Get a specific release from SAP endpoint."""
    data = odata_release.get_release_less_details(entity_id)
    services = client.get_all(
        enums.IcingaObjectType.SERVICE.value, filters=f'match("{entity_id}", service.vars.Sap.Qmnum)'
    )
    data_to_update = SapDataToUpdate(**data)
    db_session = create_session(config.service_name)
    for service in services:
        if data["UserStatusText"] != service["attrs"]["vars"]["Sap"]["StatOrdUsrSeqTxt"]:
            client.update(
                object_type=enums.IcingaObjectType.SERVICE.value,
                name=icinga.encode_string(service["name"]),
                attrs={"attrs": icinga.format_icinga_attrs(module_name="Sap", attrs=data_to_update.release_status())},
            )
    sap_view_entity = SapDataView(**data)
    try:
        # in this case you get it by the Id field and not the Qmnum since it's a release
        get_release_db = Release.get_by_release_id(data["Id"], db_session)
        handle_update_sap_status(get_release_db, data, db_session)
        db_session.commit()
    except Exception as exc:
        error_msg = (
            f"Get release entity for Icinga - Failed for release ID : {entity_id} on exception :  {exc.__str__()}"
        )
        logging.error(error_msg)
        db_session.rollback()
        elastic_apm.capture_exception()
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
    finally:
        db_session.close()

    return sanitize_fields(response=sap_view_entity.model_dump())


async def get_and_update_release(release_id: int, release_entity: Release | None = None) -> JSONResponse:
    """Get and update a specific release from SAP endpoint."""
    # Get the release data from the SAP endpoint.
    status_code, data = await odata_release.get_release(release_id)
    if status_code in [HTTPStatus.OK.value, HTTPStatus.CREATED.value]:
        # Update release status in the DB.
        db_session = create_session(config.service_name)
        try:
            if not release_entity:
                release_entity = Release.get_by_release_id(release_id, db_session)
            handle_update_sap_status(release_entity, data, db_session)
            # Update release status in the frontend.
            services = client.get_all(
                enums.IcingaObjectType.SERVICE.value, filters=f'match("R{release_id}", service.vars.Sap.Qmnum)'
            )
            data_to_update = SapDataToUpdate(**data)
            for service in services:
                # check to clear the sap data if the release is completed or cancelled and the metric is up.
                if (
                    data["UserStatus"] in [enums.ReleaseStatus.COMPLETED.value, enums.ReleaseStatus.CANCELLED.value]
                ) and service["attrs"]["state"] == enums.IcingaServiceStatus.OK.value:
                    # Update in Icinga to clear SAP if the metric is up and the release is completed or cancelled
                    sap_dict = icinga.format_icinga_attrs(module_name="Sap", attrs=EMPTY_SAP_RELEASE_DATA)
                    client.update(
                        object_type=enums.IcingaObjectType.SERVICE.value,
                        name=icinga.encode_string(service["name"]),
                        attrs={"attrs": sap_dict},
                    )
                elif data["UserStatusText"] != service["attrs"]["vars"]["Sap"]["StatOrdUsrSeqTxt"]:
                    # Update SAP status in Icinga if there is a change between the sap data and the icinga data
                    client.update(
                        object_type=enums.IcingaObjectType.SERVICE.value,
                        name=icinga.encode_string(service["name"]),
                        attrs={
                            "attrs": icinga.format_icinga_attrs(
                                module_name="Sap", attrs=data_to_update.release_status()
                            )
                        },
                    )
            db_session.commit()
        except Exception as exc:
            db_session.rollback()
            error_msg = f"Get and update release - Failed for release_id: {release_id} on exception :  {exc.__str__()}"
            logging.error(error_msg)
            elastic_apm.capture_exception()
            return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
        finally:
            db_session.close()
    return JSONResponse(status_code=HTTPStatus.OK.value, content={"success": data})


@trace_scan(MeasureType.CUSTOM.value)
async def create_incident(entity: SapEntityIncident) -> JSONResponse | Any:
    """Create SAP incident and AIR procedure."""
    # Validation checks
    is_valid, error_response, alarm_id = validate_create_incident_params(entity)
    if not is_valid:
        return error_response

    session = create_session(config.service_name)
    try:
        is_valid, error_response, alarm = validate_alarm_exists(
            alarm_id=alarm_id, session=session, keyword="create_incident"
        )
        if not is_valid:
            return error_response

        if AlarmRelease.is_alarm_linked_to_release(alarm_id, session):
            error_msg = f"create_incident - Failed: Alarm is already linked to a release {alarm_id}."
            logging.error(error_msg)
            return JSONResponse(status_code=HTTPStatus.CONFLICT.value, content={"error": error_msg})

        # Create SAP incident
        status_code, data = await odata_incident.create_entity(entity)  # priority request

        if status_code not in [HTTPStatus.OK.value, HTTPStatus.CREATED.value]:
            error_msg = f"create_incident - Failed: unexpected status code {status_code} with message {data}"
            logging.error(error_msg)
            return JSONResponse(status_code=status_code, content={"error": error_msg})

        new_sap_entity = SapEntityIncident(**data)
        # release main order
        is_valid, error_response = await validate_release_main_order(new_sap_entity.qmnum or "")
        if not is_valid:
            return error_response

        if not alarm:
            return JSONResponse(status_code=HTTPStatus.NOT_FOUND.value, content={"error": "Alarm not found"})

        # Check in order to process the creation in database and UI
        is_valid, error_response, incident_record = process_incident_creation(
            session=session, alarm_id=alarm_id, alarm=alarm, new_sap_entity=new_sap_entity, entity=entity, client=client
        )
        if not is_valid:
            return error_response

        # Run AIR procedure if needed
        if entity.run_procedure and incident_record:
            await run_air_procedure(
                session=session,
                entity=entity,
                new_sap_entity=new_sap_entity,
                incident_id=incident_record.id,
            )

        session.commit()

    except HTTPError as exchttp:
        error_code = exchttp.response.status_code
        error_msg = (
            f"create_incident - Failed: While creating incident with error code {error_code}: {exchttp.response.text}"
        )
        logging.error(error_msg)
        return JSONResponse(status_code=error_code, content={"error": error_msg})
    except Exception as exc:
        session.rollback()
        error_msg = f"create_incident - Failed with entity {entity.model_dump()} on exception : {exc.__str__()}"
        logging.error(error_msg)
        elastic_apm.capture_exception()
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
    finally:
        session.close()
    return JSONResponse(status_code=HTTPStatus.OK.value, content={"success": new_sap_entity.model_dump()})


@trace_scan(MeasureType.CUSTOM.value)
async def link_incident(entity: SapDataToUpdate):
    """Add link information to sap incident and update icinga."""
    if not entity.alarm_id:
        error_msg = f"link_incident - Failed: Empty alarm_id on {entity.alarm_id}."
        logging.error(error_msg)
        return JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg})

    if not entity.qmnum:
        error_msg = f"link_incident - Failed: Empty sap incident id on {entity.qmnum}."
        logging.error(error_msg)
        return JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg})

    alarm_id = int(entity.alarm_id)
    session = create_session(config.service_name)
    try:
        is_valid, error_response, alarm = validate_alarm_exists(
            alarm_id=alarm_id, session=session, keyword="link_incident"
        )
        if not is_valid:
            return error_response

        if not alarm:
            return JSONResponse(status_code=HTTPStatus.NOT_FOUND.value, content={"error": "Alarm not found"})

        if AlarmRelease.is_alarm_linked_to_release(alarm_id, session):
            error_msg = f"link_incident - Failed: Alarm is already linked to a release on alarm_id {alarm_id}."
            logging.error(error_msg)
            return JSONResponse(status_code=HTTPStatus.CONFLICT.value, content={"error": error_msg})

        # Patch requests from SAP return a 204 status code and an empty response on success.
        release_main_order_status, sap_response = await odata_incident.release_main_order_incident(int(entity.qmnum))
        if release_main_order_status != HTTPStatus.NO_CONTENT.value:
            error_msg = (
                f"link_incident - Failed: release main order failed with status code: {release_main_order_status}"
            )
            logging.error(error_msg)
            return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})

        update_status, sap_response = await odata_incident.update_entity(entity)

        if update_status not in [HTTPStatus.OK.value, HTTPStatus.CREATED.value]:
            error_message: dict = json.loads(sap_response)
            error_dict: dict = error_message.get("error", {})
            error_code: str = error_dict.get("code", "")
            error_message_property: dict = error_dict.get("message", {})

            if error_code == "IW/190":
                # "Order is not released" error
                error_msg = "link_incident - Failed: Please first release the order in SAP."
                return JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg})

            if error_code == "IM/416":
                # Notification being locked by an user
                error_msg_value: str = error_message_property.get("value", "")
                return JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg_value})

        status_code, data = await odata_incident.get_entity(entity.qmnum)

        if status_code in [HTTPStatus.OK.value, HTTPStatus.CREATED.value]:
            data_to_update = SapDataToUpdate(**data)
            # Update in backend
            update_backend_alarm_incident_link(
                sap_incident_id=entity.qmnum,
                incident_id=None,
                alarm=alarm,
                is_origin_alarm=False,
                add_link=True,
                session=session,
                sap_status=data_to_update.stat_ord_usr_seq,
                user_id=entity.user_id,
            )
            # Persist the user action in the database
            persist_user_action(alarm_id, "link_comment", entity.user_id, entity.user_comment, session)
            # Update in Icinga UI
            data_to_update.tt_main_alarm = enums.SapMainAlarm.NO.value
            client.update(
                object_type=enums.IcingaObjectType.SERVICE.value,
                name=icinga.encode_string(
                    construct_service_name(entity.floc_id or alarm.ci_id, entity.metric_type, entity.metric_name)
                ),
                attrs={"attrs": icinga.format_icinga_attrs(module_name="Sap", attrs=data_to_update.create())},
            )
            session.commit()

    except Exception as exc:
        session.rollback()
        session.close()
        error_msg = f"link_incident - Failed with entity {entity.model_dump()} on exception{exc.__str__()}"
        logging.error(error_msg)
        elastic_apm.capture_exception()
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
    finally:
        session.close()
    return JSONResponse(status_code=HTTPStatus.OK.value, content={})


@trace_scan(MeasureType.CUSTOM.value)
async def unlink_incident(entity: SapDataToUpdate) -> JSONResponse | None:
    """Add unlink information to sap incident and update icinga."""
    is_valid, error_response, alarm_id = validate_unlink_incident(entity)
    if not is_valid:
        return error_response

    session = create_session(config.service_name)

    is_valid, error_response, alarm = validate_alarm_exists(
        alarm_id=alarm_id, session=session, keyword="unlink_incident"
    )
    if not is_valid:
        return error_response

    if not alarm:
        return JSONResponse(status_code=HTTPStatus.NOT_FOUND.value, content={"error": "Alarm not found"})

    try:
        update_status, update_data = await odata_incident.update_entity(entity)
        if update_status in [HTTPStatus.OK.value, HTTPStatus.CREATED.value]:
            is_valid, error_response = process_unlink_incident(entity, session, alarm_id, alarm, client)
            if not is_valid:
                return error_response
            session.commit()
    except Exception as exc:
        session.rollback()
        error_msg = "unlink incident - Failed during the unlink incident with"
        f"entity {entity.model_dump()} on exception :  {exc.__str__()}"
        logging.error(error_msg)
        elastic_apm.capture_exception()
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
    finally:
        session.close()
    return JSONResponse(status_code=HTTPStatus.OK.value, content={})


@trace_scan(MeasureType.CUSTOM.value)
async def clear_alarm(entity: AlarmUserEntity):
    """Clear an alarm manually to Icinga."""
    session = create_session(config.service_name)
    try:
        clear_alarm_manually(session=session, alarm_id=entity.alarm_id, user_id=entity.user_id)
        session.commit()
    except Exception as exc:
        session.rollback()
        error_msg = f"clear alarm - Failed with entity {entity.model_dump()} on exception: {exc.__str__()}"
        logging.error(error_msg)
        elastic_apm.capture_exception()
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
    finally:
        session.close()
    return JSONResponse(status_code=HTTPStatus.OK.value, content={})


@trace_scan(MeasureType.REQUEST.value)
async def enrich_alarm(alarm_id: int) -> JSONResponse | None:
    """Enrich an alarm manually to Icinga."""
    session = create_session(config.service_name)
    is_valid, error_response, alarm = validate_alarm_exists(alarm_id=alarm_id, session=session, keyword="enrich_alarm")
    if not is_valid:
        return error_response

    if not alarm:
        return JSONResponse(status_code=HTTPStatus.NOT_FOUND.value, content={"error": "Alarm not found"})

    try:
        alarm.reenrich(session)
        alarm.resend_to_ui(session)
        session.commit()
    except Exception as exc:
        session.rollback()
        error_msg = f"clear alarm - Failed during on alarm_id {alarm_id} "
        f"to resend to ui in enrich_alarm function on exception :  {exc.__str__()}"
        logging.error(error_msg)
        elastic_apm.capture_exception()
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
    finally:
        session.close()
    return JSONResponse(status_code=HTTPStatus.OK.value, content={})


# create release and update icinga
@trace_scan(MeasureType.CUSTOM.value)
async def link_release(entity: AlarmReleaseLink):
    """Link an alarm to a release.

    Args:
        entity: The entity containing the alarm and release data

    Returns
    -------
        JSONResponse if an error occurred, None otherwise

    This function validates the alarm and release data, then creates a link between them
    in the database and updates the UI and Icinga accordingly.
    """
    session = create_session(config.service_name)
    alarm_id = entity.alarm_id

    try:
        is_valid, error_response, alarm = validate_alarm_exists(
            alarm_id=alarm_id, session=session, keyword="link_release"
        )
        if not is_valid:
            return error_response

        is_valid, error_response = validate_no_incident_link(alarm_id, session)
        if not is_valid:
            return error_response

        is_valid, error_response = validate_release_ids(entity.release_number, entity.external_release_id)
        if not is_valid:
            return error_response

        # Get release data from SAP
        status_code, data = await odata_release.get_release(entity.release_number)

        # Validate release data from SAP
        is_valid, error_response = validate_release_exists(status_code, entity.release_number)
        if not is_valid:
            return error_response

        is_valid, error_response = validate_release_status(data, entity.release_number)
        if not is_valid:
            return error_response

        if not alarm:
            return JSONResponse(status_code=HTTPStatus.NOT_FOUND.value, content={"error": "Alarm not found"})

        process_release_link(
            session=session,
            alarm_id=alarm_id,
            alarm=alarm,
            release_data=data,
            comment=entity.comment,
            external_release_id=entity.external_release_id,
            user_id=entity.user_id,
            user_comment=entity.user_comment,
            metric_type=entity.metric_type,
            metric_name=entity.metric_name,
        )

    except Exception as exc:
        session.rollback()
        elastic_apm.capture_exception()
        error_msg = f"link_release - Failed with entity {entity.model_dump()} on exception: {exc.__str__()}"
        logging.error(error_msg)
        return JSONResponse(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value,
            content={"error": f"link_release - Failed to link release due to {exc.__str__()}"},
        )
    finally:
        session.close()
    return JSONResponse(status_code=HTTPStatus.OK.value, content={})


@trace_scan(MeasureType.CUSTOM.value)
async def unlink_release(entity: AlarmReleaseLink) -> JSONResponse | None:
    """Unlink an alarm from a release."""
    alarm_id = entity.alarm_id
    session = create_session(config.service_name)
    try:
        is_valid, error_response, alarm = validate_alarm_exists(
            alarm_id=alarm_id, session=session, keyword="unlink_release"
        )
        if not is_valid:
            return error_response

        is_valid, error_response, alarm_release = validate_unlink_release(entity, session, alarm_id)
        if not is_valid:
            return error_response

        if not alarm or not alarm_release:
            return JSONResponse(status_code=HTTPStatus.NOT_FOUND.value, content={"error": "Alarm not found"})

        process_unlink_release(
            entity=entity, session=session, alarm_id=alarm_id, alarm=alarm, client=client, alarm_release=alarm_release
        )

        session.commit()
    except Exception as exc:
        session.rollback()
        error_msg = f"unlink_release - Failed with entity {entity.model_dump()} on exception: {exc.__str__()}"
        logging.error(error_msg)
        elastic_apm.capture_exception()
        return JSONResponse(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.value, content={"error": error_msg})
    finally:
        session.close()
    return JSONResponse(status_code=HTTPStatus.OK.value, content={})


@trace_scan(MeasureType.REQUEST.value)
def read_kafka(reader: KafkaReader) -> None:
    """Execute the client in kafka mode to read data in a kakfa topic and store the data in the backend if needed."""
    air.read_kafka_topic(reader)


@trace_scan(MeasureType.CUSTOM.value)
async def update_incidents_status() -> None:
    """Jobs to update all opening sap incidents."""
    session = create_session(config.service_name)
    try:
        incidents = Incident().get_all_unresolved_incidents(session)
        logging.info(f"Found {len(incidents)} unresolved incidents to update")

        # Process incidents concurrently in batches to avoid overwhelming the system
        batch_size = 10
        for i in range(0, len(incidents), batch_size):
            batch = incidents[i : i + batch_size]
            tasks = []

            for incident in batch:
                if sap_id := int(incident.sap_id):
                    tasks.append(get_and_update_incident(sap_id, incident))

            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Log any exceptions that occurred
                for idx, result in enumerate(results):
                    if isinstance(result, Exception):
                        incident = batch[idx]
                        logging.error(f"Failed to update incident {incident.sap_id}: {result}")

        logging.info("Completed updating incidents status")
    except Exception as exc:
        logging.error(f"Error in update_incidents_status: {exc}")
        elastic_apm.capture_exception()
    finally:
        session.close()


@trace_scan(MeasureType.CUSTOM.value)
async def update_releases_status() -> None:
    """Jobs to update all opening releases."""
    session = create_session(config.service_name)
    try:
        releases = Release().get_all_uncompleted_release(session)
        logging.info(f"Found {len(releases)} uncompleted releases to update")

        # Process releases concurrently in batches to avoid overwhelming the system
        batch_size = 10
        for i in range(0, len(releases), batch_size):
            batch = releases[i : i + batch_size]
            tasks = []

            for release in batch:
                if sap_id := int(release.sap_id):
                    tasks.append(get_and_update_release(sap_id, release))

            if tasks:
                # we make sure to wait for all tasks to be completed before moving to the next batch
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # we also make sure that we log any exceptions that occurred in the previous batc
                for idx, result in enumerate(results):
                    if isinstance(result, Exception):
                        release = batch[idx]
                        logging.error(f"Failed to update release {release.sap_id}: {result}")

        logging.info("Completed updating releases status")
    except Exception as exc:
        logging.error(f"Error in update_releases_status: {exc}")
        elastic_apm.capture_exception()
    finally:
        session.close()


# Wrapper functions for APScheduler (since it doesn't natively support async functions)
@trace_scan(MeasureType.CUSTOM.value)
def update_incidents_status_sync() -> None:
    """Run update_incidents_status in a synchronous context for APScheduler."""
    try:
        asyncio.run(update_incidents_status())
    except Exception as exc:
        logging.error(f"Error in update_incidents_status_sync: {exc}")
        elastic_apm.capture_exception()


@trace_scan(MeasureType.CUSTOM.value)
def update_releases_status_sync() -> None:
    """Run update_releases_status in a synchronous context for APScheduler."""
    try:
        asyncio.run(update_releases_status())
    except Exception as exc:
        logging.error(f"Error in update_releases_status_sync: {exc}")
        elastic_apm.capture_exception()


@trace_scan(MeasureType.REQUEST.value)
def add_user_action(user_action: UserActionAdd) -> None:
    """Add user action in the database."""
    session = create_session(config.service_name)
    try:
        persist_user_action(
            alarm_id=user_action.alarm_id,
            action_type=user_action.action_type,
            user_id=user_action.user_id,
            user_comment=user_action.user_comment,
            session=session,
            is_first_ack=user_action.is_first_ack,
        )
        session.commit()
    finally:
        session.close()
