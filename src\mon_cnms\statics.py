"""Statics module for mon-cnms."""

COLUMNS_RENAMING = {
    "SNMPv2-MIB::snmpTrapOID.0": "sourcetype",
    "@timestamp": "source_timestamp",
    "snmptrap.hostname": "hostname",
    "snmptrap.alarmid": "alarmid",
    "snmptrap.eventuei": "eventuei",
    "snmptrap.nodelabel": "nodelabel",
    "snmptrap.ipaddr": "ipaddr",
    "snmptrap.servicename": "servicename",
    "snmptrap.alarmtype": "alarmtype",
    "snmptrap.severityname": "severityname",
    "snmptrap.firsteventtime": "firsteventtime",
    "snmptrap.lasteventtime": "lasteventtime",
    "snmptrap.logmsg": "logmsg",
    "snmptrap.description": "description",
    "snmptrap.operinstruct": "operinstruct",
}
COLUMNS_TO_KEEP = list(COLUMNS_RENAMING.keys())
TOPLEVEL_DEFAULT = "A1926,A1927"
TOPLEVEL_DISCARD = {
    "DE1BBLS1A": "A1926",
    "DE1BBLS1B": "A1926",
    "DE1BBRC1DB": "A1926",
    "DE1BEIFBH01": "A1926",
    "DE1BEIFBM01": "A1926",
    "DE1MGW1A": "A1926",
    "DE1MGW1A-SHM": "A1926",
    "DE1R4CR1A": "A1926",
    "DE1R4CR1B": "A1926",
    "DE1SCP1": "A1926",
    "DE1SCP1A": "A1926",
    "DE1SCP1B": "A1926",
    "DE1SCP_CLUSTER": "A1926",
    "DE1SMSC1A": "A1926",
    "DE1SMSC1B": "A1926",
    "DE1CNMS1": "A1927",
    "DE1COAMR1": "A1927",
    "DE1COAMR2": "A1927",
    "DE1HLRDS1": "A1927",
    "DE1HYP1": "A1927",
    "DE1HYP2": "A1927",
    "DE1HYP3": "A1927",
    "DE1MGMT1": "A1927",
    "DE1MVS1": "A1927",
    "DE1RHEVM1": "A1927",
    "DE1RPM1": "A1927",
    "DE1SAN1": "A1927",
    "DE2BBLS2A": "A1926",
    "DE2BBLS2B": "A1926",
    "DE2BBRC2DB": "A1926",
    "DE2BEIFBH02": "A1926",
    "DE2BEIFBM02": "A1926",
    "DE2MGW2A": "A1926",
    "DE2MGW2A-SHM": "A1926",
    "DE2R4CR2A": "A1926",
    "DE2R4CR2B": "A1926",
    "DE2SCP2": "A1926",
    "DE2SCP2A": "A1926",
    "DE2SCP2B": "A1926",
    "DE2SCP_CLUSTER": "A1926",
    "DE2SMSC2A": "A1926",
    "DE2SMSC2B": "A1926",
    "MONBBLS2A": "A1926",
    "MONBBLS2B": "A1926",
    "MONBBRC2DB": "A1926",
    "MONBEIFBH02": "A1926",
    "MONBEIFBM02": "A1926",
    "MONCNMS2": "A1927",
    "MONCOAMR1": "A1927",
    "MONCOAMR2": "A1927",
    "MONDRS1": "A1927",
    "MONHLRDS2": "A1927",
    "MONHYP1": "A1927",
    "MONHYP2": "A1927",
    "MONHYP3": "A1927",
    "MONIPA1": "A1927",
    "MONIPA2": "A1927",
    "MONMGMT1": "A1927",
    "MONMGW2A": "A1926",
    "MONMGW2A-SHM": "A1926",
    "MONMVS2": "A1927",
    "MONR4CR2A": "A1926",
    "MONR4CR2B": "A1926",
    "MONREPO1": "A1927",
    "MONRHEVM1": "A1927",
    "MONRPM2": "A1927",
    "MONSAN1": "A1927",
    "MONSAN1SP": "A1927",
    "MONSCP2": "A1926",
    "MONSCP2A": "A1926",
    "MONSCP2B": "A1926",
    "MONSCP_CLUSTER": "A1926",
    "MONSMSC2A": "A1926",
    "MONSMSC2B": "A1926",
    "MONTSC2A": "A1926",
    "MONX2GO1": "A1927",
    "MUIBBLS1A": "A1926",
    "MUIBBLS1B": "A1926",
    "MUIBBRC1DB": "A1926",
    "MUIBEIFBH01": "A1926",
    "MUIBEIFBM01": "A1926",
    "MUICNMS1": "A1927",
    "MUICOAMR1": "A1927",
    "MUICOAMR2": "A1927",
    "MUIDRS1": "A1927",
    "MUIHLRDS1": "A1927",
    "MUIHYP1": "A1927",
    "MUIHYP2": "A1927",
    "MUIHYP3": "A1927",
    "MUIIPA1": "A1927",
    "MUIIPA2": "A1927",
    "MUIMGMT1": "A1927",
    "MUIMGW1A": "A1926",
    "MUIMGW1A-SHM": "A1926",
    "MUIMVS1": "A1927",
    "MUIR4CR1A": "A1926",
    "MUIR4CR1B": "A1926",
    "MUIREPO1": "A1927",
    "MUIRHEVM1": "A1927",
    "MUIRPM1": "A1927",
    "MUISAN1": "A1927",
    "MUISAN1SP": "A1927",
    "MUISCP1": "A1926",
    "MUISCP1A": "A1926",
    "MUISCP1B": "A1926",
    "MUISCP_CLUSTER": "A1926",
    "MUISMSC1A": "A1926",
    "MUISMSC1B": "A1926",
    "MUITSC1A": "A1926",
    "MUIX2GO1": "A1927",
}
DISCARD_NODES = [
    "DE1SCP_CLUSTER",
    "DE1COAMR1",
    "DE1COAMR2",
    "DE1HLRDS1",
    "DE1HYP1",
    "DE1HYP2",
    "DE1HYP3",
    "DE1MGMT1",
    "DE1RHEVM1",
    "DE1RPM1",
    "DE1SAN1",
    "DE2SCP_CLUSTER",
    "MONCOAMR1",
    "MONCOAMR2",
    "MONDRS1",
    "MONHLRDS2",
    "MONHYP1",
    "MONHYP2",
    "MONHYP3",
    "MONIPA1",
    "MONIPA2",
    "MONMGMT1",
    "MONREPO1",
    "MONRHEVM1",
    "MONRPM2",
    "MONSAN1",
    "MONSAN1SP",
    "MONSCP_CLUSTER",
    "MONSMSC2A",
    "MONSMSC2B",
    "MONTSC2A",
    "MONX2GO1",
    "MUICOAMR1",
    "MUICOAMR2",
    "MUIDRS1",
    "MUIHLRDS1",
    "MUIHYP1",
    "MUIHYP2",
    "MUIHYP3",
    "MUIIPA1",
    "MUIIPA2",
    "MUIMGMT1",
    "MUIREPO1",
    "MUIRHEVM1",
    "MUIRPM1",
    "MUISAN1",
    "MUISAN1SP",
    "MUISCP_CLUSTER",
    "MUISMSC1A",
    "MUISMSC1B",
    "MUITSC1A",
    "DE1SCP_CLUSTER",
    "DE1COAMR1",
    "DE1COAMR2",
    "DE1HLRDS1",
    "DE1HYP1",
    "DE1HYP2",
    "DE1HYP3",
    "DE1MGMT1",
    "DE1RHEVM1",
    "DE1RPM1",
    "DE1SAN1",
    "DE2SCP_CLUSTER",
    "MONCOAMR1",
    "MONCOAMR2",
    "MONDRS1",
    "MONHLRDS2",
    "MONHYP1",
    "MONHYP2",
    "MONHYP3",
    "MONIPA1",
    "MONIPA2",
    "MONMGMT1",
    "MONREPO1",
    "MONRHEVM1",
    "MONRPM2",
    "MONSAN1",
    "MONSAN1SP",
    "MONSCP_CLUSTER",
    "MONSMSC2A",
    "MONSMSC2B",
    "MONTSC2A",
    "MONX2GO1",
    "MUICOAMR1",
    "MUICOAMR2",
    "MUIDRS1",
    "MUIHLRDS1",
    "MUIHYP1",
    "MUIHYP2",
    "MUIHYP3",
    "MUIIPA1",
    "MUIIPA2",
    "MUIMGMT1",
    "MUIREPO1",
    "MUIRHEVM1",
    "MUIRPM1",
    "MUISAN1",
    "MUISAN1SP",
    "MUISCP_CLUSTER",
    "MUISMSC1A",
    "MUISMSC1B",
    "MUITSC1A",
    "MUIX2GO1",
]
