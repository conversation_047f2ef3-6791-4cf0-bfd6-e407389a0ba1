"""Utils module for mon-adva."""

import json
from datetime import datetime, timezone

from olympus_common import enums, utils

# variables
lookup_tables = {
    1: "fsp500",
    2: "fsp1500",
    3: "fsp2000",
    4: "fsp3000",
    5: "fsp150CP",
    6: "fsp150Mx",
    10: "fsp150CCt-312",
    11: "fsp150CCd-410",
    12: "fsp150CCf-411",
    13: "fsp150CCt-512",
    14: "fsp150CCs-624",
    15: "fsp150CCd-811",
    16: "fsp150CCf-814",
    17: "fsp150CCf-815",
    18: "fsp150CCf-825",
    19: "fsp150CCs-925",
    20: "fsp150CC-GE206",
    21: "fsp150CC-GE201",
    22: "fsp150CC-GE201SE",
    23: "fsp150CC-324",
    24: "fsp150CC-584",
    25: "fsp150CC-GE206F",
    26: "fsp150EG-X",
    50: "fsp150CM",
    100: "fsp3000R7",
    101: "fsp3000RE",
    1000: "hn4000",
    1001: "hn400",
    9999: "fspNm",
}


def is_heartbeat(metric_type: str) -> bool:
    """Return true if the metric type corresponds to a heartbeat."""
    return metric_type == "HEART-BEAT"


def actionable(location_attr: str, severity_: int, metric_type: str) -> bool:
    """Indicate if an alarm is actionable or not."""
    if is_heartbeat(metric_type):
        return False

    if location_attr in ["LABO", "BRICO 4 LABO"] or severity_ in [1, 2, 3]:
        return False
    else:
        return True


def additional_data(netype: int) -> str:
    """Indicate in which platform the server runs. This data is extracted from the event."""
    return json.dumps({"network_element_type": lookup_tables[int(netype)]})


def ci_id(nename: str, metric_type: str) -> str:
    """Indicate the CI ID."""
    if is_heartbeat(metric_type):
        return "ADVA_HB"

    return nename


def clear_type(type_: int = 2) -> str:
    """Indicate the way the clear is made, possible values between "Not Defined":0, "Manually":1, "Automatically":2."""
    if type_ == 0:
        return enums.ClearType.NOT_DEFINED.value
    elif type_ == 1:
        return enums.ClearType.MANUALLY.value
    else:
        return enums.ClearType.AUTOMATICALLY.value


def event_id(id_: int) -> str:
    """Indicate the event ID."""
    return str(id_) if id_ else "N/A"


def manager() -> str:
    """Name of the Probe."""
    return "mon-adva"


def metric_type(name: str) -> str:
    """Indicate the alarm metrics."""
    if is_heartbeat(name):
        return "/ApplicationEvent/"

    return name


def metric_name(nename: str, metric_type: str) -> str:
    """Indicate the monitored element name of the alarm."""
    if is_heartbeat(metric_type):
        return "Heartbeat"

    return nename


def node(nename: str | None) -> str:
    """Indicate the Hostname or the IP value of the Hostname e.g:'bdiraiorlc012'."""
    return str(nename).upper() if nename else "N/A"


def node_alias(nelpadress: str) -> str:
    """Indicate the Hostname or the IP value of the Hostname."""
    return nelpadress


def raise_time(name: str, nms_time: str, ne_time: str) -> datetime:
    """First date and time when the problem alarm was generated (timestamp sent by EMS)."""
    timestamp = nms_time if is_heartbeat(name) else ne_time
    try:
        year1, year2, month, day, hour, minute, sec, *_ = [int(part, base=16) for part in timestamp.split(":")]
    except Exception:
        # if they change the format of the timestamp revert to the previous parse_time implementation
        year1, year2, month, day, hour, minute, sec, *_ = [ord(u) for u in timestamp]

    date = datetime(year1 * 256 + year2, month, day, hour, minute, sec)
    return date.astimezone(timezone.utc).replace(tzinfo=None)


def scope() -> str:
    """Represent the MD group that is responsible for handling that alarm."""
    return enums.Scope.IT.value


def summary(summary_: str) -> str:
    """Represent the alarm summary."""
    return summary_


def top_level(alarm_: str = "watch") -> str:
    """Indicate the alarm top level."""
    return "A1332" if alarm_.lower() == "watch" else "ADVA_WDM"


def severity(severity_: int, event_type: int) -> int:
    """Indicate the alarm severity."""
    if event_type == 2:
        return enums.Severity.CLEARED.value
    elif severity_ == 1:
        return enums.Severity.INDETERMINATE.value
    elif severity_ == 2:
        return enums.Severity.CRITICAL.value
    elif severity_ == 3:
        return enums.Severity.MAJOR.value
    elif severity_ == 4:
        return enums.Severity.MINOR.value
    else:
        return enums.Severity.WARNING.value


def event_type(event_type_: int, metric_type: str) -> str:
    """Indicate if the alarm is a problem, a resolution or a heartbeat."""
    if is_heartbeat(metric_type):
        return enums.AlarmType.HEARTBEAT.value
    elif int(event_type_) == 2:
        return enums.AlarmType.RESOLUTION.value
    else:
        return enums.AlarmType.PROBLEM.value


def clear_time(event_type_: int, raise_time_: datetime) -> datetime | None:
    """Indicate the clear time of the alarm."""
    return raise_time_ if int(event_type_) == 2 else None


def handle_time() -> datetime:
    """Determine the handle time, i.e. the time when the alarm is handled by Olympus."""
    return utils.now_naive()


def wake_up_time(raise_time_: datetime) -> datetime:
    """Return the wake-up time."""
    return raise_time_
