apiVersion: batch/v1
kind: Job
metadata:
  name: a2110-sbom-tools-cronjob-#{appEnv}#-#{lower(releaseName)}#
  namespace: a2110-olympus-monitoring
spec:
  parallelism: 1
  completions: 1
  backoffLimit: 6
  template:
    metadata:
      labels:
        app: "a2110-sbom-tools-app-#{appEnv}#"
    spec:
      volumes:
        - name: a2110-olympus-volume-claim-#{appEnv}#
          persistentVolumeClaim:
            claimName: a2110-olympus-volume-claim-#{appEnv}#
      containers:
        - name: a2110-sbom-tools-app-#{appEnv}#-#{lower(releaseName)}#
          image: artifactory.msnet.railb.be/a2110-docker/a2110-olympus-image:#{releaseName}#
          envFrom:
            - configMapRef:
                name: a2110-sbom-tools-config-map-#{appEnv}#
            - secretRef:
                name: a2110-sbom-tools-secret-#{appEnv}#
          volumeMounts:
            - name: a2110-olympus-volume-claim-#{appEnv}#
              mountPath: /data
              subPath: "#{olympusServiceName}#/"
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: OnFailure
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
  completionMode: NonIndexed
