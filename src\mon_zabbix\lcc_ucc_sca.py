"""Utils module for mon-zabbix."""

import datetime
import json
import logging
import re
from dataclasses import asdict, dataclass
from typing import NoReturn

import pandas as pd

from mon_zabbix import statics
from mon_zabbix.enums import ZabbixTypes
from olympus_common import enums as olympus_enums
from olympus_common.elastic_apm import CaptureSpan


# ================================================
# Class
# ================================================
@dataclass
class CiMetrics:
    """Class to represent the key_length and metrics of an OS."""

    ElementMonitoredName: str | None = None
    Metrics: str | None = None
    CiSrc: int | None = None  # represent CI source
    Key_length: int | None = None


@dataclass
class ZabbixOsMetrics:
    """Parent Class for keeping the zabbix metrics lookup file."""

    APP_FILE: CiMetrics
    APP_PORT: CiMetrics
    APP_PROC: CiMetrics
    CL_PATH: CiMetrics
    CL_QU: CiMetrics
    CL_RS: CiMetrics
    SYS_FILE: CiMetrics
    SYS_FS: CiMetrics
    SYS_NET: CiMetrics
    SYS_NET_PING: CiMetrics
    SYS_PROC: CiMetrics

    def get_key_length(self, type_subtype: str) -> int:
        """Get the key length."""
        try:
            metric = self._get_ci_metric(type_subtype)
            key_length_ = metric.Key_length
            if key_length_ is None:
                self._raise(metric, "key length")
            return key_length_
        except ValueError as v:
            logging.debug(v)
            return -1

    def get_element_monitored_name(self, type_subtype: str) -> str:
        """Get element monitored name."""
        try:
            metric = self._get_ci_metric(type_subtype)
            element_monitored_name = metric.ElementMonitoredName
            if element_monitored_name is None:
                self._raise(metric, "element monitored name")
            return element_monitored_name
        except ValueError as v:
            logging.debug(v)
            return "No mapping value"

    def get_metrics(self, type_subtype: str) -> str:
        """Get metrics."""
        try:
            metric = self._get_ci_metric(type_subtype)
            metrics = metric.Metrics
            if metrics is None:
                self._raise(metric, "metrics")
            return metrics
        except ValueError as v:
            logging.debug(v)
            return "No mapping value"

    def get_ci_src(self, type_subtype: str) -> int:
        """Get CI Source."""
        try:
            metric = self._get_ci_metric(type_subtype)
            ci_src = metric.CiSrc
            if ci_src is None:
                self._raise(metric, "CI src")
            return ci_src
        except ValueError as v:
            logging.debug(v)
            return -1

    def _get_ci_metric(self, metric_type: str) -> CiMetrics:
        field_name = metric_type.replace("-", "_")
        return getattr(self, field_name)

    def _raise(self, metric: CiMetrics, missing_attribute: str) -> NoReturn:
        raise ValueError(f"Did not find {missing_attribute} on {metric}.")


@dataclass
class ZabbixAixMetrics(ZabbixOsMetrics):
    """Class for keeping the zabbix Aix metrics lookup file."""

    CL_RG: CiMetrics
    CL_RG_A1580: CiMetrics
    FS_JFS2: CiMetrics
    SYS_DAEMON: CiMetrics


@dataclass
class ZabbixUnixMetrics(ZabbixOsMetrics):
    """Class for keeping the zabbix Unix metrics lookup file."""

    CL_RG: CiMetrics
    CL_RG_A1580: CiMetrics
    FS_PL: CiMetrics
    FS_UFS: CiMetrics
    FS_ZFS: CiMetrics
    MW_APACHE: CiMetrics
    MW_APACHE_PERF: CiMetrics
    MW_APACHE_PORT: CiMetrics
    MW_APACHE_SVC: CiMetrics
    MW_JAVA: CiMetrics
    MW_JAVA_SVC: CiMetrics
    MW_TIBCO_AS: CiMetrics
    MW_TIBCO_BW: CiMetrics
    MW_TIBCO_EMS: CiMetrics
    MW_TOMCAT: CiMetrics
    MW_TOMCAT_PERF: CiMetrics
    MW_TOMCAT_PORT: CiMetrics
    MW_TOMCAT_SVC: CiMetrics
    MW_VARNISH: CiMetrics
    MW_VARNISH_SVC: CiMetrics
    SYS_DAEMON: CiMetrics


@dataclass
class ZabbixLinuxMetrics(ZabbixOsMetrics):
    """Class for keeping the zabbix Linux metrics lookup file."""

    APP_SVC: CiMetrics
    MW_APACHE: CiMetrics
    MW_APACHE_PERF: CiMetrics
    MW_APACHE_PORT: CiMetrics
    MW_APACHE_SVC: CiMetrics
    MW_JAVA: CiMetrics
    MW_JAVA_SVC: CiMetrics
    MW_MEMCACHED: CiMetrics
    MW_MEMCACHED_MEM: CiMetrics
    MW_MEMCACHED_SVC: CiMetrics
    MW_ODM: CiMetrics
    MW_ODM_APPL: CiMetrics
    MW_ODM_PROC: CiMetrics
    MW_TIBCO_AS: CiMetrics
    MW_TIBCO_BW: CiMetrics
    MW_TIBCO_EMS: CiMetrics
    MW_TOMCAT: CiMetrics
    MW_TOMCAT_PERF: CiMetrics
    MW_TOMCAT_PORT: CiMetrics
    MW_TOMCAT_SVC: CiMetrics
    MW_VARNISH: CiMetrics
    MW_VARNISH_SVC: CiMetrics
    SYS_CPU: CiMetrics
    SYS_FS_INODE: CiMetrics
    SYS_FS_PFREE: CiMetrics
    SYS_MEM: CiMetrics
    SYS_MEM_AVAILABLE: CiMetrics
    SYS_MEM_FREE: CiMetrics
    SYS_MEM_OSE_FREE: CiMetrics
    SYS_PROC_NUMBER: CiMetrics
    SYS_PROC_RUNNING: CiMetrics
    SYS_SEC: CiMetrics
    SYS_SEC_AUDITD: CiMetrics
    SYS_SVC: CiMetrics
    ZBX_APP: CiMetrics


@dataclass
class ZabbixScadaMetrics(ZabbixOsMetrics):
    """Class for keeping the zabbix Aix metrics lookup file."""

    FS_PL: CiMetrics
    FS_UFS: CiMetrics
    FS_ZFS: CiMetrics
    SYS_DAEMON: CiMetrics


# ================================================
# Variables
# ================================================

# Represent Aix metrics and key_length
aix_metrics_kl = {
    "APP_FILE": CiMetrics(ElementMonitoredName="2", Metrics="/FileWatcher/", CiSrc=1, Key_length=1),
    "APP_PORT": CiMetrics(ElementMonitoredName="2", Metrics="/NetworkResponse/", CiSrc=1, Key_length=1),
    "APP_PROC": CiMetrics(ElementMonitoredName="2", Metrics="/ProcessAvailability/", CiSrc=1, Key_length=1),
    "CL_PATH": CiMetrics(ElementMonitoredName="1", Metrics="/TransportPathStatus/", Key_length=1),
    "CL_QU": CiMetrics(ElementMonitoredName="QuorumVoteStatus", Metrics="/QuorumVoteStatus/", Key_length=1),
    "CL_RG": CiMetrics(ElementMonitoredName="1", Metrics="/CLGroupStatus/", CiSrc=1, Key_length=1),
    "CL_RG_A1580": CiMetrics(ElementMonitoredName="ClusterSwitch", Metrics="/Cluster/ClusterEvent/", Key_length=1),
    "CL_RS": CiMetrics(ElementMonitoredName="1", Metrics="/CLResourceStatus/", Key_length=1),
    "FS_JFS2": CiMetrics(ElementMonitoredName="1", Metrics="/DiskSpace/", Key_length=1),
    "SYS_DAEMON": CiMetrics(ElementMonitoredName="1", Metrics="/ProcessAvailability/", Key_length=1),
    "SYS_FILE": CiMetrics(ElementMonitoredName="1", Metrics="/FileWatcher/", Key_length=1),
    "SYS_FS": CiMetrics(ElementMonitoredName="1", Metrics="/DiskSpace/", Key_length=2),
    "SYS_NET": CiMetrics(Key_length=2),
    "SYS_NET_PING": CiMetrics(ElementMonitoredName="1", Metrics="/NetworkConnectivity/", Key_length=2),
    "SYS_PROC": CiMetrics(ElementMonitoredName="1", Metrics="/ProcessAvailability/", Key_length=2),
}

# Represent Unix metrics and key_length
unix_metrics_kl = {
    "APP_FILE": CiMetrics(ElementMonitoredName="2", Metrics="/Application/FileWatcher/", CiSrc=1, Key_length=1),
    "APP_PORT": CiMetrics(ElementMonitoredName="2", Metrics="/Application/NetworkResponse/", CiSrc=1, Key_length=1),
    "APP_PROC": CiMetrics(ElementMonitoredName="2", Metrics="/ProcessAvailability/", CiSrc=1, Key_length=1),
    "CL_PATH": CiMetrics(ElementMonitoredName="1", Metrics="/Cluster/TransportPathStatus/", Key_length=1),
    "CL_QU": CiMetrics(ElementMonitoredName="QuorumVoteStatus", Metrics="/Cluster/QuorumVoteStatus/", Key_length=1),
    "CL_RG": CiMetrics(ElementMonitoredName="1", Metrics="/ClGroupStatus/", CiSrc=1, Key_length=1),
    "CL_RG_A1580": CiMetrics(ElementMonitoredName="ClusterSwitch", Metrics="/Cluster/ClusterEvent/"),
    "CL_RS": CiMetrics(ElementMonitoredName="1", Metrics="/Cluster/CLResourceStatus/", Key_length=1),
    "FS_PL": CiMetrics(ElementMonitoredName="1", Metrics="/FileSystem/PoolDiskSpaceUsed/", Key_length=1),
    "FS_UFS": CiMetrics(ElementMonitoredName="1", Metrics="/FileSystem/FilesystemDiskSpaceUsed/", Key_length=1),
    "FS_ZFS": CiMetrics(ElementMonitoredName="1", Metrics="/FileSystem/ZfsDiskSpaceUsed/", Key_length=1),
    "MW_APACHE": CiMetrics(Key_length=2),
    "MW_APACHE_PERF": CiMetrics(ElementMonitoredName="2", Metrics="/ApachePerformance/", CiSrc=2),
    "MW_APACHE_PORT": CiMetrics(ElementMonitoredName="2", Metrics="/ApacheResponse/", CiSrc=2),
    "MW_APACHE_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/ApacheProcessRunning/", CiSrc=2),
    "MW_JAVA": CiMetrics(Key_length=2),
    "MW_JAVA_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/JavaProcessRunning/", CiSrc=2),
    "MW_TIBCO_AS": CiMetrics(ElementMonitoredName="2", Metrics="/TIBCOASAvailability/", CiSrc=2),
    "MW_TIBCO_BW": CiMetrics(ElementMonitoredName="2", Metrics="/TIBCOBWAvailability/", CiSrc=2),
    "MW_TIBCO_EMS": CiMetrics(ElementMonitoredName="2", Metrics="/TIBCOEMSAvailability/", CiSrc=2),
    "MW_TOMCAT": CiMetrics(Key_length=2),
    "MW_TOMCAT_PERF": CiMetrics(ElementMonitoredName="2", Metrics="/TomcatPerformance/", CiSrc=2),
    "MW_TOMCAT_PORT": CiMetrics(ElementMonitoredName="2", Metrics="/TomcatResponse/", CiSrc=2),
    "MW_TOMCAT_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/TomcatProcessRunning/", CiSrc=2),
    "MW_VARNISH": CiMetrics(Key_length=2),
    "MW_VARNISH_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/VARNISHAvailability/", CiSrc=2),
    "SYS_DAEMON": CiMetrics(ElementMonitoredName="1", Metrics="/System/DaemonAvailability/", Key_length=1),
    "SYS_FILE": CiMetrics(ElementMonitoredName="1", Metrics="/System/FileWatcher/", Key_length=1),
    "SYS_FS": CiMetrics(ElementMonitoredName="1", Metrics="/FileSystem/FileSystemFreeSpace/", Key_length=1),
    "SYS_NET": CiMetrics(Key_length=2),
    "SYS_NET_PING": CiMetrics(ElementMonitoredName="1", Metrics="/System/NetworkConnectivity/"),
    "SYS_PROC": CiMetrics(ElementMonitoredName="1", Metrics="/System/ProcessAvailability/", Key_length=1),
}

# Represent Unix metrics and key_length
linux_metrics_kl = {
    "APP_FILE": CiMetrics(ElementMonitoredName="2", Metrics="/Application/FileWatcher/", CiSrc=1, Key_length=1),
    "APP_PORT": CiMetrics(ElementMonitoredName="2", Metrics="/Application/NetworkResponse/", CiSrc=1, Key_length=1),
    "APP_PROC": CiMetrics(ElementMonitoredName="2", Metrics="/ProcessAvailability/", CiSrc=1, Key_length=1),
    "APP_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/ProcessAvailability/", CiSrc=1, Key_length=1),
    "CL_PATH": CiMetrics(
        ElementMonitoredName="TransportPathStatus", Metrics="/Cluster/TransportPathStatus/", Key_length=1
    ),
    "CL_QU": CiMetrics(ElementMonitoredName="QuorumVoteStatus", Metrics="/Cluster/QuorumVoteStatus/", Key_length=1),
    "CL_RS": CiMetrics(ElementMonitoredName="1", Metrics="/Cluster/CLResourceStatus/", Key_length=1),
    "MW_APACHE": CiMetrics(Key_length=2),
    "MW_APACHE_PERF": CiMetrics(ElementMonitoredName="2", Metrics="/ApachePerformance/", CiSrc=2),
    "MW_APACHE_PORT": CiMetrics(ElementMonitoredName="2", Metrics="/ApacheResponse/", CiSrc=2),
    "MW_APACHE_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/ApacheProcessRunning/", CiSrc=2),
    "MW_JAVA": CiMetrics(Key_length=2),
    "MW_JAVA_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/JavaProcessRunning/", CiSrc=2),
    "MW_MEMCACHED": CiMetrics(Key_length=2),
    "MW_MEMCACHED_MEM": CiMetrics(ElementMonitoredName="2", Metrics="/MEMCACHEDMemory/", CiSrc=2),
    "MW_MEMCACHED_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/MEMCACHEDRunning/", CiSrc=2),
    "MW_ODM": CiMetrics(Key_length=2),
    "MW_ODM_APPL": CiMetrics(ElementMonitoredName="1", Metrics="/ServiceAvailability/", CiSrc=2),
    "MW_ODM_PROC": CiMetrics(ElementMonitoredName="1", Metrics="/ProcessAvailability/", CiSrc=2),
    "MW_TIBCO_AS": CiMetrics(ElementMonitoredName="2", Metrics="/TIBCOASAvailability/", CiSrc=2, Key_length=1),
    "MW_TIBCO_BW": CiMetrics(ElementMonitoredName="2", Metrics="/TIBCOBWAvailability/", CiSrc=2, Key_length=1),
    "MW_TIBCO_EMS": CiMetrics(ElementMonitoredName="2", Metrics="/TIBCOEMSAvailability/", CiSrc=2, Key_length=1),
    "MW_TOMCAT": CiMetrics(Key_length=2),
    "MW_TOMCAT_PERF": CiMetrics(ElementMonitoredName="2", Metrics="/TomcatPerformance/", CiSrc=2),
    "MW_TOMCAT_PORT": CiMetrics(ElementMonitoredName="2", Metrics="/TomcatResponse/", CiSrc=2),
    "MW_TOMCAT_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/TomcatProcessRunning/", CiSrc=2),
    "MW_VARNISH": CiMetrics(Key_length=2),
    "MW_VARNISH_SVC": CiMetrics(ElementMonitoredName="2", Metrics="/VARNISHAvailability/", CiSrc=2),
    "SYS_CPU": CiMetrics(ElementMonitoredName="1", Metrics="/System/ProcessorLoad/", Key_length=1),
    "SYS_FILE": CiMetrics(ElementMonitoredName="1", Metrics="/System/FileWatcher/", Key_length=1),
    "SYS_FS": CiMetrics(ElementMonitoredName="1", Metrics="/FileSystem/FileSystemFreeSpace/", Key_length=2),
    "SYS_FS_INODE": CiMetrics(ElementMonitoredName="2", Metrics="/FileSystem/FileSystemFreeInode/"),
    "SYS_FS_PFREE": CiMetrics(ElementMonitoredName="2", Metrics="/FileSystem/FileSystemFreeSpace/"),
    "SYS_MEM": CiMetrics(Key_length=2),
    "SYS_MEM_AVAILABLE": CiMetrics(ElementMonitoredName="Available", Metrics="/System/MemoryAvailable/"),
    "SYS_MEM_FREE": CiMetrics(ElementMonitoredName="Free", Metrics="/System/MemoryFree/"),
    "SYS_MEM_OSE_FREE": CiMetrics(ElementMonitoredName="1", Metrics="/System/MemoryFree/"),
    "SYS_NET": CiMetrics(Key_length=2),
    "SYS_NET_PING": CiMetrics(ElementMonitoredName="1", Metrics="/System/NetworkConnectivity/"),
    "SYS_PROC": CiMetrics(ElementMonitoredName="1", Metrics="/System/ProcessAvailability/", Key_length=2),
    "SYS_PROC_NUMBER": CiMetrics(ElementMonitoredName="Number", Metrics="/System/ProcessStatistic/"),
    "SYS_PROC_RUNNING": CiMetrics(ElementMonitoredName="Number", Metrics="/System/ProcessStatistic/"),
    "SYS_SEC": CiMetrics(ElementMonitoredName="1", Metrics="/System/ProcessAvailability/", Key_length=2),
    "SYS_SEC_AUDITD": CiMetrics(ElementMonitoredName="SecurityService", Metrics="/System/SecurityService/"),
    "SYS_SVC": CiMetrics(ElementMonitoredName="1", Metrics="/System/ProcessAvailability/", Key_length=1),
    "ZBX_APP": CiMetrics(ElementMonitoredName="2", Metrics="/ProcessAvailability/", Key_length=1),
}

# Represent Scada metrics and key_length
scada_metrics_kl = {
    "APP_FILE": CiMetrics(ElementMonitoredName="2", Metrics="/Application/FileWatcher/", CiSrc=1, Key_length=1),
    "APP_PORT": CiMetrics(ElementMonitoredName="2", Metrics="/Application/NetworkResponse/", CiSrc=1, Key_length=1),
    "APP_PROC": CiMetrics(ElementMonitoredName="2", Metrics="/ProcessAvailability/", CiSrc=1, Key_length=1),
    "CL_PATH": CiMetrics(ElementMonitoredName="TransportPathStatus", Metrics="/TransportPathStatus/", Key_length=1),
    "CL_QU": CiMetrics(ElementMonitoredName="QuorumVoteStatus", Metrics="/QuorumVoteStatus/", Key_length=1),
    "CL_RS": CiMetrics(ElementMonitoredName="1", Metrics="/CLResourceStatus/", Key_length=1),
    "FS_PL": CiMetrics(ElementMonitoredName="1", Metrics="/FileSystem.PoolDiskSpaceIsed/", Key_length=1),
    "FS_UFS": CiMetrics(ElementMonitoredName="1", Metrics="/FileSystem/FilesystemDiskSpaceUsed/", Key_length=1),
    "FS_ZFS": CiMetrics(ElementMonitoredName="1", Metrics="/FileSystem/ZfsDiskSpaceUsed/", Key_length=1),
    "SYS_DAEMON": CiMetrics(ElementMonitoredName="1", Metrics="/System/DaemonAvailability/", Key_length=1),
    "SYS_FILE": CiMetrics(ElementMonitoredName="1", Metrics="/System/FileWatcher/", Key_length=1),
    "SYS_FS": CiMetrics(ElementMonitoredName="1", Metrics="/FileSystem/FileSystemFreeSpace/", Key_length=1),
    "SYS_NET": CiMetrics(Key_length=2),
    "SYS_NET_PING": CiMetrics(ElementMonitoredName="1", Metrics="/System/NetworkConnectivity/"),
    "SYS_PROC": CiMetrics(ElementMonitoredName="1", Metrics="/System/ProcessAvailability/", Key_length=1),
}


# ================================================
# functions
# ================================================


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def metric_type_metric_subtype(row: pd.Series) -> pd.Series:
    """Create the metric type and the metric subtype based on the trigger_comments field.

    This way of working will be maintained until there are always object_1 and object_2 in input.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    pd.Series
        Pandas Series containing a tuple formed by the metric type and metric subtype.
    """
    if _is_heartbeat(row):
        return pd.Series((row["metric_type"], statics.NA_STR))

    if row["object_1"] and row["object_2"] and row["object_1"] != statics.NA_STR and row["object_2"] != statics.NA_STR:
        return pd.Series((row["object_1"], row["object_2"]))

    trigger_comments: str = row["trigger_comments"]
    if regex_result := re.search("[a-zA-Z0-9]+::[a-zA-Z0-9]+", trigger_comments.upper()):
        metric_type = regex_result[0].split("::")[0]
        metric_subtype = regex_result[0].split("::")[1]
    else:
        logging.debug(f"No match for regex '[a-zA-Z0-9]+::[a-zA-Z0-9]+' in {trigger_comments}")
        metric_type = statics.NA_STR
        metric_subtype = statics.NA_STR
    return pd.Series((metric_type, metric_subtype))


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def object1_object2(row: pd.Series) -> pd.Series:
    """Create the object1 and object2 fields based on the trigger_comments field.

    This way of working will be maintained until there are always object_3 and object_4 in input.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    pd.Series
        Pandas Series containing a tuple formed by the object1 and object2.
    """
    if row["object_3"] and row["object_4"] and row["object_3"] != statics.NA_STR and row["object_4"] != statics.NA_STR:
        return pd.Series((row["object_3"], row["object_4"]))

    comments: str = row["trigger_comments"]
    if row["zabbix_type"] == ZabbixTypes.SCADA.value:
        comments = re.sub(r"{HOSTNAME}|{HOST\.NAME}", row["host"], comments)
    # regex_result = re.findall(r"((?<=::)[a-zA-Z]+)", row["trigger_comments"])
    regex_result = comments.split("::")
    if regex_result and len(regex_result) >= 3:
        object1 = regex_result[2].upper()
        if regex_result and len(regex_result) >= 4:
            object2 = regex_result[3].upper()
        else:
            object2 = statics.NA_STR
    else:
        logging.debug(f"No object item(s) for this value {row['trigger_comments']}")
        object1 = statics.NA_STR
        object2 = statics.NA_STR
    return pd.Series((object1, object2))


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def key_length(row: pd.Series) -> int:
    """Create a key_length(kl) based on the concatenation of metic_type and metric_subtype.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    int
        key_length compute based on the concatenation of metic_type and metric_subtype.
        If 0: The match is not found in the lookup table.

    Raises
    ------
    ValueError
        If the zabbix_type is not in the list of the known types.
    """
    zabbix_type: str = row["zabbix_type"]
    type_subtype = f"{row['metric_type']}_{row['metric_subtype']}"
    if zabbix_type == ZabbixTypes.LINUX:
        zabbix_linux_metrics = ZabbixLinuxMetrics(**linux_metrics_kl)
        if type_subtype in asdict(zabbix_linux_metrics).keys():
            key_length_ = zabbix_linux_metrics.get_key_length(type_subtype)
        else:
            logging.debug(f"No value found for the key {type_subtype}")
            key_length_ = 0
    elif zabbix_type == ZabbixTypes.UNIX:
        zabbix_unix_metrics = ZabbixUnixMetrics(**unix_metrics_kl)
        if type_subtype in asdict(zabbix_unix_metrics).keys():
            key_length_ = zabbix_unix_metrics.get_key_length(type_subtype)
        else:
            logging.debug(f"No value found for the key {type_subtype}")
            key_length_ = 0
    elif zabbix_type == ZabbixTypes.AIX:
        zabbix_aix_metrics = ZabbixAixMetrics(**aix_metrics_kl)
        if type_subtype in asdict(zabbix_aix_metrics).keys():
            key_length_ = zabbix_aix_metrics.get_key_length(type_subtype)
        else:
            logging.debug(f"No value found for the key {type_subtype}")
            key_length_ = 0
    elif zabbix_type == ZabbixTypes.SCADA:
        zabbix_scada_metrics = ZabbixScadaMetrics(**scada_metrics_kl)
        if type_subtype in asdict(zabbix_scada_metrics).keys():
            key_length_ = zabbix_scada_metrics.get_key_length(type_subtype)
        else:
            logging.debug(f"No value found for the key {type_subtype}")
            key_length_ = 0
    else:
        raise ValueError(
            f"zabbix_type {zabbix_type} does not correspond to one of these: "
            f"{[ZabbixTypes.LINUX, ZabbixTypes.UNIX, ZabbixTypes.AIX]}"
        )
    return key_length_


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def metrics_key(row: pd.Series) -> str:
    """Create the metrics_key based on the concatenation of multiple fields.

    if key_length=1: metric_type and metric_subtype
    else if key_length = 2 metricType, metric_sub_type and object_1
    else "N/A"

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    str
        The metrics key which will be used for mapping.
    """
    if row["key_length"] == 1:
        metrics_key = f"{row['metric_type']}_{row['metric_subtype']}"
    elif row["key_length"] == 2:
        if row["object_3"] != statics.NA_STR:
            metrics_key = f"{row['metric_type']}_{row['metric_subtype']}_{row['object_3']}"
        else:
            logging.debug(f"No metrics key for the entry: {row['object_3']}")
            metrics_key = statics.NA_STR
    else:
        logging.debug(f"{row['key_length']} equal to 0 means no match in the previous step.")
        metrics_key = statics.NA_STR
    return metrics_key


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def lookup_table_elements(row: pd.Series) -> pd.Series:
    """Perform a request on a lookup table in order to retrieve the element_monitored_name, the metrics and the ci_src.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    pd.Series
        Contains element_monitored_name, metrics and ci_src.

    Raises
    ------
    ValueError
        If the zabbix_type is not in the list of the known types.
    """
    ci_src: int | str = statics.NA_STR
    if _is_heartbeat(row):
        element_monitored_name = row["metric_name"]
        metrics = row["metric_type"]
    elif row["metrics_key"] == statics.NA_STR:
        logging.debug("value was not defined in the previous step")
        element_monitored_name = statics.NA_STR
        metrics = statics.NA_STR
    else:
        zabbix_type: str = row["zabbix_type"]
        zabbix_os_metrics: ZabbixOsMetrics
        if zabbix_type == ZabbixTypes.LINUX.value:
            zabbix_os_metrics = ZabbixLinuxMetrics(**linux_metrics_kl)
        elif zabbix_type == ZabbixTypes.UNIX.value:
            zabbix_os_metrics = ZabbixUnixMetrics(**unix_metrics_kl)
        elif zabbix_type == ZabbixTypes.AIX.value:
            zabbix_os_metrics = ZabbixAixMetrics(**aix_metrics_kl)
        elif zabbix_type == ZabbixTypes.SCADA.value:
            zabbix_os_metrics = ZabbixScadaMetrics(**scada_metrics_kl)
        else:
            raise ValueError(
                f"zabbix_type {zabbix_type} does not correspond to one of these: "
                f"{[ZabbixTypes.LINUX.value, ZabbixTypes.UNIX.value, ZabbixTypes.AIX.value]}"
            )

        if row["metrics_key"] in asdict(zabbix_os_metrics).keys():
            element_monitored_name = zabbix_os_metrics.get_element_monitored_name(row["metrics_key"])
            metrics = zabbix_os_metrics.get_metrics(row["metrics_key"])
            ci_src = zabbix_os_metrics.get_ci_src(row["metrics_key"])
        else:
            element_monitored_name = statics.NA_STR
            metrics = statics.NA_STR
    return pd.Series((element_monitored_name, metrics, ci_src))


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def element_name_metrics_actionable(row: pd.Series) -> pd.Series:
    """Compute the element_monitored_name, the metrics and the actionable_alarm fields based on the given parameters.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    pd.Series
        Contains element_monitored_name, metrics and actionable_alarm.
    """
    if _is_heartbeat(row):
        return pd.Series((row["metric_name"], row["metric_type"], False))

    element_monitored_name_: str = row["metric_name"]
    object_3_: str = row["object_3"]
    object_4_: str = row["object_4"]
    summary_: str = row["summary"]
    metrics: str = row["metric_type"]
    # None -> need
    actionable_alarm: bool | None = False
    if _is_heartbeat(row):
        metrics = row["metric_type"]
        element_monitored_name = row["metric_name"]
        actionable_alarm = False
    elif element_monitored_name_ == statics.NA_STR:
        metrics = statics.NA_STR
        element_monitored_name = statics.NA_STR.lower()
        actionable_alarm = False  # Non actionable
    elif element_monitored_name_ == "1":
        element_monitored_name = object_3_.lower()
        actionable_alarm = None
    elif element_monitored_name_ == "2":
        element_monitored_name = object_4_.lower()
        actionable_alarm = None
    elif element_monitored_name_ == "3":
        if summary_.split(" "):
            element_monitored_name = summary_.split(" ")[0].lower()
        else:
            element_monitored_name = statics.NA_STR
        actionable_alarm = None
    else:
        element_monitored_name = element_monitored_name_.lower()
        actionable_alarm = None
    return pd.Series((element_monitored_name, metrics, actionable_alarm))


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def ci_id(row: pd.Series) -> str:
    """Create the ci_id relative of the event.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    str
        The CI_ID needed for enrichment of the event.
    """
    if "event_ci_id" in row and row["event_ci_id"] and row["event_ci_id"] != statics.NA_STR:
        return row["event_ci_id"]

    host: str = row["host"]
    if f"{row['metric_type']}_{row['metric_subtype']}" == statics.CL_RG_STR and statics.SWITCHED_STR in row["summary"]:
        ci_id = "A1580"
    elif isinstance(row["ci_src"], int):
        if row["ci_src"] == 1:
            ci_id = row["object_3"]
        elif row["ci_src"] == 2:
            ci_id = row["object_4"]
        else:
            ci_id = host.split(".")[0] if "." in host else host
    elif isinstance(row["ci_src"], str):
        if row["ci_src"] in {"OSE_API", "OSE_DNS", "OSE_ETCD", "OSE_FREE", "OSE_LOADS", "OSE_NET", "OSE_SDN"}:
            ci_id = statics.ZABBIX_SPECIAL_CI_ID_MAPPING[row["ci_src"]]
        elif "OSE_GFS" in row["ci_src"]:
            ci_id = "MON_GFS_A1647"
        elif "OSE_ROUT" in row["ci_src"]:
            ci_id = "MON_INFRA_A1647"
        else:
            ci_id = host.split(".")[0] if "." in host else host
    else:
        ci_id = host.split(".")[0] if "." in host else host
    return ci_id


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def alert_group(row: pd.Series) -> str:
    """Get the alert_group based on the provided parameters.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    str
        The alert_group relative to the event.
    """
    metric_type: str = row["metric_type"]
    metric_subtype: str = row["metric_subtype"]
    summary: str = row["summary"]
    return (
        "CLUSTER SWITCH"
        if f"{metric_type}_{metric_subtype}" == statics.CL_RG_STR and statics.SWITCHED_STR in summary
        else f"{metric_type}_{metric_subtype}"
    )


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def extended_attributes(row: pd.Series) -> str | None:
    """Create the extended attributes based on the given parameters.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    str | None
        the extended attribute of the event or None is the alert_group is not CL_RS.
    """
    if alert_group(row) == "CL_RS":
        return json.dumps({"resource": row["object_3"]})
    else:
        return json.dumps(
            {
                "object_1": row["object_1"],
                "object_2": row["object_2"],
                "object_3": row["object_3"],
                "object_4": row["object_4"],
                "trigger_comments": row["trigger_comments"],
            }
        )


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def severity(row: pd.Series) -> int:
    """Get the severity based on the provided parameters.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    int
        the severity of the event.
    """
    if _is_heartbeat(row):
        return olympus_enums.Severity.INDETERMINATE.value

    if str(row["problem"]) == "0":
        # Clear
        return olympus_enums.Severity.CLEARED.value

    severity_: int = int(row["severity"])
    clear_level_ = row["event_type"]
    if f"{row['metric_type']}_{row['metric_subtype']}" == statics.CL_RG_STR and statics.SWITCHED_STR in row["summary"]:
        return olympus_enums.Severity.WARNING.value
    elif clear_level_ == olympus_enums.AlarmType.RESOLUTION.value:
        return olympus_enums.Severity.INDETERMINATE.value
    elif severity_ == olympus_enums.Severity.WARNING.value:
        return olympus_enums.Severity.WARNING.value
    elif severity_ == olympus_enums.Severity.MINOR.value:
        return olympus_enums.Severity.MINOR.value
    elif severity_ == olympus_enums.Severity.MAJOR.value:
        return olympus_enums.Severity.MAJOR.value
    elif severity_ == olympus_enums.Severity.CRITICAL.value:
        return olympus_enums.Severity.CRITICAL.value
    else:
        return olympus_enums.Severity.INDETERMINATE.value


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def delay(row: pd.Series) -> int:
    """Return the delay for the provided metrics_key."""
    return 1200 if row["metrics_key"] == statics.CL_RG_STR else 0


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def clear_level(row: pd.Series) -> str:
    """Create the clear_level based on the field "value" of the Zabbix API which is retrieved from the elastic API.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    str
        clear_level of the event.
    """
    if _is_heartbeat(row):
        return olympus_enums.AlarmType.HEARTBEAT.value

    return olympus_enums.AlarmType.RESOLUTION.value if row["problem"] == "0" else olympus_enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def node_node_alias(row: pd.Series) -> pd.Series:
    """Create the node and the node alias from the host of the event.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    pd.Series
        Pandas Series which contains the node and the node alias
    """
    host: str = row["host"]
    if "." in host:
        host_split = host.split(".", maxsplit=1)
        node = host_split[0]
        node_alias = host_split[1]
    else:
        node = host
        node_alias = ""
    return pd.Series((node, node_alias))


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def raise_time_clear_time(row: pd.Series) -> pd.Series:
    """Define the raise time and the clear in function of the clear_level.

    Parameters
    ----------
    row : pd.Series
        The row corresponding to the event.

    Returns
    -------
    pd.Series
        panda series containing the raise time and the clear time
    """
    if row["event_type"] == olympus_enums.AlarmType.RESOLUTION.value:
        clear_time = datetime.datetime.fromtimestamp(int(row["clock"]), tz=datetime.timezone.utc).replace(tzinfo=None)
    else:
        clear_time = None
    raise_time = datetime.datetime.fromtimestamp(int(row["clock"]), tz=datetime.timezone.utc).replace(tzinfo=None)
    return pd.Series((raise_time, clear_time))


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def wake_up_time(row: pd.Series) -> datetime.datetime:
    """Define the wake up time in function of the delay."""
    delay_ = delay(row)
    raise_time = row["raise_time"]
    return raise_time + datetime.timedelta(0, delay_)


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def top_level() -> str:
    """Create the top level following some parameters.

    Returns
    -------
    str
        The top level relative of the event.
    """
    return "A1048"


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def actionable_alarm(row: pd.Series) -> bool | None:
    """Complete the actionable_alarm field of the event."""
    if not row["metrics_key"] or row["metrics_key"] == statics.NA_STR:
        return False
    elif (
        row["severity"] == olympus_enums.Severity.INDETERMINATE.value
        and row["event_type"] != olympus_enums.AlarmType.RESOLUTION.value
    ):
        return False
    else:
        return None


@CaptureSpan(span_type=olympus_enums.MeasureType.CUSTOM.value)
def _is_heartbeat(row: pd.Series) -> bool:
    """Determine if the message is a heartbeat."""
    return row["metric_name"] == "Heartbeat"
