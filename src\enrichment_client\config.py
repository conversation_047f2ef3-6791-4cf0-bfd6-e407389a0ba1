"""Configuration module for the enrichment-client."""

from dataclasses import dataclass

from olympus_common.config import CMDBConfig, DatabaseServiceConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for enrichment-client."""

    cmdb_config: CMDBConfig = dataclass_field(CMDBConfig)
    thread_number: int = env_field("THREAD_NUMBER", astype=int, default="50")


config = Config()  # Create the singleton instance at import time.
