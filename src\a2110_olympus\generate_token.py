"""Module to generate tokens used to interact with the different Olympus APIs."""

from dataclasses import dataclass
from pathlib import Path

import requests
from dotenv import load_dotenv

from olympus_common.dataclass import env_field


@dataclass
class TokenConfig:
    """Represent the configuration for generating a token."""

    client_id: str = env_field("CLIENT_ID")
    client_secret: str = env_field("CLIENT_SECRET")
    grant_type: str = env_field("GRANT_TYPE", default="client_credentials")
    scope: str = env_field("SCOPE")
    token_url: str = env_field("TOKEN_URL")


def main():
    """Generate a token to interact with the Olympus APIs.

    This function will load the environment variables from the `.env` file and generate a token to interact with the
    Olympus APIs.
    """
    dotenv_path = Path(__file__).parent / ".env"
    load_dotenv(dotenv_path=dotenv_path)
    print(_get_token())


def _get_token():
    config = TokenConfig()
    response = requests.post(
        config.token_url,
        data={
            "client_id": config.client_id,
            "client_secret": config.client_secret,
            "grant_type": config.grant_type,
            "scope": config.scope,
        },
        timeout=10,
    )
    response.raise_for_status()
    response_json = response.json()
    return response_json["access_token"]


if __name__ == "__main__":
    main()
