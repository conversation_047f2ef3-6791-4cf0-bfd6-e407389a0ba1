"""Module to provide the UCMDB objects."""

import logging
from typing import Tuple

import pandas as pd

from icinga_objects import statics
from icinga_objects.config import config
from icinga_objects.ucmdb_repository import UcmdbRepository
from icinga_objects.utils import (
    aggregated_df,
    delete_nan_none,
    format_host_data,
    sort_list_columns,
    to_add,
    to_del,
    to_update,
)
from olympus_common import enums, icinga
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import MeasureType
from olympus_common.ucmdb import create_ucmdb_db_session
from olympus_common.utils import parallelize_process

client = icinga.IcingaClient()


class CIs:
    """Represent the CI object base to create hosts in icinga."""

    host_pending_state = 16
    max_pending_host = 1000

    def __init__(self, ci_type: str) -> None:
        self.ci_type = ci_type
        self.icinga_object = enums.IcingaObjectType.HOST.value
        self.session = create_ucmdb_db_session()
        self.ucmdb_repository = UcmdbRepository(self.session)

    @CaptureSpan(MeasureType.CUSTOM.value)
    def get_all_cis_dashboard_from_ucmdb(self) -> pd.DataFrame:
        """Get all data from the 2 sets of dataframes (dashboards and CIs) on floc_id."""
        dashboards = self.ucmdb_repository.get_all_dashboard_from_ucmdb(self.ci_type)
        cis = self.ucmdb_repository.get_cis_from_ucmdb(self.ci_type)
        # basically if there is dashboard present merge them together
        if not dashboards.empty and not cis.empty:
            merged_df = pd.merge(left=cis, right=dashboards, on="floc_id", how="left", indicator=True)
            # rename columns to remove _x suffix
            merged_df = merged_df.rename(columns=lambda x: x.replace("_x", ""))
            # drop columns with _y suffix
            col_to_drop = [col for col in merged_df.columns if col.endswith("_y")]
            merged_df = merged_df.drop(col_to_drop, axis=1)
            # drop _merge column
            merged_df = merged_df.drop(columns=["_merge"], axis=1)
            return merged_df
        else:
            return cis

    @CaptureSpan(MeasureType.CUSTOM.value)
    def set_ucmdb_df(self) -> pd.DataFrame:
        """Set UCMDB data as dataframe."""
        df = self.get_all_cis_dashboard_from_ucmdb()
        df = aggregated_df(df)
        df = df.map(lambda x: delete_nan_none(x))
        df.rename(columns={"ci_type": "ciType"}, inplace=True)
        return df

    @CaptureSpan(MeasureType.CUSTOM.value)
    def get_hosts_from_icinga(self) -> list[dict]:
        """Get hosts from icinga."""
        return client.get_all(object_type=self.icinga_object, filters=f'"{self.ci_type}" in host.groups')

    @CaptureSpan(MeasureType.CUSTOM.value)
    def set_icinga_df(self) -> pd.DataFrame:
        """Set Icinga data as dataframe."""
        fields = "attrs.vars."
        df = pd.json_normalize(self.get_hosts_from_icinga())
        if not df.empty:
            vars_fields = [col for col in list(df.columns) if fields in col]
            df = df[vars_fields]
            df = df.rename(columns=lambda x: x.split(fields)[1])
            sort_list_columns(df)
            return df
        else:
            client.create_group(self.ci_type, self.ci_type, enums.IcingaObjectType.HOST_GROUP)
            return pd.DataFrame()

    @CaptureSpan(MeasureType.CUSTOM.value)
    def set_ucmdb_and_icinga_df(
        self, df_ucmdb: pd.DataFrame | None = None, df_icinga: pd.DataFrame | None = None
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Set two dataframes one with UCMDB data and another one with Icinga data."""
        if df_ucmdb is None:
            df_ucmdb = self.set_ucmdb_df()
        if df_icinga is None:
            df_icinga = self.set_icinga_df()
        return df_ucmdb, df_icinga

    @CaptureSpan(MeasureType.CUSTOM.value)
    def add_in_icinga(self, df_ucmdb: pd.DataFrame, df_icinga: pd.DataFrame) -> None:
        """Add hosts in icinga."""
        df_to_add = to_add(df_ucmdb, df_icinga)
        if not df_to_add.empty:
            df_to_add = format_host_data(df_to_add)
            data = df_to_add.to_dict(orient="records")
            parallelize_process(data, self._partition_to_add, config.thread_number)
            parallelize_process(data, self._partition_to_check, config.thread_number)
        else:
            logging.debug(f"No {self.ci_type} to add")

    @CaptureSpan(MeasureType.CUSTOM.value)
    def update_in_icinga(self, df_ucmdb: pd.DataFrame, df_icinga: pd.DataFrame) -> None:
        """Update hosts from icinga according to UCMDB."""
        df_to_update = to_update(df_ucmdb, df_icinga, fields_to_ignore=statics.col_to_ignore)
        if not df_to_update.empty:
            df_to_update = format_host_data(df_to_update)
            # drop groups columns because it can't be updated once created, so we will bypass that field for update
            if "groups" in list(df_to_update.columns):
                df_to_update = df_to_update.drop(columns=["groups"])
            data = df_to_update.to_dict(orient="records")
            parallelize_process(data, self._partition_to_update, config.thread_number)
        else:
            logging.debug(f"No {self.ci_type} to update")

    @CaptureSpan(MeasureType.CUSTOM.value)
    def del_in_icinga(self, df_ucmdb: pd.DataFrame, df_icinga: pd.DataFrame) -> None:
        """Delete hosts from icinga according to UCMDB."""
        df_to_del = to_del(df_ucmdb, df_icinga)
        if not df_to_del.empty:
            df_to_del = format_host_data(df_to_del)
            data = df_to_del.to_dict(orient="records")
            parallelize_process(data, self._partition_to_delete, config.thread_number)
        else:
            logging.debug(f"No {self.ci_type} to delete")

    @CaptureSpan(MeasureType.CUSTOM.value)
    def sync_with_icinga(self, purge_base: bool = config.purge_icinga_base) -> None:
        """Synchronise of icinga with UCMDB according to the CI type."""
        if purge_base:
            self.delete_hosts_from_icinga()
        if config.process_check_missing:
            self.process_check_missing()
        df_ucmdb, df_icinga = self.set_ucmdb_and_icinga_df()
        self.add_in_icinga(df_ucmdb, df_icinga)
        self.update_in_icinga(df_ucmdb, df_icinga)
        # We are not deleting hosts for the moment because they may be linked to alarms.
        if config.can_delete_host:
            self.del_in_icinga(df_ucmdb, df_icinga)
        client.show_counter_values()
        client.reset_counter()

    @CaptureSpan(MeasureType.CUSTOM.value)
    def get_unprocessed_hosts(self) -> list:
        """Get unprocessed hosts from icinga."""
        hosts = client.get_all(
            object_type=self.icinga_object,
            filters=f'match({self.host_pending_state}, host.severity) && ("{self.ci_type}" in host.groups)',
        )
        hosts_to_process_check = []
        if hosts:
            if len(hosts) <= self.max_pending_host:
                for host in hosts:
                    hosts_to_process_check.append(host["attrs"]["vars"]["floc_id"])
            else:
                logging.info(
                    f"{len(hosts)} hosts is a very large number to do a query in DB, "
                    f"These pending hosts will be deleted and added to next iteration."
                )
                self.delete_hosts_from_icinga(hosts)
        return hosts_to_process_check

    @CaptureSpan(MeasureType.CUSTOM.value)
    def get_hosts_to_check(self) -> pd.DataFrame:
        """Get hosts to process check for icinga."""
        unprocessed_hosts = self.get_unprocessed_hosts()
        if unprocessed_hosts:
            return self.ucmdb_repository.get_host_to_process_from_floc_id(unprocessed_hosts)
        return pd.DataFrame()

    @CaptureSpan(MeasureType.CUSTOM.value)
    def process_check_missing(self) -> None:
        """Process missing hosts during sync."""
        df_hosts = self.get_hosts_to_check()
        if not df_hosts.empty:
            df_hosts.rename(columns={"ci_type": "ciType"}, inplace=True)
            df_hosts = format_host_data(df_hosts)
            data = df_hosts.to_dict(orient="records")
            parallelize_process(data, self._partition_to_check, config.thread_number)
            client.show_counter_values()
            client.reset_counter()

    @CaptureSpan(MeasureType.CUSTOM.value)
    def delete_hosts_from_icinga(self, host_to_del: list[dict] | None = None) -> None:
        """Delete all hosts type from icinga without check UCMDB data."""
        if isinstance(host_to_del, list):
            hosts = host_to_del
        else:
            hosts = self.get_hosts_from_icinga()
        parallelize_process(hosts, self._partition_to_delete, config.thread_number)

    @CaptureSpan(MeasureType.CUSTOM.value)
    def _partition_to_add(self, record: dict) -> None:
        """Allow to add hosts in parallel."""
        try:
            name = icinga.get_encoded_hostname(record)
            client.add(self.icinga_object, name, record, record["vars.ciType"])
        except Exception as e:
            logging.exception(f"Error while adding {record['vars.floc_id']} : {e}")

    @CaptureSpan(MeasureType.CUSTOM.value)
    def _partition_to_update(self, record: dict) -> None:
        """Allow to update hosts in parallel."""
        try:
            name = icinga.get_encoded_hostname(record)
            client.update(self.icinga_object, name, {"attrs": record})
        except Exception as e:
            logging.exception(f"Error while updating {record['vars.floc_id']} : {e}")

    @CaptureSpan(MeasureType.CUSTOM.value)
    def _partition_to_delete(self, record: dict) -> None:
        """Allow to delete hosts in parallel."""
        try:
            name = icinga.get_encoded_hostname(record)
            client.delete(name=name, object_type=self.icinga_object)
        except Exception as e:
            logging.exception(f"Error while deleting {record['vars.floc_id']} : {e}")

    @CaptureSpan(MeasureType.CUSTOM.value)
    def _partition_to_check(self, record: dict) -> None:
        """Allow to check hosts in parallel."""
        try:
            name = icinga.get_hostname(record)
            client.process_check_result(
                self.icinga_object, name, record["vars.identification"], enums.IcingaHostStatus.OK, "passive"
            )
        except Exception as e:
            logging.exception(f"Error while process-checking {record['vars.floc_id']} : {e}")
