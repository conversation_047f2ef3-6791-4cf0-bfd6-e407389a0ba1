"""Module to provide methods and objects for Icinga api.

References
----------
https://icinga.com/docs/icinga-2/latest/doc/12-icinga2-api
"""

import logging
import time
from collections import Counter
from dataclasses import dataclass, field
from functools import wraps
from typing import Any, Callable, Iterable
from urllib.parse import quote, unquote

import urllib3
from icinga2apic.client import Client
from icinga2apic.exceptions import Icinga2ApiRequestException

from olympus_common.dataclass import env_field
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import IcingaObjectType, MeasureType
from olympus_common.exceptions import IcingaMaxRetryError, OlympusError

urllib3.disable_warnings()

te_floc_class = [
    "IA0101",
    "IA0102",
    "IA0103",
    "IA0201",
    "IA0202",
    "IA0301",
    "IA0302",
    "IA0401",
    "IA0402",
    "IA0403",
    "IA0404",
    "IA0501",
    "IA0502",
    "IA0601",
    "IA0602",
    "IA0701",
    "IA0702",
    "IA0703",
    "IA0704",
    "IA0705",
    "IA0801",
    "IA0802",
    "IA0901",
    "IA0902",
]

it_ems = [
    "airco",
    "bigdata",
    "certificats",
    "gsx",
    "ha",
    "ms defender",
    "openshift",
    "solman",
    "stonebranch",
    "datalines",
    "websphere-mq",
    "zabbix",
    "vrealize",
    "occ",
    "scom",
    "ftp",
    "olympus",
    "pem",
    "dcms",
]


def retry(
    unless: str | Iterable[str] = (),
    backoff_seconds: int = 3,
    max_retries: int = 3,
    raise_on_max_retries: bool = False,
):
    """Run the decorated function in a try/except statement.

    When the function raises a Icinga2ApiRequestException and the max retries are not exceeded, retry to run the
    function.
    The unless argument is used to allow for certain text in an exception to avoid retrying, setting this to None will
    cause the retry to always trigger regardless of the error message.
    We only catch Icinga2ApiRequestException, any other exception will just bubble up and not be retried.

    The first run (current_retry == 0) is not considered a retry, so if max_retry=5, we will actually run the function
    once and then retry 5 times max (total runs would be 6 once retries have been exhausted).

    The raise_on_max_retries flag decides if the IcingaMaxRetryException should be raised or just logged.
    """

    def _decorator(fn: Callable):
        @wraps(fn)
        def _inner(*args, **kwargs) -> Any:
            last_exception = None
            for current_retry in range(max_retries + 1):  # increment max_retries by 1 to include the first run.
                try:
                    return fn(*args, **kwargs)
                except Icinga2ApiRequestException as e:
                    last_exception = e
                    if unless:
                        if isinstance(unless, str):
                            if unless in e.error:
                                logging.error(e)
                                break
                        elif isinstance(unless, Iterable) and any(u in e.error for u in unless):
                            logging.error(e)
                            break

                    if current_retry <= max_retries:
                        msg = f"Retry {current_retry}/{max_retries} because {e.error}: Sleeping for {backoff_seconds}s"
                        logging.error(msg)
                        time.sleep(backoff_seconds)
            else:
                exc = IcingaMaxRetryError(f"Max retries exceeded because: {last_exception}")
                if raise_on_max_retries:
                    if last_exception:
                        raise exc from last_exception
                    else:
                        raise exc
                else:
                    logging.error(exc)

        return _inner

    return _decorator


@dataclass
class IcingaClient:
    """Represent the icinga client."""

    endpoint: str = env_field("ICINGA_API_ENDPOINT")
    user: str = env_field("ICINGA_USER")
    password: str = env_field("ICINGA_PASSWORD")
    counter: Counter = field(default_factory=Counter)
    use_logging: bool = False
    _client = None

    @property
    def client(self):
        """Create an icinga api client if it doesn't exist already."""
        if self._client is None:
            self._client = Client(self.endpoint, self.user, self.password)
        return self._client

    @CaptureSpan(MeasureType.REQUEST.value)
    @retry(unless="No objects found")
    def get(self, object_type: IcingaObjectType, name: str) -> dict:
        """Get object details from its name.

        Parameters
        ----------
        object_type : IcingaObjectType
            The Icinga object type.
        name : str
            The icinga object name.

        Returns
        -------
        dict
            The object information.
        """
        return self.client.objects.get(object_type=object_type, name=name)

    @CaptureSpan(MeasureType.REQUEST.value)
    @retry(unless="No objects found")
    def get_all(
        self, object_type: IcingaObjectType, filters: str | None = None, attrs: list | None = None, joins: bool = False
    ) -> list[dict]:
        """Get service details from its name.

        Parameters
        ----------
        object_type : IcingaObjectType
            The Icinga object type.
        filters : str | None
            filters matched object(s).
        attrs : list | None
            Get only the specified objects attributes.
        joins : bool
            It allows to get the joined object, e.g. for a Service the Host object.
            Examples: client.objects.get('Service', 'server!ping', joins=True)

        Returns
        -------
        list[dict]
            The list of all object information for the given type.
        """
        return self.client.objects.list(object_type=object_type, filters=filters, attrs=attrs, joins=joins)

    @CaptureSpan(MeasureType.REQUEST.value)
    @retry(unless="already exists")
    def add(
        self,
        object_type: IcingaObjectType,
        name: str,
        attrs: dict,
        template: str | None = None,
        check_command: str | None = None,
        enable_active_checks: bool | None = None,
        max_check_attempts: int | None = None,
    ) -> list[dict]:
        """Add the object into Icinga.

        Notes
        -----
        Except if there is an exception during the addition, the process is retried 20 times
        for multithread purpose.

        Parameters
        ----------
        object_type : IcingaObjectType
            The icinga object type.
        name : str
            The name of the object.
        attrs : dict
            The attribute of the object.
        template : str | None, optional
            The icinga template that the object will use, by default None.
        check_command : str | None, optional
            The check command that the object will use, by default None.
        enable_active_checks : bool | None, optional
            Enable or disable the active check for the object, by default None.
        max_check_attempts : int | None, optional
            Number of attempt before hard status, by default None.

        Returns
        -------
        list[dict]
            The icinga response.

        Raises
        ------
        OlympusError
        """
        self._update_attrs(attrs, check_command, enable_active_checks, max_check_attempts)
        kwargs = dict(object_type=object_type, name=name, attrs=attrs)
        match object_type:
            case IcingaObjectType.HOST:
                template = template or "generic-host"
                kwargs["templates"] = [template]
            case IcingaObjectType.SERVICE:
                template = template or "generic-service"
                kwargs["templates"] = [template]
            case IcingaObjectType.DEPENDENCY:
                """Dependency object doesn't take template."""

        response = self.client.objects.create(**kwargs)
        if response["results"][0]["code"] not in [200, 201]:
            logging.error(
                f"Error in Icinga to add: status code:{response['results'][0]['status']},  data: {response['result']}"
            )
        self._register_successful_call("add", f"{object_type} {name} added to Icinga")
        return response

    @CaptureSpan(MeasureType.REQUEST.value)
    @retry()
    def update(self, object_type: IcingaObjectType, name: str, attrs: dict[str, dict[str, Any]]) -> list[dict]:
        """Update an object in icinga.

        Parameters
        ----------
        object_type : IcingaObjectType
            The Icinga object type.
        name : str
            The object name.
        attrs : dict[str, dict[str, Any]]
            Detailed information about the object to update.

        Returns
        -------
        list[dict]
            The icinga response.

        Raises
        ------
        OlympusError
        """
        response = self.client.objects.update(object_type=object_type, name=name, attrs=attrs)
        if response["results"][0]["code"] not in [200, 201]:
            logging.error(
                f"Error in Icinga to update: status code:{response['results'][0]['status']}, "
                f" data: {response['result']}"
            )
        self._register_successful_call("update", f"{object_type} {name} updated")
        return response

    @CaptureSpan(MeasureType.REQUEST.value)
    @retry()
    def delete(self, name: str, object_type: IcingaObjectType) -> list[dict]:
        """Delete an object in icinga.

        Parameters
        ----------
        name : str
            The object name.
        object_type : IcingaObjectType
            The Icinga object type.

        Returns
        -------
        list[dict]
            The icinga response.
        """
        response = self.client.objects.delete(object_type=object_type, name=name)
        self._register_successful_call("delete", f"{object_type} {name} deleted")
        return response

    @CaptureSpan(MeasureType.REQUEST.value)
    @retry()
    def process_check_result(
        self,
        object_type: IcingaObjectType,
        name: str,
        plugin_output: str,
        exit_status: int,
        check_command: str = "dummy",
    ) -> list[dict]:
        """Process a check result for an icinga object.

        Parameters
        ----------
        object_type : IcingaObjectType
            The icinga object type.
        name : str
            The object name.
        plugin_output : str
            The plugins main output.
        exit_status : IcingaServiceStatus | IcingaHostStatus
            The exit status given for the object.
        check_command : str, optional
            The check command used for the object status, by default "dummy".

        Returns
        -------
        list[dict]
            The icinga response.

        Raises
        ------
        OlympusError
        """
        response = self.client.actions.process_check_result(
            object_type=object_type,
            name=name,
            check_command=check_command,
            exit_status=exit_status,
            plugin_output=plugin_output,
        )
        if response["results"][0]["code"] not in [200, 201]:
            logging.error(
                f"Error in Icinga to process_check: status code:{response['results'][0]['status']}, "
                f" data: {response['result']}"
            )
        msg = f"processed check result for {name} with plugin output {plugin_output}"
        self._register_successful_call("process_check", msg)
        return response

    @CaptureSpan(MeasureType.REQUEST.value)
    @retry()
    def create_group(self, name: str, display_name: str, group_type: IcingaObjectType) -> list[dict]:
        """Create an icinga object group if it doesn't exist.

        Parameters
        ----------
        name : str
            The icinga object group name.
        display_name : str
            The icinga object group display name.
        group_type : IcingaObjectType
            The Icinga group type.

        Returns
        -------
        list[dict]
            The icinga response.

        Raises
        ------
        ValueError
            Raise if the group type is not correct.
        """
        if group_type not in [IcingaObjectType.HOST_GROUP, IcingaObjectType.SERVICE_GROUP]:
            msg = f"The given object_type is not in {[IcingaObjectType.HOST_GROUP, IcingaObjectType.SERVICE_GROUP]}."
            logging.error(msg)

        if not self.get(group_type, name):
            response = self.client.objects.create(group_type, name=name, attrs={"display_name": display_name})
            self._register_successful_call("create_group", f"{group_type} {name} added to icinga")
        else:
            response = []
            logging.info(f"Group {name} already exists.")
        return response

    @staticmethod
    def _update_attrs(
        attrs: dict, check_command: str | None, enable_active_checks: bool | None, max_check_attempts: int | None
    ) -> dict:
        """Update the given attributes with the given parameters."""
        generic_attrs: dict[str, str | bool | int] = {}
        if check_command:
            generic_attrs["check_command"] = check_command
        if enable_active_checks is not None:
            generic_attrs["enable_active_checks"] = enable_active_checks
        if max_check_attempts is not None:
            generic_attrs["max_check_attempts"] = max_check_attempts
        if generic_attrs.keys():
            attrs |= generic_attrs
        return attrs

    def show_counter_values(self) -> None:
        """Print the counter values in a nice format."""
        for key in self.counter.keys():
            msg = f"The client has made {self.counter[key]} {key} operations."
            if self.use_logging:
                logging.info(msg)
            else:
                print(msg)

    def reset_counter(self) -> None:
        """Reset the counter."""
        for key in self.counter.keys():
            self.counter[key] = 0

    def _register_successful_call(self, key: str, msg: str) -> None:
        """Log or print the success message and increment the correct counter."""
        if self.use_logging:
            logging.info(msg)
        else:
            print(msg, end="\n")
        self.counter[key] += 1


def encode_string(input_string: str) -> str:
    """URL-encode a string."""
    return quote(input_string, safe="")


def decode_string(input_string: str) -> str:
    """URL-decode a string."""
    return unquote(input_string)


def format_icinga_attrs(module_name: str, attrs: dict) -> dict:
    """Create icinga attributes from data of an external platform like SAP or AIReactivity."""
    data = {}
    for key, value in attrs.items():
        data.update({f"vars.{module_name}.{key}": value})
    return data


def get_hostname(record: dict) -> str:
    """Set name to create host in icinga.

    Parameters
    ----------
    record : dict
        Potentials keys for the host name

    Returns
    -------
    str

    Raises
    ------
    OlympusError
        If no name found for this host.
        If arguments in not None or empty return the host name
    """
    if "vars.floc_id" in record.keys():
        return record["vars.floc_id"]
    elif "floc_id" in record.keys():
        return record["floc_id"]
    elif "vars.identification" in record.keys():
        return record["vars.identification"]
    # If the object come from Icinga
    elif "name" in record.keys():
        return record["name"]
    else:
        raise OlympusError(f"No name found for this host. {record}")


def get_servicename(row: dict) -> str:
    """Get encoded the service name."""
    hostname = get_hostname(row)
    metric_name = row["vars.metric_name"]
    metric_type = row["vars.metric_type"]
    return f"{hostname}!{metric_type}{metric_name}"


def get_encoded_servicename(row: dict) -> str:
    """Get encoded the service name."""
    return encode_string(get_servicename(row))


def get_encoded_hostname(record: dict) -> str:
    """Set encoded  name to create host in icinga.

    Parameters
    ----------
    record : dict
        Potentials keys for the host name

    Returns
    -------
    str
        If arguments in not None or empty return the host name
    """
    return encode_string(get_hostname(record))


def compute_host_group(ci_type: str | None, floc_class: str | None) -> list[str]:
    """Determine host groups."""
    if ci_type:
        group = [ci_type]
    else:
        group = []

    if floc_class:
        if floc_class[0:6] in te_floc_class:
            group.append("TE")
        else:
            group.append("IT")
    return group


def compute_service_group(source: str | None, action_class: str | None) -> list[str]:
    """Determine metric groups."""
    if source:
        group = [source]
    else:
        group = []

    if action_class:
        group.append(action_class)
    elif source:
        if source.lower() in it_ems:
            group.append("IT")
        else:
            group.append("TE")
    else:
        group.append("IT")
    return group
