"""Clean up the git branches that were ever pushed and are no longer available on the remote."""

import subprocess


def main():
    """Remove the local branches that were pushed to a remote, but no longer exist on that remote."""
    cmd = "git fetch --all --prune"
    subprocess.run(cmd)  # noqa: S603
    git_for_each_ref_cmd = 'git for-each-ref --format="%(refname:short),%(upstream:short)" refs/heads'
    branch_info = subprocess.check_output(git_for_each_ref_cmd, shell=True, encoding="utf-8").splitlines()  # noqa: S602
    branches_to_delete = []
    for info in branch_info:
        branch, upstream = info.split(",")
        if not upstream:
            # No upstream means it was never pushed, so this branch should be kept.
            continue
        remote_exists_cmd = f'git rev-parse --verify --quiet "{upstream}"'
        try:
            subprocess.check_output(remote_exists_cmd, shell=True).decode("utf-8")  # noqa: S602
        except subprocess.CalledProcessError:
            # The upstream exists and the remote no longer exists, mark this branch for deletion.
            branches_to_delete.append(branch)

    if branches_to_delete:
        print(f"About to delete the following branches: {branches_to_delete}")
        input("Press Enter to continue or Ctrl+C to cancel.")

    for branch in branches_to_delete:
        subprocess.run(f"git branch -D {branch}")  # noqa: S603


if __name__ == "__main__":
    main()
