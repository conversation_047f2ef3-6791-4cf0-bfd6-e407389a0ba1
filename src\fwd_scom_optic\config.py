"""Configuration module for fwd-scom-optic."""

from dataclasses import dataclass

from olympus_common.config import DatabaseKafkaConsumerServiceConfig
from olympus_common.dataclass import env_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseKafkaConsumerServiceConfig, metaclass=Singleton):
    """Represent the configuration for the SCOM to OPTIC forwarder."""

    app_env: str = env_field("APP_ENV", default="dev")

    syslog_host: str = env_field("SYSLOG_HOST")
    syslog_port: int = env_field("SYSLOG_PORT", astype=int)


config = Config()
