FROM artifactory.msnet.railb.be/a1647-builds/certs-ubi8-python311:latest

# Execute the following commands as root.
USER 0

# Install the oracle instant client which is required to connect to oracle databases.
COPY .openshift/a2110_olympus/external_scripts/oracle-instantclient-basic-*********.05-1.el8.x86_64.rpm /tmp/oracle_instant_client.rpm
RUN yum install -y /tmp/oracle_instant_client.rpm && \
    yum clean all && \
    rm -rf /tmp/oracle_instant_client.rpm

# Upgrade pip and install poetry.
RUN pip install --upgrade pip && \
    pip install poetry==1.8.2 && \
    poetry config certificates.infrabel-artifactory.cert false

# Copy our source and fix-permissions on it.
COPY ./poetry.lock ./pyproject.toml README.md $APP_ROOT/src/
RUN poetry install --no-root --without dev

COPY ./src/ $APP_ROOT/src/src
RUN poetry install --only-root && \
    fix-permissions $APP_ROOT -P

# Run the ENTRYPOINT in a non-root context.
USER 1001

# Set the default entrypoint to run the run script.
ENTRYPOINT ["poetry", "run", "runservice"]
