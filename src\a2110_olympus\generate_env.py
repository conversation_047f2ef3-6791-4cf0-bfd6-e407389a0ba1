"""Generate a dotenv file from all possible configurations."""

from functools import partial
from pathlib import Path

from a2110_olympus._utils import force_import_config
from olympus_common.dataclass import DataclassInstance

SRC_PATH = Path(__file__).parent.parent
ROOT = SRC_PATH.parent


def main() -> None:
    """Generate a dotenv file for all subdirectories in `src` if they have a `config.py`.

    All "config" modules are imported. Missing keys are ignored and replaced with a dummy value to ensure that the
    instantiation of the config object succeeds.

    If a .env file exists for a service, the missing keys are added and the superfluous keys are commented.

    Examples
    --------
    `python -m a2110_olympus.generate_env` will create a .env file on the same level as pyproject.toml.
    """
    configs = _get_configs()
    _write_dotenv(configs)


def _get_dotenv_lines(obj: DataclassInstance) -> list[str]:
    """Recursively find all dotenv lines for the given `obj`."""
    lines = []
    for field in obj.__dataclass_fields__.values():
        if isinstance(field.default_factory, DataclassInstance):
            nested_lines = _get_dotenv_lines(field.default_factory)
            lines.extend(nested_lines)
        elif isinstance(field.default_factory, partial) and field.default_factory.func.__name__ == "_from_env":
            key = field.default_factory.args[0]
            if key == "ENVIRONMENT":
                value = "DEV"
            else:
                value = field.default_factory.keywords["default"] or "0"
            line = f'{key}="{value}"'
            lines.append(line)
    return sorted(set(lines))


def _get_configs(src: Path = SRC_PATH) -> dict[str, DataclassInstance]:
    """Get all available configs.

    Configs are instantiated with dummy values while catching MissingKeyExceptions. This is required because our
    services instantiate a config at import-time.
    """
    configs: dict[str, DataclassInstance] = {}
    for item in src.iterdir():
        if not item.is_dir():
            continue
        config_path = item / "config.py"
        if not config_path.exists():
            continue

        if item.name == "olympus_common":
            continue

        module = force_import_config(item)

        configs[item.name] = module.config
    return configs


def _write_dotenv(configs: dict[str, DataclassInstance], src: Path = SRC_PATH):
    """Write the .env file for each entry in the provided configs.

    If a .env file exists for a service, the missing keys are added and the superfluous keys are commented.
    Keys present in the root .env are also commented
    """
    root_dotenv = ROOT / ".env"
    root_dotenv_content = {}
    if root_dotenv.exists():
        for line in root_dotenv.read_text().splitlines():
            if line and not line.startswith("#"):
                key, value = line.split("=")
                root_dotenv_content[key] = value

    for servicename, config in configs.items():
        final_content = [f"# {servicename.upper()}"]
        lines = _get_dotenv_lines(config)
        final_content.extend(lines)

        dotenv_path = src / servicename / ".env"
        if dotenv_path.exists():
            dotenv_lines = dotenv_path.read_text().splitlines()
            existing_keys = {line.split("=")[0] for line in dotenv_lines if line and not line.startswith("#")}
            new_keys = {line.split("=")[0] for line in final_content if line and not line.startswith("#")}
            missing_keys = new_keys - existing_keys
            for missing_key in missing_keys:
                if missing_key in root_dotenv_content:
                    continue

                if missing_key == "ENVIRONMENT":
                    value = "DEV"
                    fresh_line = f'{missing_key}="{value}"'
                else:
                    fresh_dotenv_lines = _get_dotenv_lines(config)
                    fresh_line = [line for line in fresh_dotenv_lines if line.split("=")[0] == missing_key][0]
                dotenv_lines.append(fresh_line)

            superfluous_keys = existing_keys - new_keys
            superfluous_keys |= set(root_dotenv_content.keys())
            if superfluous_keys:
                updated_dotenv = []
                for line in dotenv_lines:
                    if line and not line.startswith("#") and line.split("=")[0] in superfluous_keys:
                        # Comment `line` as it is no longer found on the config object.
                        line = f"# {line}"
                    updated_dotenv.append(line)
                final_content = updated_dotenv
            else:
                final_content = dotenv_lines
        else:
            dotenv_path.parent.mkdir(parents=True, exist_ok=True)

        dotenv_content = "\n".join(final_content)
        dotenv_path.write_text(dotenv_content)


if __name__ == "__main__":
    main()
