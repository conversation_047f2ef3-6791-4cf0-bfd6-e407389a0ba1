"""Configuration module for mon-big-data."""

from dataclasses import dataclass

from olympus_common.config import DatabaseServiceConfig, JWKConfig, ServerConfig
from olympus_common.dataclass import dataclass_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for this DD."""

    server_config: ServerConfig = dataclass_field(ServerConfig)
    jwk_config: JWKConfig = dataclass_field(JWKConfig)


config = Config()
