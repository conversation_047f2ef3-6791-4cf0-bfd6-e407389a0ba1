kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-core-api-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/a2110_olympus/run.py
  DEBUG: "0"
  LOGS_FOLDER: "/data/logs"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  OLYMPUS_HOST: "#{olympusHost}#"
  OlYMPUS_PORT: "#{olympusPort}#"
  APP_ENV: "#{appEnv}#"
  DB_HOST: "#{databaseHost}#"
  DB_PORT: "#{databasePort}#"
  DB_NAME: "#{databaseName}#"
  DB_SCHEMA: "#{databaseSchema}#"
  JWK_URI: "#{jwkUri}#"
  JWK_VALID_AUDIENCES: >-
    #{jwkValidAudiences}#
  ELASTIC_APM_SERVER_URL: "#{elasticApmServerUrl}#"
  ELASTIC_APM_LOG_LEVEL: "#{elasticApmLogLevel}#"
