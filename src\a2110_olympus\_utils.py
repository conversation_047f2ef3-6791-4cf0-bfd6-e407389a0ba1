"""Utils for a2110_olympus module."""

import importlib
import os
from pathlib import Path
from types import ModuleType

import yaml

from olympus_common.exceptions import MissingKeyError


def get_services(src: Path) -> list[str]:
    """Get all available configs.

    Configs are instantiated with dummy values while catching MissingKeyExceptions. This is required because our
    services instantiate a config at import-time.
    """
    services: list[str] = []
    for item in src.iterdir():
        if not item.is_dir():
            continue

        if force_import_module(item):
            services.append(item.name)
    return services


def get_persistent_volume_claims(src: Path, namespace: str) -> dict[str, dict]:
    """Get all available persistent volume claims for the given namespace.

    They are returned as a dict of dicts. This way we can use the parent.name as an identifier. (eg: mon_zabbix)
    """
    persistent_volume_claims: dict[str, dict] = {}
    pvc_paths = src.glob("**/*persistent-volume-claim*")
    for item in pvc_paths:
        if not item.is_file():
            continue

        pvc_item = yaml.safe_load(item.read_text())
        if pvc_item["kind"] != "PersistentVolumeClaim":
            continue

        if pvc_item["metadata"]["namespace"] != namespace:
            continue

        persistent_volume_claims[item.parent.name] = pvc_item
    return persistent_volume_claims


def force_import_module(item: Path):
    """Forcibly import a module.

    If a MissingKeyException occurs, the config is forcibly imported, then the original import is retried.
    """
    name = item.name
    # Get the main function from the item's name
    try:
        module = importlib.import_module(f"{name}.main")
        module.main  # noqa: B018 (accessing the attribute to check if it exists)
    except MissingKeyError:
        # Import the config, then recursively retry the import
        force_import_config(item)
        return force_import_module(item)
    except (AttributeError, ModuleNotFoundError):
        print(f"Could not find `{name}.main:main`")
        return
    else:
        return module


def force_import_config(item: Path) -> ModuleType:
    """Forcibly import a config module.

    If a MissingKeyException occurs, the missing key is set in `os.environ` and the config import is retried.
    """
    try:
        module = importlib.import_module(f"{item.name}.config")
    except MissingKeyError as exc:
        # Set the missing key in os.environ and recursively retry the import
        missing_key = exc.args[0].args[0]
        if missing_key == "ENVIRONMENT":
            value = "DEV"
        else:
            value = "0"
        os.environ[missing_key] = value
        return force_import_config(item)
    else:
        return module
