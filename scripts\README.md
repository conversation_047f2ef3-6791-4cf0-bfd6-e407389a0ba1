# Useful scripts for A2110-Olympus
## Usage

This project uses [poethepoet](https://pypi.org/project/poethepoet/) to run scripts like install, lint and test.
The pipelines use poe to run various tasks.  
Run a command using poe:  
`poetry run poe $TASKNAME`

## Included scripts
Since poe supports many types of script invocations and we use cli-packages like mypy flake8 etc, there isn't much we have to script ourselves. However, some use-cases require a script. All defined scripts can be found in the `scripts` folder in the root of the project.
- `upgrade_dependencies.py`: Upgrade all poetry dependencies (main and dev group) to their latest version. Note that this script does not respect version boundaries and just takes the latest available version for a given dependency. This can be useful if you want to test your code with the latest version of all current dependencies.

## Information about poe tasks

For an overview of the defined tasks, refer to the section `[tool.poe.tasks]` in `pyproject.toml`<br/>
The `help` key in each task will provide a short description for the defined task.

## Suggested order of task-execution
```bash
poetry install # (required to have poethepoet installed and available.)
# poetry run poe update # (only if you actually want to update all dependencies.)
# poetry run poe upgrade # (only if you actually want to upgrade all dependencies.)
poetry run poe format
poetry run poe lint
poetry run poe test
```
