"""Module to add specific test-cases to the test folder."""

import json
from pathlib import Path


def add_test_case():
    """Add a test-case to the test folder.

    Prompts the user to select a test-case file or create a new one, then adds a test-case to the file.
    The test-case will contain empty records for the output tables. This means that the test will expect a record to be
    created, but it does not verify the contents of the record.
    """
    testcase_dir = Path(__file__).parent.parent / "tests" / "end2end" / "data" / "test"
    testcase_files = list(testcase_dir.iterdir())
    for i, file in enumerate(testcase_files):
        print(f"[{i}] {file.stem}")
    response = input(
        "For which service would you like to add a test-case? (Enter index or name) "
        "(If the filename does not exist, you will be prompted to create it): "
    )
    try:
        index = int(response)
        filename = testcase_files[index]
    except ValueError:
        filename = (testcase_dir / response).with_suffix(".json")

    if not filename.exists():
        response_create = input(f"Test-case {response} does not exist, would you like to create it? (y/N)")
        if response_create.lower() != "y":
            print("Exiting...")
            return
        filename.touch()
        agent_name = input("Enter agent name: ")
        filecontent = {"agent_name": agent_name, "data": []}
    else:
        filecontent = json.loads(filename.read_text())

    print(f"Adding test-case for {filename.stem}")
    print("Please provide the input data for the test-case (Enter 'eof()' to continue): ")
    input_lines = []
    while True:
        line = input()
        if line == "eof()":
            break
        input_lines.append(line)
    input_data = json.loads("\n".join(input_lines))
    output_data = {}
    while True:
        output_table = input(
            "In which table(s) should the input create a record? (Enter 'c' to continue) (eg: s2110_alarm): "
        )
        if not output_table:
            # Continue if no input is given. This could happen accidentally.
            continue
        if output_table == "c":
            break
        output_data[output_table] = [{}]
        print(
            f"Added an empty record, causing the test to expect a record in {output_table}, "
            "but not verify the contents. Manually edit the record to verify fields."
        )
    result = {"input": input_data, "output": output_data}
    freeze_time = input("Would you like to freeze the time to a specific date? (Leave empty to skip): ")
    if freeze_time:
        result["frozen_time"] = freeze_time
    comment = input("Would you like to add a comment to the test-case? (Leave empty to skip): ")
    if comment:
        result["comment"] = comment
    filecontent["data"].append(result)
    filename.write_text(json.dumps(filecontent, indent=2))


if __name__ == "__main__":
    add_test_case()
