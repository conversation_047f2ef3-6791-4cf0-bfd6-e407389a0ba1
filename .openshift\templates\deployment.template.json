{"apiVersion": "apps/v1", "kind": "Deployment", "metadata": {"name": "a2110-#{olympusServiceNameKebab}#-deployment-#{appEnv}#", "namespace": "a2110-olympus-monitoring"}, "spec": {"replicas": 1, "selector": {"matchLabels": {"app": "a2110-#{olympusServiceNameKebab}#-app-#{appEnv}#"}}, "strategy": {"type": "RollingUpdate"}, "template": {"metadata": {"labels": {"app": "a2110-#{olympusServiceNameKebab}#-app-#{appEnv}#"}}, "spec": {"volumes": [{"name": "a2110-olympus-volume-claim-#{appEnv}#", "persistentVolumeClaim": {"claimName": "a2110-olympus-volume-claim-#{appEnv}#"}}], "containers": [{"envFrom": [{"configMapRef": {"name": "a2110-#{olympusServiceNameKebab}#-config-map-#{appEnv}#"}}, {"secretRef": {"name": "a2110-#{olympusServiceNameKebab}#-secret-#{appEnv}#"}}], "image": "artifactory.msnet.railb.be/a2110-docker/a2110-olympus-image:#{releaseName}#", "imagePullPolicy": "Always", "name": "a2110-#{olympusServiceNameKebab}#-app-#{appEnv}#", "resources": {}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File", "volumeMounts": [{"mountPath": "/data", "name": "a2110-olympus-volume-claim-#{appEnv}#", "subPath": "#{olympusServiceName}#/"}]}], "dnsPolicy": "ClusterFirst", "restartPolicy": "Always", "terminationGracePeriodSeconds": 30}}}}