"""Utils to get access to data."""

from typing import Any

import requests

from mon_certificates.config import UcmdbConfig


def _get_ucmdb_header() -> dict:
    """Generate a token from UCMDB and return it within the authorization header."""
    ucmdb_token = _get_ucmdb_token()["access_token"]
    return {"authorization": f"Bearer {ucmdb_token}"}


def get_data_from_ucmdb(url: str) -> Any:
    """Get data from the ucmdb api.

    Parameters
    ----------
    url : str
        This url represent the variable part of the original url allows to get data from ucmdb api

    Returns
    -------
    Any
        represent the ucmdb response as a json object
    """
    ucmdb_config = UcmdbConfig()
    response = requests.get(
        url=f"{ucmdb_config.endpoint}/dbdata/procedures/pGetCiInfoV{ucmdb_config.version}?{url}",
        headers=_get_ucmdb_header(),
        timeout=30,
    )
    response.raise_for_status()
    return response.json()


def _get_ucmdb_token() -> Any:
    """Get the ucmdb token.

    Returns
    -------
    Any
        JSON type that contains a UCMDB token.
    """
    ucmdb_config = UcmdbConfig()
    response = requests.post(ucmdb_config.token_url, data=ucmdb_config.to_dict(), timeout=30)
    response.raise_for_status()  # Potentially raises requests.HTTPError
    return response.json()  # Potentially raises requests.JSONDecodeError
