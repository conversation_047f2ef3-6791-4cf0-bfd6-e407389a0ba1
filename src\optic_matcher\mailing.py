"""Mailing module for optic-matcher."""

import logging
from urllib import parse

import requests

from optic_matcher.config import config


def get_proxy(user: str | None = None, password: str | None = None, https: bool = False) -> dict:
    """Return the proxy dict as needed for the requests library.

    Parameters
    ----------
    user : str | None, optional
        The user used for the proxy configuration (try to use sys_user), by default None
    password : str | None, optional
        The password of the user, by default None
    https : bool, optional
        The bool argument to choose between http or https protocol, by default False

    Returns
    -------
    dict
        The dict for proxies needed for requests lib.
    """
    if user and password:
        password = parse.quote(password)
        proxy = f"http://{user}:{password}@proxyi.msnet.railb.be:80"
    else:
        proxy = "http://proxyi.msnet.railb.be:80"
    if https:
        return {"https": proxy}
    else:
        return {"http": proxy}


def send_graph_email(
    exception: Exception, recipients_: list[str], mail_text: str, subject: str, sender: str, cc_recipients_: list[str]
):
    """Send an email with the MS Graph API and save it to the sent items with the category certificate.

    Parameters
    ----------
    exception : Exception
        Exception to put in the body of the email.
    recipients_ : list[str]
        list of the email address of the recipients of the email.
    mail_text : str
        Html body of the email.
    subject : str
        Subject of the email.
    sender : str
        email address from which you send the email.
    cc_recipients_ : list[str]
        list of the email address of the CC recipients of the email. Default = set()
    """
    https_proxy = get_proxy(https=True)

    recipients = [{"EmailAddress": {"Address": receiver}} for receiver in recipients_]
    cc_recipients = [{"EmailAddress": {"Address": receiver}} for receiver in cc_recipients_]

    token_result = get_token(https_proxy)

    access_token = token_result.get("access_token")

    if not access_token:
        logging.error(token_result.get("error"))
        logging.error(token_result.get("error_description"))
        return

    endpoint = f"https://graph.microsoft.com/v1.0/users/{sender}/sendMail"
    email_msg = {
        "Message": {
            "Subject": subject,
            "Body": {"ContentType": "html", "Content": f"{mail_text} : {exception}"},
            "ToRecipients": recipients,
            "ccRecipients": cc_recipients,
        },
    }
    response = requests.post(
        endpoint,
        headers={"Authorization": "Bearer " + access_token},
        json=email_msg,
        timeout=30,
        proxies=https_proxy,
    )
    if response.ok:
        logging.info("Email sent successfully")
    else:
        logging.error(response.json())


def get_token(https_proxy: dict) -> dict:
    """Get token from MS Graph API."""
    url = f"https://login.microsoftonline.com/{config.graph_config.tenant_id}/oauth2/v2.0/token"
    body = {
        "client_id": config.graph_config.client_id,
        "client_secret": config.graph_config.client_secret,
        "scope": "https://graph.microsoft.com/.default",
        "grant_type": "client_credentials",
    }
    header = {"ContentType": "application/x-www-form-urlencoded"}
    result: dict = requests.post(
        url=url,
        data=body,
        headers=header,
        timeout=30,
        proxies=https_proxy,
    ).json()
    return result
