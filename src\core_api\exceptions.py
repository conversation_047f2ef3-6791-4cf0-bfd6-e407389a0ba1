"""Exceptions raised by core_api."""


class CoreAPIError(Exception):
    """Represent the base core api exception."""

    def __init__(self, msg: str, status_code: int = 500):
        self.msg = msg
        self.status_code = status_code
        super().__init__(msg)


class NoFiltersProvidedError(CoreAPIError):
    """Represent the exception in case no valid filters were provided to an endpoint."""
