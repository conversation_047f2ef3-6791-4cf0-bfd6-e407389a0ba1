"""Update packages entry point."""

import tomllib
from pathlib import Path

from a2110_olympus._utils import get_services


def main() -> None:
    """Update packages in pyproject.toml.

    Notes
    -----
    We have to manually modify the toml's string content as tomllib does not include a dump method.
    If this is too much, we could use a third-party toml library that does support dumps.
    """
    src_path = Path(__file__).parent.parent
    services = get_services(src_path)

    toml_path = src_path.parent / "pyproject.toml"
    original_toml_text = toml_path.read_text()
    toml_loaded = tomllib.loads(original_toml_text)
    packages = toml_loaded["tool"]["poetry"]["packages"]
    package_includes = [package["include"] for package in packages]
    missing_services = [service for service in services if service not in package_includes]
    if not missing_services:
        return

    for service in missing_services:
        package = {"include": service, "from": "src"}
        packages.append(package)

    packages = sorted(packages, key=lambda p: p["include"])
    package_string_list = ["packages = ["]
    for package in packages:
        str_pairs = ", ".join(f'{k} = "{v}"' for k, v in package.items())
        line = "    { " + str_pairs + " },"
        package_string_list.append(line)
    package_string_list.append("]")

    toml_lines = original_toml_text.splitlines()
    for i, line in enumerate(toml_lines):
        if line.strip().startswith("packages = ["):
            package_startline = i
            break
    else:
        raise Exception("Could not find a line starting with packages = [")

    new_toml_lines = toml_lines[:]
    for package_line in toml_lines[package_startline:]:
        new_toml_lines.pop(package_startline)
        if package_line.strip().endswith("]"):
            break
    else:
        raise Exception("Could not find a line ending with ]")

    r = reversed(package_string_list)

    for line in r:
        new_toml_lines.insert(package_startline, line)

    toml_path.write_text("\n".join(new_toml_lines) + "\n")


if __name__ == "__main__":
    main()
