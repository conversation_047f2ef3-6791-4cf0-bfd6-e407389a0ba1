"""Config module for mon-ca-spectrum."""

from dataclasses import dataclass

from olympus_common.config import DatabaseKafkaConsumerServiceConfig
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseKafkaConsumerServiceConfig, metaclass=Singleton):
    """Represent the configuration for the CA Spectrum DD.

    We do not write outputs since some invalid utf-8 characters were found:
    bsnAPMacAddrTrapVariable = ,3Š
    """

    write_outputs: bool = False


config = Config()  # Initialize the singleton at import time.
