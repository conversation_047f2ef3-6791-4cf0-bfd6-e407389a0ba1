"""Validation functions for the sap-air-client."""

import json
import logging
from http import H<PERSON><PERSON>tatus
from typing import <PERSON><PERSON>

from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from olympus_common.db import Alarm, AlarmIncident, AlarmRelease
from olympus_common.enums import ReleaseStatus
from sap_air_client.api_service.incident import odata_incident
from sap_air_client.models import AlarmReleaseLink, SapDataToUpdate, SapEntityIncident


def validate_alarm_exists(
    alarm_id: int, session: Session, keyword: str
) -> tuple[bool, Optional[JSONResponse], Optional[Alarm]]:
    """Validate that the alarm ID exists and return the alarm object.

    Args:
        alarm_id: The alarm ID to validate
        session: The database session
        keyword: The keyword to use in the error message

    Returns
    -------
        Tuple containing:
        - Boolean indicating if validation passed
        - JSONResponse with error details if validation failed, None otherwise
        - Alarm object if validation passed, None otherwise
    """
    alarm = Alarm.get_alarm_by_id(session, alarm_id)
    if not alarm:
        error_msg = f"{keyword} - Failed: Alarm ID {alarm_id} not found."
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.NOT_FOUND.value, content={"error": error_msg}), None

    return True, None, alarm


def validate_no_incident_link(alarm_id: int, session: Session) -> tuple[bool, Optional[JSONResponse]]:
    """Validate that the alarm is not already linked to an incident.

    Args:
        alarm_id: The alarm ID to validate
        session: The database session

    Returns
    -------
        Tuple containing:
        - Boolean indicating if validation passed
        - JSONResponse with error details if validation failed, None otherwise
    """
    if AlarmIncident.is_alarm_linked_to_incident(alarm_id, session):
        error_msg = f"link_release - Failed: Alarm is already linked to an incident {alarm_id}."
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.CONFLICT.value, content={"error": error_msg})

    return True, None


def validate_release_ids(
    release_number: int, external_release_id: Optional[str]
) -> tuple[bool, Optional[JSONResponse]]:
    """Validate release number and external release ID constraints.

    Args:
        release_number: The release number to validate
        external_release_id: The external release ID to validate

    Returns
    -------
        Tuple containing:
        - Boolean indicating if validation passed
        - JSONResponse with error details if validation failed, None otherwise
    """
    if not release_number and not external_release_id:
        error_msg = "link_release - Failed: The release ID and the external release ID cannot both be empty."
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg})

    if release_number and external_release_id:
        error_msg = "link_release - Failed: The release ID and the external release ID cannot both be non-empty."
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg})

    return True, None


def validate_release_exists(status_code: int, release_number: int) -> tuple[bool, Optional[JSONResponse]]:
    """Validate that the release exists in SAP.

    Args:
        status_code: The status code from the SAP API call
        release_number: The release number for error messages

    Returns
    -------
        Tuple containing:
        - Boolean indicating if validation passed
        - JSONResponse with error details if validation failed, None otherwise
    """
    if status_code not in [HTTPStatus.OK.value, HTTPStatus.CREATED.value]:
        error_msg = f"link_release - Failed: Release ID {release_number} is missing."
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.NOT_FOUND.value, content={"error": error_msg})

    return True, None


def validate_release_status(release_data: dict, release_number: int) -> tuple[bool, Optional[JSONResponse]]:
    """Validate that the release has the correct status.

    Args:
        release_data: The release data from SAP
        release_number: The release number for error messages

    Returns
    -------
        Tuple containing:
        - Boolean indicating if validation passed
        - JSONResponse with error details if validation failed, None otherwise
    """
    if release_data["UserStatus"] not in [ReleaseStatus.APPROVED.value, ReleaseStatus.IN_EXECUTION.value]:
        error_msg = (
            f"link_release - Failed: The release ID {release_number} has a status "
            f"{release_data['UserStatusText']}, \n it has to be Approved or "
            "In Execution status before linking it to an alarm."
        )
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg})

    return True, None


def validate_create_incident_params(entity: SapEntityIncident) -> tuple[bool, Optional[JSONResponse], int]:
    """Validate the parameters for creating an incident.

    Args:
        entity: The entity containing the incident data

    Returns
    -------
        Tuple containing:
        - Boolean indicating if validation passed
        - JSONResponse with error details if validation failed, None otherwise
    """
    if not entity.alarm_id:
        error_msg = "create_incident - Failed: Empty alarm ID"
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg}), 0

    if not entity.main_service or entity.main_service == "null":
        error_msg = (
            "create_incident - Failed: Aucun service défini dans UCMDB, "
            "veuillez créer un sous-ordre pour Compliancy T311C.\n"
            "Er is geen service gedefinieerd in UCMDB, gelieve een suborder te maken voor Compliancy T311C"
        )
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg}), 0

    if not entity.main_sub_service or entity.main_sub_service == "null":
        error_msg = (
            "create_incident - Failed: Aucun sub-service défini dans UCMDB, "
            "veuillez créer un sous-ordre pour Compliancy T311C.\n"
            "Er is geen subservice gedefinieerd in UCMDB, gelieve een suborder te maken voor Compliancy T311C."
        )
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg}), 0

    return True, None, int(entity.alarm_id)


async def validate_release_main_order(qmnum: str) -> tuple[bool, Optional[JSONResponse]]:
    """Release the main order of an incident.

    Args:
        qmnum: The SAP incident ID

    Returns
    -------
        Tuple containing:
        - Boolean indicating if operation succeeded
        - JSONResponse with error details if operation failed, None otherwise
    """
    release_main_order_status, sap_response = await odata_incident.release_main_order_incident(int(qmnum))
    if release_main_order_status != HTTPStatus.NO_CONTENT.value:  # 204
        error_main_order_response: dict = json.loads(sap_response)
        error_main_order_dict: dict = error_main_order_response.get("error", {})
        error_message: dict = error_main_order_dict.get("message", {})

        if error_message.get("value"):
            error_msg = f"create_incident - Failed: {release_main_order_status}: {error_message['value']}"
            logging.error(error_msg)
            return False, JSONResponse(status_code=release_main_order_status, content={"error": error_msg})

    return True, None


def validate_unlink_incident(entity: SapDataToUpdate) -> tuple[bool, Optional[JSONResponse], int]:
    """Validate the parameters for unlinking an incident.

    Args:
        entity: The entity containing the incident data

    Returns
    -------
        Tuple containing:
        - Boolean indicating if validation passed
        - JSONResponse with error details if validation failed, None otherwise
    """
    if not entity.alarm_id:
        error_msg = "unlink incident - Failed: - Empty alarm ID."
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg}), 0

    alarm_id = int(entity.alarm_id)

    if not entity.qmnum:
        error_msg = f"unlink incident - Failed: - Missing SAP incident ID on sap_id {entity.qmnum}"
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg}), 0

    return True, None, alarm_id


def validate_unlink_release(
    entity: AlarmReleaseLink, session: Session, alarm_id: int
) -> tuple[bool, Optional[JSONResponse], Optional[AlarmRelease]]:
    """Validate the parameters for unlinking a release.

    Args:
        entity: The entity containing the release data

    Returns
    -------
        Tuple containing:
        - Boolean indicating if validation passed
        - JSONResponse with error details if validation failed, None otherwise
    """
    if not entity.release_number and not entity.external_release_id:
        error_msg = "unlink_release - Failed: The release number and the external release number cannot both be empty."
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg}), None

    if entity.release_number and entity.external_release_id:
        error_msg = (
            "unlink_release - Failed: The release number and the external release number cannot both be non-empty."
        )
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.BAD_REQUEST.value, content={"error": error_msg}), None

    if not (
        alarm_release := AlarmRelease.get_active_release(
            alarm_id, entity.release_number, entity.external_release_id, session
        )
    ):
        error_msg = f"unlink_release - Failed: Alarm is not linked to the release on alarm_id {alarm_id}."
        logging.error(error_msg)
        return False, JSONResponse(status_code=HTTPStatus.NOT_FOUND.value, content={"error": error_msg}), None

    return True, None, alarm_release
