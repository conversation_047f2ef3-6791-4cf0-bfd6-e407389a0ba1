"""Module to provide logging utilities to an application."""

import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any

from olympus_common.utils import mb_to_bytes

LOG_FORMAT_FILE = (
    '%(asctime)s - %(levelname)s - %(name)s - File "%(pathname)s:%(lineno)d", function = %(funcName)s\n%(message)s'
)
LOG_FORMAT_CONSOLE = (
    '%(asctime)s - %(levelname)s - %(name)s - File "%(pathname)s", line %(lineno)d, function = %(funcName)s'
    "\n%(message)s"
)
MAX_FILE_SIZE_MB = 200
MAX_FILE_SIZE = mb_to_bytes(MAX_FILE_SIZE_MB)


@dataclass
class Logger:
    """Represent a logger which truncates log files if logging to a file is enabled.

    Parameters
    ----------
    filename : str | None, optional
        The name of the logfile.
        If this is a string, log to the given file and use `LOG_FORMAT_FILE`.
        If this is None, log to console and use `LOG_FORMAT_CONSOLE`.
        By default "olympus.log".
    level : int, optional
        Adjust the level of the root logger to the provided value, by default logging.INFO.
    logformat : str | None, optional
        Override the logger's `format`.
        if this is not provided one of two things can happen:
            - If filename is truthy use `LOG_FORMAT_FILE`
            - Else use `LOG_FORMAT_CONSOLE`
        by default None
    folder : Path, optional
        The parent folder in which the logfile will be created.
        Not used in case filename is None, by default `Path.cwd()`
    max_size : int, optional
        The max_size (in bytes) of the logfile before it is emptied, by default `MAX_FILE_SIZE` (200M)
    """

    filename: str | None = "olympus.log"
    level: int = logging.INFO
    logformat: str | None = None
    folder: Path = field(default_factory=Path.cwd)
    max_size: int = MAX_FILE_SIZE

    def setup(self, logger_name: str | None = None):
        """Set up logging based on the current Logger instance.

        If a filename is set (default), logs are sent to self.folder/self.filename with a `TruncatingFileHandler` and
        `LOG_FORMAT_FILE`.
        Otherwise logs are sent to console with `LOG_FORMAT_CONSOLE`.

        Arguments
        ---------
        logger_name : str | None, optional
            The name of the logger to setup. If this is None, the root logger is configured, by default None.
        """
        filename = self.folder / self.filename if self.filename else None
        if not self.logformat:
            self.logformat = LOG_FORMAT_FILE if filename else LOG_FORMAT_CONSOLE

        logger = logging.getLogger(logger_name)
        logger.setLevel(self.level)

        if filename:
            # We will log to a file, not console. Create a file handler with log file truncation.
            filename.parent.mkdir(exist_ok=True, parents=True)
            handler: logging.Handler = TruncatingFileHandler(filename, max_size=self.max_size)
        else:
            # We will log to console, create a default StreamHandler.
            handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter(self.logformat))
        logger.addHandler(handler)


class TruncatingFileHandler(logging.FileHandler):
    """Represent a FileHandler that truncates (empties) the log when it exceeds self.max_size.

    Initialization of this class is the same as `logging.FileHandler` with the addition of a max_size keyword.

    Parameters
    ----------
    *args : Any
        Arguments directly passed to `super().__init__`
    max_size : int
        The treshold in bytes used to decide if the logfile should be emptied. By default `MAX_FILE_SIZE` (200M).
    **kwargs: Any
        Keyword arguments directly passed to `super().__init__`
    """

    def __init__(self, *args: Any, max_size: int = MAX_FILE_SIZE, **kwargs: Any) -> None:
        self.max_size = max_size
        super().__init__(*args, **kwargs)

    def emit(self, record: logging.LogRecord) -> None:
        """Emit the current record.

        Truncate the file if it exists and exceeds the given max_size.

        Parameters
        ----------
        record : logging.LogRecord
            The log-record that should be emitted.
        """
        path = Path(self.baseFilename)
        if path.exists() and path.stat().st_size > self.max_size:
            path.write_bytes(b"")
        super().emit(record)


def get_level_name(level: int | str):
    """Return the name of the logging level.

    We uppercase the input string and try to convert it to an int to support both int and string input.
    """
    if isinstance(level, str):
        level = level.upper()
        try:
            level = int(level)
        except ValueError:
            """Not an int, so we assume 'INFO', 'DEBUG', etc."""
    return logging.getLevelName(level)
