"""Custom icinga_alarm module for icinga-events."""

import json
from dataclasses import dataclass
from typing import Any, Optional, Self

from sqlalchemy.orm import Mapped

from olympus_common.db import Agent, Alarm, AlarmJob, Enrichment, Incident, Occurrence, get_alarm_field
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import AlarmType, IcingaServiceStatus, MeasureType, Severity
from olympus_common.icinga import compute_host_group, compute_service_group, encode_string
from olympus_common.utils import datetime_from_utc_to_local


@dataclass
class CustomIcingaAlarm(Alarm):
    """Represent an enriched alarm.

    Some properties are added here for convenience, they are specific to Icinga.
    """

    _timestamp_format = "%Y-%m-%d %H:%M:%S"

    @classmethod
    def from_dict(cls, orig_dict: dict[str, Any], **kwargs) -> Self:
        """Construct a class instance from the provided orig_dict, overridden with the provided kwargs.

        kwargs are used to override (or add) certain fields to the BackendTable. This can be useful if for example the
        provided orig_dict does not include the scope_ref field. We could then call the function in the following
        manner: BackendTable.from_dict(mydict, scope_ref="foo"), which will return an BackendTable with scope_ref="foo",
        if the scope_ref="foo" was missing and the orig_dict did not include the scope_ref key, this method would error
        because it is missing a required value.

        Columns which are not present on the BackendTable object, but are present in the provided orig_dict will be
        silently dropped.

        Columns which are present on BackendTable, but not present in the provided orig_dict or in the provided kwargs
        will raise an error upon initializing the BackendTable.

        Notes
        -----
        The provided kwargs will overwrite values in the orig_dict. This means that if your orig_dict has a scope_ref
        key, but you pass scope_ref as a keyword argument, the passed keyword argument's value will be used instead of
        the orig_dict's value.

        This method can also be useful if you have a pd.Series. You could then call this method like so:
        BackendTable.from_dict(row.as_dict(), extra_kwarg="foo")

        The type of cls and return-type of this method are the generic type TObject to allow correct type inference
        for subclasses.
        """
        filtered_row_dict = {key: value for key, value in orig_dict.items() if key in cls.columns()}
        for column, value in filtered_row_dict.items():
            column_type = cls.column_types().get(column)
            if column_type == Mapped[Optional[dict[str, Any]]] and type(value) is str:
                # For JSON columns, the received value is serialized, so we de-serialize it.
                filtered_row_dict[column] = json.loads(value) if value else None
            elif column_type in [Mapped["Enrichment"], Mapped[Optional["Enrichment"]]]:
                filtered_row_dict[column] = Enrichment.from_dict(value) if value else None
            elif column_type in [Mapped["Occurrence"], Mapped[Optional["Occurrence"]]]:
                filtered_row_dict[column] = Occurrence.from_dict(value) if value else None
            elif column_type in [Mapped["Agent"], Mapped[Optional["Agent"]]]:
                filtered_row_dict[column] = Agent.from_dict(value) if value else None
            elif column_type in [Mapped["AlarmJob"], Mapped[Optional["AlarmJob"]]]:
                filtered_row_dict[column] = AlarmJob.from_dict(value) if value else None
            elif column_type in [Mapped["Incident"], Mapped[Optional["Incident"]]]:
                filtered_row_dict[column] = Incident.from_dict(value) if value else None
        combined = {**filtered_row_dict, **kwargs}
        return cls(**combined)

    @property
    def raised_at(self) -> str:
        """Return the datetime in string when this alarm was raised.

        The current data is an epoch timestamp expressed as a string.
        In this property, we convert that to a python string containing a datetime.
        """
        if self.first_raise_time:
            return datetime_from_utc_to_local(self.first_raise_time).strftime(self._timestamp_format)

        return ""

    @property
    def cleared_at(self) -> str:
        """Return the datetime in string when this alarm was cleared.

        The current data is an epoch timestamp expressed as a string.
        In this property, we convert that to a python string containing a datetime.
        """
        if self.last_occurrence.clear_time:
            return datetime_from_utc_to_local(self.last_occurrence.clear_time).strftime(self._timestamp_format)

        return ""

    @property
    def severity(self) -> int:
        """Return the alarm's severity.

        The info obtained through enrichment takes precedence over the base info.
        """
        return get_alarm_field(self, "severity")

    @property
    def status(self) -> int:
        """Return the status as it should be represented in Icinga.

        Severity -> Icinga exit_status:
        0 -> OK (0)
        1 -> UNKNOWN (3)
        2, 3 or 4 -> WARNING (1)
        5 -> CRITICAL (2)
        """
        if not self.is_active or self.last_occurrence.event_type == AlarmType.RESOLUTION.value:
            return IcingaServiceStatus.OK.value
        elif self.severity == Severity.INDETERMINATE.value or self.severity not in [
            Severity.WARNING.value,
            Severity.MINOR.value,
            Severity.MAJOR.value,
            Severity.CRITICAL.value,
        ]:
            return IcingaServiceStatus.UNKNOWN.value
        elif self.severity in [Severity.WARNING.value, Severity.MINOR.value, Severity.MAJOR.value]:
            return IcingaServiceStatus.WARNING.value
        else:
            return IcingaServiceStatus.CRITICAL.value

    @property
    def last_occurred_at(self) -> str:
        """Return the datetime in string when this alarm last occurred."""
        if self.last_problem and self.last_problem.raise_time:
            return datetime_from_utc_to_local(self.last_problem.raise_time).strftime(self._timestamp_format)
        elif self.last_occurrence.raise_time:
            return datetime_from_utc_to_local(self.last_occurrence.raise_time).strftime(self._timestamp_format)
        return ""

    @property
    def hostname(self) -> str:
        """Return the hostname of this alarm."""
        if enr := self.last_enrichment:
            return enr.floc_id or enr.identification or self.ci_id or ""
        else:
            return self.ci_id or ""

    @property
    def encoded_hostname(self) -> str:
        """Return the URL-encoded hostname of this alarm."""
        return encode_string(self.hostname)

    @property
    def servicename(self) -> str:
        """Return the servicename of this alarm."""
        service_name = f"{self.hostname}!{self.metric_type}{self.metric_name}"
        if self.identifier_hash:
            return f"{service_name}/{self.identifier_hash}"
        return service_name

    @property
    def encoded_servicename(self) -> str:
        """Return the URL-encoded servicename of this alarm."""
        return encode_string(self.servicename)

    @property
    def additional_info(self) -> str:
        """Return the additional info of this alarm."""
        if self.last_problem and self.last_problem.additional_data:
            return json.dumps(self.last_problem.additional_data)

        return ""

    @property
    def identification(self) -> str:
        """Return the CI's identification."""
        if self.last_enrichment:
            return self.last_enrichment.identification or self.ci_id or ""
        else:
            return self.ci_id or ""

    @property
    def service_display_name(self) -> str:
        """Return the service display name."""
        return self.metric_name or ""

    @property
    def host_display_name(self) -> str:
        """Return the host display name."""
        return self.identification

    @property
    def host_groups(self) -> list[str]:
        """Return the host groups."""
        if self.last_enrichment:
            return compute_host_group(ci_type=self.last_enrichment.ci_type, floc_class=self.last_enrichment.floc_class)

        return []

    @property
    def service_groups(self) -> list[str]:
        """Return the service groups."""
        return compute_service_group(
            source=get_alarm_field(self, "source"), action_class=get_alarm_field(self, "action_class")
        )

    @property
    def air_details(self) -> dict:
        """Return the details of the linked incident."""
        return {"EventID": "", "ExecutionID": "", "Link": "", "Status": "", "error": ""}

    @property
    def sap_details(self) -> dict:
        """Return the details of the linked incident."""
        return {"Qmnum": "", "StatOrdUsrSeqTxt": "", "TTMainAlarm": ""}

    @property
    def previous_sap_details(self) -> dict:
        """Return the previous details of the linked incident."""
        if self.is_active:
            return {}

        if self.active_incident:
            return {
                "IncidentId": self.active_incident.sap_id,
                "SapIncidentStatus": self.active_incident.sap_status,
            }
        elif self.active_release:
            return {
                "ReleaseId": self.active_release.sap_id,
                "SapReleaseStatus": self.active_release.sap_status,
            }

        return {}

    @property
    def clean_summary(self) -> str:
        """Return a clean summary for icinga."""
        if self.last_problem and self.last_problem.summary:
            escaped_string = self.last_problem.summary
            for character_to_escape in ["\\", "&", "%", "{", "}", "'", "`", "/", '"']:
                if character_to_escape in escaped_string:
                    escaped_string = escaped_string.replace(character_to_escape, f"\\{character_to_escape}")
            escaped_string = escaped_string.replace("$", "")
            return escaped_string
        else:
            return ""

    @CaptureSpan(span_type=MeasureType.CUSTOM.value)
    def details(self, needed_keys: list[str]) -> dict:
        """Return the details about the alarm."""
        alarm_keys = dir(self)
        if self.last_enrichment:
            alarm_keys.extend(f"last_enrichment.{col}" for col in self.last_enrichment.columns())
        if self.agent:
            alarm_keys.extend(f"agent.{col}" for col in self.agent.columns())
        if self.last_problem:
            alarm_keys.extend(f"last_problem.{col}" for col in self.last_problem.columns())

        alarm_dict: dict[str, Any] = {}

        for key in needed_keys:
            if key not in alarm_keys:
                continue

            if key == "last_enrichment.instructions":
                if self.last_enrichment:
                    doc_ = getattr(self.last_enrichment, "instructions", "N/A")
                    alarm_dict["vars.instructions"] = doc_
                    alarm_dict["notes"] = f"<br>Documentation: {doc_}"
            elif key in ["action_class"]:
                alarm_dict[f"vars.{key}"] = get_alarm_field(self, key)
            elif key == "clean_summary":
                alarm_dict["vars.summary"] = getattr(self, key, "")
            elif key == "top_level":
                field_value: str = get_alarm_field(self, key)
                alarm_dict[f"vars.{key}"] = field_value.split(",") if field_value else []
            elif key == "linked_environments":
                alarm_dict[f"vars.{key}"] = getattr(self, key, "").split(",") if getattr(self, key, "") else []
            elif key == "last_enrichment.dashboards":
                alarm_dict["vars.dashboardName"] = (
                    getattr(self.last_enrichment, "dashboards", "").split(",")
                    if getattr(self.last_enrichment, "dashboards", "")
                    else []
                )
            elif key == "air_details":
                if not self.is_active:
                    # We only empty the customvar if the alarm becomes inactive.
                    alarm_dict["vars.AIReactivity"] = getattr(self, key, {})
            elif key == "sap_details":
                if not self.is_active:
                    # We only empty the customvar if the alarm becomes inactive.
                    alarm_dict["vars.Sap"] = getattr(self, key, {})
            elif key == "previous_sap_details":
                alarm_dict["vars.PreviousSap"] = getattr(self, key, {})
            elif key == "agent.name":
                alarm_dict["vars.agent"] = getattr(self.agent, "name", "N/A")
            elif key in ["host_display_name", "service_display_name"]:
                alarm_dict["display_name"] = getattr(self, key, "N/A")
            elif key in ["host_groups", "service_groups"]:
                alarm_dict["groups"] = getattr(self, key, "N/A")
            elif key == "id":
                alarm_dict["vars.alarm_id"] = str(getattr(self, key, {}))
            elif "last_enrichment." in key:
                if self.last_enrichment:
                    field_name = key.split(".")[-1]
                    alarm_dict[f"vars.{field_name}"] = getattr(self.last_enrichment, field_name, "N/A")
            elif "last_problem." in key:
                if self.last_problem:
                    field_name = key.split(".")[-1]
                    alarm_dict[f"vars.{field_name}"] = getattr(self.last_problem, field_name, "N/A")
            else:
                alarm_dict[f"vars.{key}"] = getattr(self, key, "N/A")  # Note: getattr also works for properties.
        return alarm_dict
