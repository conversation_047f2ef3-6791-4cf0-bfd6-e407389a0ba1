{"extra_env": {"AGENT_NAME": "HA"}, "agent_names": ["HA"], "data": [{"input": {"ci_id": "A171_SWI", "environment": "ACC", "severity": "warning", "syslog_timestamp": "Feb 12 10:01:51", "@timestamp": "2025-02-12T09:01:50.000Z", "hostname": "arte2-ca11-lp", "tag": "A171", "channel": "OPTIC_HA", "metric": "u00a_core.ExporterAgent.lastActionTimestamp", "@version": "1", "message": "136923771 : This instrument has state WARNING (expired=false,lapsed=false,previous=RIGHT), value >Wed Feb 12 09:54:23 CET 2025< ", "input.type": "log", "host.name": "mon-xa01-lp", "ecs.version": "1.1.0", "agent.id": "3f9e2ff5-fcc3-4770-b7c8-1a1e9c9ff5ba", "agent.type": "filebeat", "agent.hostname": "mon-xa01-lp", "agent.ephemeral_id": "83e938b8-69ec-4eba-9991-b18e6f553bba", "agent.version": "7.5.2", "event.kafka.timestamp": "2025-02-12T09:01:51.259Z", "event.kafka.offset": 68830797, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.created": "2025-02-12T09:01:51.260Z", "event.uuid": "292026d8-5e66-4d46-9f44-380ba6303426", "event.logstash.instance_name": "iictmiapls016", "fields.sender": "A1617", "fields.kafka_topic": "a1617-monitoring_gateway-logs-dev", "fields.env": "DEV", "log.offset": 42504164, "log.original": "Feb 12 10:01:51 arte2-ca11-lp A171: [ID - local6.warning] OPTIC_HA A171_SWI ACC u00a_core.ExporterAgent.lastActionTimestamp 2025/02/12 10:01:50 (2025/02/12 10:01:50) : 136923771 : This instrument has state WARNING (expired=false,lapsed=false,previous=RIGHT), value >Wed Feb 12 09:54:23 CET 2025< ", "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log"}, "output": {"s2110_alarm": [{"ci_id": "A171_SWI", "metric_name": "u00a_core.ExporterAgent.lastActionTimestamp", "metric_type": "/SysLogEvent/", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "A171_SWI", "metric_name": "u00a_core.ExporterAgent.lastActionTimestamp", "metric_type": "/SysLogEvent/", "summary": "136923771 : This instrument has state WARNING (expired=false,lapsed=false,previous=RIGHT), value >Wed Feb 12 09:54:23 CET 2025< ", "clear_time": null, "severity": 3}]}}, {"input": {"ci_id": "HA_A1278_SIGW_BUILD1", "environment": "DEV", "severity": "info", "syslog_timestamp": "Feb 12 10:01:59", "@timestamp": "2025-02-12T09:01:59.000Z", "hostname": "iictyiaplv167", "tag": "j<PERSON>s", "channel": "OPTIC_HA", "metric": "SIGW-BUILD1_ha.heartbeat", "@version": "1", "message": "2380800 : SIGW-BUILD1 heart is beating (1363 beats till now, age is 4d17h30m10s274ms)", "input.type": "log", "host.name": "mon-xa01-lp", "ecs.version": "1.1.0", "agent.id": "3f9e2ff5-fcc3-4770-b7c8-1a1e9c9ff5ba", "agent.type": "filebeat", "agent.hostname": "mon-xa01-lp", "agent.ephemeral_id": "83e938b8-69ec-4eba-9991-b18e6f553bba", "agent.version": "7.5.2", "event.kafka.timestamp": "2025-02-12T09:02:01.285Z", "event.kafka.offset": 68830806, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.created": "2025-02-12T09:02:01.285Z", "event.uuid": "0049d109-07df-4544-a6f0-99d798b13bf4", "event.logstash.instance_name": "iictmiapls016", "fields.sender": "A1617", "fields.kafka_topic": "a1617-monitoring_gateway-logs-dev", "fields.env": "DEV", "log.offset": 42511542, "log.original": "Feb 12 10:01:59 iictyiaplv167 jboss: [ID - local6.info] OPTIC_HA HA_A1278_SIGW_BUILD1 DEV SIGW-BUILD1_ha.heartbeat 2025/02/12 10:01:59 (2025/02/12 10:01:59) : 2380800 : SIGW-BUILD1 heart is beating (1363 beats till now, age is 4d17h30m10s274ms)", "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "HA_A1278_SIGW_BUILD1", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "HA jboss HeartBeat Message", "clear_time": null, "severity": 1, "additional_data": null}]}}, {"input": {"ci_id": "mailgw_hb", "environment": "ACC", "severity": "err", "syslog_timestamp": "Feb 12 10:02:37", "@timestamp": "2025-02-12T09:02:37.000Z", "hostname": "mail-monitoring-gw", "tag": "A1617-ACC-Mailgw", "channel": "OPTIC_python", "metric": "heartbeat", "@version": "1", "message": "heart is beating : email gateway OK - Checked 18457 times - running for 25 days, 20 hours, 8 minutes, 12 seconds", "input.type": "log", "host.name": "mon-xa01-lp", "ecs.version": "1.1.0", "agent.id": "3f9e2ff5-fcc3-4770-b7c8-1a1e9c9ff5ba", "agent.type": "filebeat", "agent.hostname": "mon-xa01-lp", "agent.ephemeral_id": "83e938b8-69ec-4eba-9991-b18e6f553bba", "agent.version": "7.5.2", "event.kafka.timestamp": "2025-02-12T09:02:38.396Z", "event.kafka.offset": 68830872, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.created": "2025-02-12T09:02:38.396Z", "event.uuid": "bb2a670c-aa98-4a47-973d-12522bdc4838", "event.logstash.instance_name": "iictmiapls016", "fields.sender": "A1617", "fields.kafka_topic": "a1617-monitoring_gateway-logs-dev", "fields.env": "DEV", "log.offset": 42565677, "log.original": "Feb 12 10:02:37 mail-monitoring-gw A1617-ACC-Mailgw: [ID - local6.err] OPTIC_python mailgw_hb ACC heartbeat 2025/02/12 10:02:37 : heart is beating : email gateway OK - Checked 18457 times - running for 25 days, 20 hours, 8 minutes, 12 seconds", "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log"}, "output": {}}, {"input": {"ci_id": "SYSLOG_GW_PRODMON_DRP", "environment": "PROD", "severity": "err", "syslog_timestamp": "Feb 12 10:02:55", "@timestamp": "2025-02-12T09:02:55.000Z", "hostname": "mon-xa01-lp.msnet.railb.be", "tag": "A1617-ACC-SyslogHB", "channel": "OPTIC_python", "metric": "Heartbeat", "@version": "1", "message": "Heart is beating for Syslog GW PROD_DRP", "input.type": "log", "host.name": "mon-xa01-lp", "ecs.version": "1.1.0", "agent.id": "3f9e2ff5-fcc3-4770-b7c8-1a1e9c9ff5ba", "agent.type": "filebeat", "agent.hostname": "mon-xa01-lp", "agent.ephemeral_id": "83e938b8-69ec-4eba-9991-b18e6f553bba", "agent.version": "7.5.2", "event.kafka.timestamp": "2025-02-12T09:02:56.518Z", "event.kafka.offset": 68830959, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.created": "2025-02-12T09:02:56.518Z", "event.uuid": "678086e2-3c9f-4f77-99a0-b2f2a7a6fd19", "event.logstash.instance_name": "iictmiapls016", "fields.sender": "A1617", "fields.kafka_topic": "a1617-monitoring_gateway-logs-dev", "fields.env": "DEV", "log.offset": 42644289, "log.original": "Feb 12 10:02:55 mon-xa01-lp.msnet.railb.be A1617-ACC-SyslogHB: [ID - local6.err] OPTIC_python SYSLOG_GW_PRODMON_DRP PROD Heartbeat 2025/02/12 10:02:55 : Heart is beating for Syslog GW PROD_DRP", "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log"}, "output": {}}, {"input": {"ci_id": "A171_SWI", "environment": "ACC", "severity": "info", "syslog_timestamp": "Feb 12 10:04:21", "@timestamp": "2025-02-12T09:04:21.000Z", "hostname": "arte2-ca11-lp", "tag": "A171", "channel": "OPTIC_HA", "metric": "u00a_core.ExporterAgent.lastActionTimestamp", "@version": "1", "message": "136926618 : This instrument has state RIGHT (expired=false,lapsed=false,previous=WARNING), value >Wed Feb 12 10:01:52 CET 2025< ", "input.type": "log", "host.name": "mon-xa01-lp", "ecs.version": "1.1.0", "agent.id": "3f9e2ff5-fcc3-4770-b7c8-1a1e9c9ff5ba", "agent.type": "filebeat", "agent.hostname": "mon-xa01-lp", "agent.ephemeral_id": "83e938b8-69ec-4eba-9991-b18e6f553bba", "agent.version": "7.5.2", "event.kafka.timestamp": "2025-02-12T09:04:25.693Z", "event.kafka.offset": 68831005, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.created": "2025-02-12T09:04:25.693Z", "event.uuid": "8b83609f-9ddc-42d7-ad5c-d791b4c8645b", "event.logstash.instance_name": "iictmiapls016", "fields.sender": "A1617", "fields.kafka_topic": "a1617-monitoring_gateway-logs-dev", "fields.env": "DEV", "log.offset": 42681735, "log.original": "Feb 12 10:04:21 arte2-ca11-lp A171: [ID - local6.info] OPTIC_HA A171_SWI ACC u00a_core.ExporterAgent.lastActionTimestamp 2025/02/12 10:04:21 (2025/02/12 10:04:21) : 136926618 : This instrument has state RIGHT (expired=false,lapsed=false,previous=WARNING), value >Wed Feb 12 10:01:52 CET 2025< ", "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "A171_SWI", "metric_name": "u00a_core.ExporterAgent.lastActionTimestamp", "metric_type": "/SysLogEvent/", "summary": "136926618 : This instrument has state RIGHT (expired=false,lapsed=false,previous=WARNING), value >Wed Feb 12 10:01:52 CET 2025< ", "clear_time": "2025-02-12T09:04:21", "severity": 0, "additional_data": null}]}}, {"input": {"ci_id": "SNMP_GW_DEV", "environment": "ACC", "severity": "err", "syslog_timestamp": "Feb 12 10:04:55", "@timestamp": "2025-02-12T09:04:55.000Z", "hostname": "mon-xa01-lp.msnet.railb.be", "tag": "A1617-ACC-SNMP_HB", "channel": "OPTIC_SNMP", "metric": "Heartbeat", "@version": "1", "message": "Heart is beating", "input.type": "log", "host.name": "mon-xa01-lp", "ecs.version": "1.1.0", "agent.id": "3f9e2ff5-fcc3-4770-b7c8-1a1e9c9ff5ba", "agent.type": "filebeat", "agent.hostname": "mon-xa01-lp", "agent.ephemeral_id": "83e938b8-69ec-4eba-9991-b18e6f553bba", "agent.version": "7.5.2", "event.kafka.timestamp": "2025-02-12T09:04:55.763Z", "event.kafka.offset": 68831020, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.created": "2025-02-12T09:04:55.763Z", "event.uuid": "4470f79d-9ad1-45e6-8230-0466d0c423db", "event.logstash.instance_name": "iictmiapls016", "fields.sender": "A1617", "fields.kafka_topic": "a1617-monitoring_gateway-logs-dev", "fields.env": "DEV", "log.offset": 42693626, "log.original": "Feb 12 10:04:55 mon-xa01-lp.msnet.railb.be A1617-ACC-SNMP_HB: [ID - local6.err] OPTIC_SNMP SNMP_GW_DEV ACC Heartbeat 2025/02/12 10:04:55 : Heart is beating", "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log"}, "output": {}}, {"input": {"ci_id": "A171_SWI", "environment": "ACC", "severity": "info", "syslog_timestamp": "Feb 12 10:14:10", "@timestamp": "2025-02-12T09:14:09.000Z", "hostname": "arte2-ca11-lp", "tag": "arte2", "channel": "OPTIC_HA", "metric": "u00a_ha.heartbeat", "@version": "1", "message": "136938695 : ha-u00a heart is beating (10371 beats till now, age is 36d00h10m15s307ms) ", "input.type": "log", "host.name": "mon-xa01-lp", "ecs.version": "1.1.0", "agent.id": "3f9e2ff5-fcc3-4770-b7c8-1a1e9c9ff5ba", "agent.type": "filebeat", "agent.hostname": "mon-xa01-lp", "agent.ephemeral_id": "83e938b8-69ec-4eba-9991-b18e6f553bba", "agent.version": "7.5.2", "event.kafka.timestamp": "2025-02-12T09:14:10.392Z", "event.kafka.offset": 68832008, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1617-monitoring_gateway-logs-dev", "event.kafka.topic": "a1617-monitoring_gateway-logs-dev", "event.created": "2025-02-12T09:14:10.392Z", "event.uuid": "ca16c2d6-0341-4521-ac99-5e569dc084e7", "event.logstash.instance_name": "iictmiapls016", "fields.sender": "A1617", "fields.kafka_topic": "a1617-monitoring_gateway-logs-dev", "fields.env": "DEV", "log.offset": 43631204, "log.original": "Feb 12 10:14:10 arte2-ca11-lp arte2: [ID - local6.info] OPTIC_HA A171_SWI ACC u00a_ha.heartbeat 2025/02/12 10:14:09 (2025/02/12 10:14:09) : 136938695 : ha-u00a heart is beating (10371 beats till now, age is 36d00h10m15s307ms) ", "log.file.path": "/var/opt/app/a1617/Log/Rsyslog-Local6-ACC.log"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "A171_SWI", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "HA arte2 HeartBeat Message", "clear_time": null, "severity": 1, "additional_data": null}]}}]}