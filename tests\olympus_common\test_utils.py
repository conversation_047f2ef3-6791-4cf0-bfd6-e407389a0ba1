"""Test module related to olympus_common's utils."""

from dataclasses import dataclass

import pytest

from olympus_common.exceptions import OlympusError
from olympus_common.utils import Singleton, classproperty, strtobool


@pytest.mark.parametrize(
    ("val", "result"),
    [
        ("0", False),
        ("false", False),
        ("no", False),
        ("n", False),
        ("off", False),
        ("1", True),
        ("true", True),
        ("yes", True),
        ("y", True),
        ("on", True),
    ],
)
def test_strtobool(val: str, result: bool) -> None:
    """Test that strtobool converts as we would expect it to."""
    assert strtobool(val) == result


def test_strtobool_invalid():
    """Test that strtobool raises a ValueError when an invalid value is provided."""
    with pytest.raises(ValueError, match="Invalid truth value"):
        strtobool("invalid value")


def test_singleton() -> None:
    """Test that a singleton object behaves as intended.

    Notes
    -----
    dataclass is used for simple initialization, it is not required for the test.
    """

    @dataclass
    class Dummy(metaclass=Singleton):
        dummyfield: str

    dummy1 = Dummy(dummyfield="dummystr")
    dummy2 = Dummy()
    assert dummy1 is dummy2  # Ensure that the same object was returned eventhough the second instance has no args.

    # Ensure that re-initializing a Singleton with arguments raises an OlympusError.
    with pytest.raises(OlympusError, match="Dummy is a Singleton object"):
        Dummy(dummyfield="dummystr")

    # Ensure that re-initializing a Singleton with falsey arguments raises an OlympusError.
    with pytest.raises(OlympusError, match="Dummy is a Singleton object"):
        Dummy(None)


def test_classproperty():
    """Test the classproperty decorator."""

    class Foo:
        @classmethod
        @classproperty
        def x(cls):
            return cls.__name__

    class Bar(Foo):
        """Inherit the decorator from foo."""

    assert Foo.x == "Foo"
    assert Foo().x == "Foo"
    assert Bar.x == "Bar"
    assert Bar().x == "Bar"
