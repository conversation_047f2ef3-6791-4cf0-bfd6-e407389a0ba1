"""Model implementation for mon-big-data."""

from pydantic import BaseModel, Field


class Labels(BaseModel):
    """Represents The label fields for the Prometheus Alert."""

    acode: str
    alertname: str
    ci: str
    metricname: str
    metrictype: str
    node: str | None = Field(default=None)
    severity: str


class Annotation(BaseModel):
    """Represents The Annotation fields for the Alert."""

    summary: str


class Alert(BaseModel):
    """Represents The Alert fields for the Prometheus Alert."""

    status: str
    labels: Labels
    annotations: Annotation
    starts_at: str | None = Field(default=None, alias="startsAt")
    ends_at: str | None = Field(default=None, alias="endsAt")


class PrometheusAlert(BaseModel):
    """Represents The Pometheus Alert."""

    alerts: list[Alert]
