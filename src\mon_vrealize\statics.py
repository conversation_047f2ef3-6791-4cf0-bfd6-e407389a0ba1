"""Static variables for the mon_vrealize module."""

HEARTBEAT_DEFINITION_NAME = "Dummy Alert vRops"
TRAP_PROBLEM_ACTIVE = "SNMPv2-SMI::enterprises.6876.4.50.1.0.46"
TRAP_PROBLEM_CLEAR = "SNMPv2-SMI::enterprises.6876.4.50.1.0.47"
TRAP_PROBLEM_CHANGE = "SNMPv2-SMI::enterprises.6876.4.50.1.0.48"
COLUMNS_RENAMING = {
    "snmptrap.SNMPv2-MIB::snmpTrapOID.0": "trap_type",
    "snmptrap.vmwareAlertAliveServerName": "alert_alive_server_name",
    "snmptrap.vmwareAlertType": "alert_type",
    "snmptrap.vmwareAlertSubtype": "alert_subtype",
    "snmptrap.vmwareAlertHealth": "alert_health",
    "snmptrap.vmwareAlertRisk": "alert_risk",
    "snmptrap.vmwareAlertEfficiency": "alert_efficiency",
    "snmptrap.vmwareAlertMetricName": "alert__metric_name",
    "snmptrap.vmwareAlertResourceKind": "alert_resource_kind",
    "snmptrap.vmwareAlertDefinitionName": "alert_definition_name",
    "snmptrap.vmwareAlertDefinitionDesc": "alert_definition_description",
    "snmptrap.vmwareAlertImpact": "alert_impact",
    "snmptrap.vmwareAlertEntityName": "alert_entity_name",
    "snmptrap.vmwAlertNotificationRules": "alert_notification_rules",
    "snmptrap.vmwareAlertEntityType": "alert_entity_type",
    "snmptrap.vmwareAlertTimestamp": "alert_timestamp",
    "snmptrap.vmwareAlertCriticality": "alert_criticality",
    "snmptrap.vmwareAlertRootCause": "alert_root_cause",
    "snmptrap.vmwareAlertURL": "alert_url",
    "snmptrap.vmwareAlertID": "alert_id",
    "snmptrap.vmwareAlertMessage": "alert_message",
}
METRIC_TYPE_DICT = {
    "Host has lost connection to vCenter Server": "/NetworkConnectivity/",
    "Datastore is running out of disk space": "/DiskSpace/",
    "Datastore has lost connectivity to a storage device": "/NetworkConnectivity/",
    "Memory sensors are reporting problems": "/System/MemoryAvailable/",
}
METRIC_NAME_DICT = {
    "Host has lost connection to vCenter Server": "Host2vCenter",
    "Datastore is running out of disk space": "DataStore",
    "Datastore has lost connectivity to a storage device": "Remote_DataStore",
    "Memory sensors are reporting problems": "SensorProblems",
}
ALARMS_FILTER = METRIC_TYPE_DICT.keys()
GLOBAL_DELAY = 900
