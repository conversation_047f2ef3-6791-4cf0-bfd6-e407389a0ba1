"""Utils module for mon-scada."""

import pandas as pd

from olympus_common.utils import strtobool


def is_tunnel_csj(row: pd.Series) -> bool:
    """Return True if the tunnel abbreviation is CSJ."""
    return is_tunnel_abbreviation(row, "CSJ")


def is_tunnel_bns(row: pd.Series) -> bool:
    """Return True if the tunnel abbreviation is BNS."""
    return is_tunnel_abbreviation(row, "BNS")


def is_tunnel_kst(row: pd.Series) -> bool:
    """Return True if the tunnel abbreviation is KST."""
    return is_tunnel_abbreviation(row, "KST")


def is_tunnel_abbreviation(row: pd.Series, abbreviation: str) -> bool:
    """Return True if the tunnel abbreviation is CSJ."""
    return row["tunnel_abbreviation"] == abbreviation


def is_heartbeat(row: pd.Series) -> bool:
    """Return True if the condition for a SCADA heartbeat is met."""
    return row["alarm_id"] == "heartbeat"


def is_active_status(status: str) -> bool:
    """Return True if the status is active."""
    if not strtobool(status):
        return False
    return True
