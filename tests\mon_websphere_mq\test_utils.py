"""Test utils for the mon_websphere_mq package."""

from mon_websphere_mq.utils import convert_to_severity
from olympus_common import enums


def test_convert_to_severity():
    assert convert_to_severity("info") == enums.Severity.CLEARED.value
    assert convert_to_severity("notice") == enums.Severity.WARNING.value
    assert convert_to_severity("warning") == enums.Severity.MINOR.value
    assert convert_to_severity("err") == enums.Severity.MAJOR.value
    assert convert_to_severity("error") == enums.Severity.MAJOR.value
    assert convert_to_severity("crit") == enums.Severity.CRITICAL.value
    assert convert_to_severity("critical") == enums.Severity.CRITICAL.value
    assert convert_to_severity("unknown") == enums.Severity.INDETERMINATE.value
