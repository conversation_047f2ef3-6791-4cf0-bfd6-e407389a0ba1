"""Mailing module for mon-certificates."""

import base64
import logging
from urllib import parse

import requests
from jinja2 import Environment, FileSystemLoader, TemplateError

from mon_certificates.config import config


def get_proxy(user: str | None = None, password: str | None = None, https: bool = False) -> dict:
    """Return the proxy dict as needed for the requests library.

    Parameters
    ----------
    user : str | None, optional
        The user used for the proxy configuration (try to use sys_user), by default None
    password : str | None, optional
        The password of the user, by default None
    https : bool, optional
        The bool argument to choose between http or https protocol, by default False

    Returns
    -------
    dict
        The dict for proxies needed for requests lib.
    """
    if user and password:
        password = parse.quote(password)
        proxy = f"http://{user}:{password}@proxyi.msnet.railb.be:80"
    else:
        proxy = "http://proxyi.msnet.railb.be:80"
    if https:
        return {"https": proxy}
    else:
        return {"http": proxy}


def render_html_cert_expiration(
    template: str,
    days_left: int,
    certificates: list[dict],
    days_left_2: int | None = None,
    certificates_2: list[dict] | None = None,
) -> str:
    """Generate html email from the given data.

    Parameters
    ----------
    template : str
        The template to use.
    days_left : int
        Number of expiry days for the certificates ExpirySeverity.
    certificates : list[dict]
        The certificates that expired soon.
    days_left_2 : int | None, optional
        Number of expiry days for the certificates WarningSeverity, by default None.
    certificates_2 : list[dict] | None, optional
        The certificates with WarningSeverity, by default None.

    Returns
    -------
    str
        The html body of the email.

    Raises
    ------
    TemplateError
        If the template is not found or if the mail body is empty.
    """
    image_string = base64.b64encode(config.image_path.read_bytes()).decode()
    file_loader = FileSystemLoader(config.templates_folder)
    env = Environment(loader=file_loader, autoescape=True)
    _template = env.get_template(template)
    if certificates_2 and days_left_2 is not None:
        mail_body = _template.render(
            header_image=image_string,
            days_left=days_left,
            certificates=certificates,
            days_left_2=days_left_2,
            certificates_2=certificates_2,
        )
    else:
        _template = env.get_template(template)
        mail_body = _template.render(header_image=image_string, days_left=days_left, certificates=certificates)
    if not mail_body:
        raise TemplateError("Something went wrong during the creation of the mail body.")
    return mail_body


def export_mail_text(
    mail_text: str,
    group_name: str,
    days_left: int,
    warning_certs: list | None = None,
    warning_days_left: int | None = None,
) -> None:
    """Create an html file containing the given string.

    This should be use in order to log mail content.

    Parameters
    ----------
    mail_text : str
        The content of the email.
    group_name : str
        The group used to slice the certificates (currently app_code or org_unit).
    days_left : int
        The max number of days left for the severity.
    warning_certs : list | None, optional
        The list containing the certificates for the warning severity (if another severity is present), by default None.
    warning_days_left : int | None, optional
        The max number of days left for the warning severity (if another severity is present), by default None.
    """
    if warning_certs is not None and warning_days_left is not None:
        html_debug_file = config.logger_config.logs_folder / f"test-{group_name}-{days_left}-{warning_days_left}.html"
    else:
        html_debug_file = config.logger_config.logs_folder / f"test-{group_name}-{days_left}.html"
    html_debug_file.write_text(mail_text)


def send_graph_email(
    recipients_: set,
    mail_text: str,
    subject: str,
    sender: str,
    cc_recipients_: set = set(),  # noqa: B006 (a set can only be define like this)
):
    """Send an email with the MS Graph API and save it to the sent items with the category certificate.

    Parameters
    ----------
    recipients_ : set
        list of the email address of the recipients of the email.
    mail_text : str
        Html body of the email.
    subject : str
        Subject of the email.
    sender : str
        email address from which you send the email.
    cc_recipients_ : set
        list of the email address of the CC recipients of the email. Default = set()
    """
    https_proxy = get_proxy(https=True)
    url = f"https://login.microsoftonline.com/{config.tenant_id}/oauth2/v2.0/token"
    body = {
        "client_id": config.client_id,
        "client_secret": config.client_secret,
        "scope": "https://graph.microsoft.com/.default",
        "grant_type": "client_credentials",
    }
    header = {"ContentType": "application/x-www-form-urlencoded"}
    result: dict = requests.post(
        url=url,
        data=body,
        headers=header,
        timeout=30,
        proxies=https_proxy,
    ).json()
    recipients = [{"EmailAddress": {"Address": receiver}} for receiver in recipients_]
    cc_recipients = [{"EmailAddress": {"Address": receiver}} for receiver in cc_recipients_]

    if access_token := result.get("access_token"):
        endpoint = f"https://graph.microsoft.com/v1.0/users/{sender}/sendMail"
        email_msg = {
            "Message": {
                "Subject": subject,
                "Body": {"ContentType": "html", "Content": mail_text},
                "ToRecipients": recipients,
                "ccRecipients": cc_recipients,
                "categories": ["certificate"],
            },
            "SaveToSentItems": config.save_to_sent,
        }
        response = requests.post(
            endpoint,
            headers={"Authorization": "Bearer " + access_token},
            json=email_msg,
            timeout=30,
            proxies=https_proxy,
        )
        if response.ok:
            logging.info("Sent email successfully")
        else:
            logging.info(response.json())
    else:
        logging.info(result.get("error"))
        logging.info(result.get("error_description"))
        logging.info(result.get("correlation_id"))
