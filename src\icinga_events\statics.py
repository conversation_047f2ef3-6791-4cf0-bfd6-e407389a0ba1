"""Static module for icinga-events."""

SERVICE_COLUMNS = [
    "agent.name",
    "cleared_at",
    "last_enrichment.instructions",
    "raised_at",
    "last_occurred_at",
    "metric_type",
    "metric_name",
    "severity",
    "tally",
    "event_url",
    "additional_info",
    "service_display_name",
    "air_details",
    "sap_details",
    "previous_sap_details",
    "clean_summary",
    "service_groups",
    "last_enrichment.subgroups",
    "last_enrichment.source",
    "last_enrichment.metric_category",
    "action_class",
    "last_enrichment.metric_description",
    "last_enrichment.metric_short_name",
    "last_enrichment.main_service",
    "last_enrichment.main_sub_service",
    "id",
]

HOST_COLUMNS = [
    "last_enrichment.address",
    "last_enrichment.brand",
    "last_enrichment.critical_ci",
    "identification",
    "last_enrichment.floc_id",
    "last_enrichment.ip_address",
    "last_enrichment.location_address",
    "last_enrichment.location_attribute",
    "last_enrichment.location_type",
    "top_level",
    "linked_environments",
    "last_enrichment.dashboards",
    "host_display_name",
    "host_groups",
    "last_enrichment.location_category",
    "last_enrichment.ci_uuid",
    "last_enrichment.ci_type",
    "last_enrichment.floc_class",
    "last_enrichment.model",
]
