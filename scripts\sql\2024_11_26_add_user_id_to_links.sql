ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_release
    ADD COLUMN link_user_id text;
	
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_release
    ADD COLUMN unlink_user_id text;
	
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_incident
    ADD COLUMN link_user_id text;
	
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_incident
    ADD COLUMN unlink_user_id text;
	
ALTER TABLE IF EXISTS #{Schema}#.s2110_incident
    ADD COLUMN user_id text;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_incident
    ADD COLUMN unlink_time timestamp without time zone;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_release
    ADD COLUMN unlink_time timestamp without time zone;
    
CREATE OR REPLACE VIEW #{Schema}#.s2110_detailed_alarm_vw
 AS
SELECT al.id AS alarm_id
      , ag.name AS agent_name
      , al.ci_id
      , al.metric_type
      , al.metric_name
      , al.first_raise_time
      , e.floc_id
      , al.is_active
      , aj_enrichment.job_status AS enrichment_status
      , aj_ui_sending.job_status AS ui_sending_status
      , r.sap_id AS sap_release_id
      , CASE WHEN al.last_clear_id = al.last_occurrence_id THEN true ELSE false END AS is_cleared
      , al.node
      , COALESCE(e.severity, last_problem.severity) AS severity
      , e.location_attribute AS location_short
      , e.location_type
      , COALESCE(e.action_class, al.action_class) AS action_class
      , COALESCE(e.top_level, al.top_level) AS top_level
      , last_problem.summary
	  , ceiling(extract(epoch from (al.closing_time - al.first_raise_time)) / 60) AS duration_minutes
	  , al.closing_time - al.first_raise_time AS duration
	  , NULL AS time_to_acknowledge_minutes
	  , NULL AS time_to_acknowledge
	  , CASE 
	      WHEN last_clear.clear_time IS NOT NULL THEN GREATEST(ceiling(extract(epoch from (last_clear.clear_time - al.first_raise_time)) / 60), 0)
		  ELSE NULL
		END AS time_to_clear_minutes
	  , CASE 
	      WHEN last_clear.clear_time IS NOT NULL AND last_clear.clear_time > al.first_raise_time THEN last_clear.clear_time - al.first_raise_time ELSE NULL END AS time_to_clear
	  , i.sap_id AS sap_incident_id
	  , i.user_id AS incident_created_by
	  , ai.link_time AS incident_link_time
	  , NULL AS incident_closed_by
	  , NULL AS incident_close_time
	  , NULL AS acknowledged_by
	  , ar.link_time AS release_link_time
	  , COALESCE(e.actionable, al.actionable) AS actionable
	  , e.ci_type AS ci_type
	  , NULL AS is_acknowledged
	  , e.location_address
	  , NULL AS comments
	  , last_problem.raise_time AS last_occurrence_time
	  , last_clear.clear_time
	  , e.critical_ci
	  , e.instructions
   FROM s2110_alarm al
   JOIN s2110_agent ag
     ON al.agent_id = ag.id
   JOIN s2110_alarm_job aj_enrichment 
     ON al.id = aj_enrichment.alarm_id AND aj_enrichment.job_name = 'enrichment'
   JOIN s2110_alarm_job aj_ui_sending 
     ON al.id = aj_ui_sending.alarm_id AND aj_ui_sending.job_name = 'ui_sending'
   LEFT JOIN s2110_alarm_enrichment ae 
     ON al.id = ae.alarm_id AND ae.last_enrichment
   LEFT JOIN s2110_enrichment e 
     ON ae.enrichment_id = e.id
   LEFT JOIN s2110_occurrence last_problem 
     ON al.last_problem_id = last_problem.id
   LEFT JOIN s2110_occurrence last_clear 
     ON al.last_clear_id = last_clear.id
   LEFT JOIN s2110_alarm_incident ai 
     ON al.id = ai.alarm_id AND ai.is_link_active
   LEFT JOIN s2110_incident i
     ON ai.incident_id = i.id
   LEFT JOIN s2110_alarm_release ar 
     ON al.id = ar.alarm_id AND ar.is_link_active
   LEFT JOIN s2110_release r 
     ON ar.release_id = r.id;
     
ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_action DROP COLUMN IF EXISTS type;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_action DROP COLUMN IF EXISTS creation_time;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_action DROP COLUMN IF EXISTS comment;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_action DROP COLUMN IF EXISTS author;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_action
    ADD COLUMN action_id bigint;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm_action
    ADD COLUMN is_first_ack boolean;
    
CREATE SEQUENCE #{Schema}#.seq_s2110_user_action
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;
	
	
CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_user_action
(
    id bigint NOT NULL DEFAULT nextval('seq_s2110_user_action'::regclass),
    action_type text COLLATE pg_catalog."default",
	creation_time timestamp without time zone,
    comment text COLLATE pg_catalog."default",
    user_id text COLLATE pg_catalog."default",
    CONSTRAINT s2110_user_action_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

ALTER TABLE #{Schema}#.s2110_alarm_action ADD CONSTRAINT fk_alarm_id FOREIGN KEY (alarm_id) REFERENCES s2110_alarm (id);
ALTER TABLE #{Schema}#.s2110_alarm_action ADD CONSTRAINT fk_release_id FOREIGN KEY (action_id) REFERENCES s2110_user_action (id);

CREATE INDEX IF NOT EXISTS s2110_idx_fk_alarm_action_to_alarm
    ON #{Schema}#.s2110_alarm_action USING btree
    (alarm_id ASC)
    TABLESPACE s2110_index;

CREATE INDEX IF NOT EXISTS s2110_idx_fk_alarm_action_to_user_action
    ON #{Schema}#.s2110_alarm_action USING btree
    (action_id ASC)
    TABLESPACE s2110_index;