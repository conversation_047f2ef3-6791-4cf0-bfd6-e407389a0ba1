"""Update launch entrypoint."""

import json
from pathlib import Path

from a2110_olympus._utils import get_services


def main() -> None:
    """Update .vscode/launch.json with all services."""
    src_folder = Path(__file__).parent.parent
    services = get_services(src_folder)

    service_configurations = []
    for service in services:
        service_configuration = {
            "name": service,
            "type": "debugpy",
            "request": "launch",
            "module": "a2110_olympus.run",
            "args": [service],
        }
        service_configurations.append(service_configuration)

    launch_path = Path(__file__).parent.parent.parent / ".vscode" / "launch.json"
    launch_json = json.loads(launch_path.read_text())
    untouched_configurations = [config for config in launch_json["configurations"] if config["name"] not in services]
    new_configurations = service_configurations + untouched_configurations
    launch_json["configurations"] = new_configurations
    launch_path.write_text(json.dumps(launch_json, indent=2))


if __name__ == "__main__":
    main()
