"""Module to interact with SAP OData Releases API."""

import datetime as dt
from dataclasses import dataclass, field
from typing import Any

import urllib3
from requests import Response, Session

from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType
from sap_air_client.config import config
from sap_air_client.statics import EXPAND_DATA_RELEASE, ODATA_HEADER
from sap_air_client.utils import handle_sap_request

urllib3.disable_warnings()


@dataclass
class Release:
    """Represent the Object to interact with SAP Releases endpoint."""

    release_set: str = "ReleaseSet"
    endpoint: str = config.sap.release_endpoint
    release_path: str = str(endpoint) + str(release_set)
    expand: list[str] = field(default_factory=lambda: EXPAND_DATA_RELEASE)
    class_name: str = "Sap"
    token_expiration: dt.datetime = dt.datetime.now()

    session: Session = Session()
    session.verify = False
    session.auth = (config.sap.user, config.sap.password)
    session.headers.update(ODATA_HEADER)

    @property
    def get_token(self) -> str:
        """Get token from SAP endpoint."""
        response: Response = self.session.get(url=config.sap.release_endpoint, headers=ODATA_HEADER)
        return response.headers["x-csrf-token"]

    def _login(self) -> None:
        """Add token in the header if it is not yet present."""
        if self.token_expiration <= dt.datetime.now():
            self.session.headers["x-csrf-token"] = self.get_token
            self.token_expiration = dt.datetime.now() + dt.timedelta(minutes=25)

    @trace_scan(MeasureType.REQUEST.value)
    def _request(self, method: str, *args, **kwargs) -> Response:
        """Send a request to SAP endpoint."""
        self._login()
        return self.session.request(method, *args, **kwargs)

    @trace_scan(MeasureType.REQUEST.value)
    def _get_url(self, release_number: int) -> str:
        """Get the url for the release number."""
        url = f"{self.release_path}('{release_number}')" + (f"?$expand={','.join(self.expand)}" if self.expand else "")
        return url

    @trace_scan(MeasureType.REQUEST.value)
    @handle_sap_request
    def get_release(self, release_number: int) -> Response:
        """Get the release from SAP endpoint."""
        return self._request("get", self._get_url(release_number))

    def get_release_less_details(self, release_number: int) -> dict[str, Any]:
        """Get the release from SAP endpoint."""
        request = self._request("get", self._get_url(release_number))
        response_data = request.json()["d"]
        return response_data


odata_release = Release()
