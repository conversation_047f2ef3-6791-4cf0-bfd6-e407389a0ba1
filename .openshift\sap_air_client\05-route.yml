---
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  labels:
    app: sap-air-client-app-#{appEnv}#
  name: a2110-sap-air-client-route-#{appEnv}#
  namespace: a2110-olympus-monitoring
spec:
  host: a2110-sap-air-client-#{appEnv}#.#{openshiftEnv}#
  to:
    kind: Service
    name: a2110-sap-air-client-service-#{appEnv}#
    weight: 100
  port:
    targetPort: http
  wildcardPolicy: None
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
