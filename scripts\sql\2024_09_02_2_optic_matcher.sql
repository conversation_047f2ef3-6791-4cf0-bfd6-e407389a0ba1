CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_optic_alarm_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_optic_alarm_id_seq
    OWNER TO #{DbUser}#;

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_optic_alarm
(
    id bigint NOT NULL DEFAULT nextval('s2110_optic_alarm_id_seq'::regclass),
    serial bigint,
    agent text COLLATE pg_catalog."default",
    ci_id text COLLATE pg_catalog."default",
    monitoredelementname text COLLATE pg_catalog."default",
    metrics text COLLATE pg_catalog."default",
    raisetime timestamp without time zone,
	import_time timestamp without time zone,
    CONSTRAINT s2110_optic_alarm_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_optic_alarm
    OWNER to #{DbUser}#;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_optic_alarm
    ADD CONSTRAINT s2110_optic_alarm_1_uk UNIQUE (serial)
    USING INDEX TABLESPACE s2110_index;

CREATE SEQUENCE IF NOT EXISTS #{Schema}#.s2110_matched_alarm_id_seq
    INCREMENT 1
    START 1
    MINVALUE 1
    MAXVALUE 9223372036854775807
    CACHE 1;

ALTER SEQUENCE #{Schema}#.s2110_matched_alarm_id_seq
    OWNER TO #{DbUser}#;

CREATE TABLE IF NOT EXISTS #{Schema}#.s2110_matched_alarm
(
    id bigint NOT NULL DEFAULT nextval('s2110_matched_alarm_id_seq'::regclass),
    agent_id bigint,
    ci_id text COLLATE pg_catalog."default",
    metric_type text COLLATE pg_catalog."default",
    metric_name text COLLATE pg_catalog."default",
    raise_time timestamp without time zone,
	optic_alarm_id bigint,
	alarm_id bigint,
	match_time timestamp without time zone,
    CONSTRAINT s2110_matched_alarm_pk PRIMARY KEY (id)
        USING INDEX TABLESPACE s2110_index
)
TABLESPACE s2110_data;

ALTER TABLE IF EXISTS #{Schema}#.s2110_matched_alarm
    ADD CONSTRAINT s2110_matched_alarm_optic_alarm_fk FOREIGN KEY (optic_alarm_id)
    REFERENCES #{Schema}#.s2110_optic_alarm (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
	
ALTER TABLE IF EXISTS #{Schema}#.s2110_matched_alarm
    ADD CONSTRAINT s2110_matched_alarm_alarm_fk FOREIGN KEY (alarm_id)
    REFERENCES #{Schema}#.s2110_alarm (id) MATCH SIMPLE
    ON UPDATE NO ACTION
    ON DELETE NO ACTION;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_optic_alarm
    ADD COLUMN enrichmentflag smallint;

ALTER TABLE IF EXISTS #{Schema}#.s2110_optic_alarm
    ADD COLUMN ci_status smallint;
    
ALTER TABLE IF EXISTS #{Schema}#.s2110_optic_alarm
    ADD COLUMN actionablealarm smallint;
    
    