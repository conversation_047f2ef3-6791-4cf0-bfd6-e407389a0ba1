kind: Secret
apiVersion: v1
metadata:
  name: a2110-sap-air-client-secret-#{appEnv}#
  namespace: a2110-olympus-monitoring
stringData:
  #Kafka variables
  KAFKA_USER: "#{kafkaUser}#"
  KAFKA_PASSWORD: "#{kafkaPassword}#"

  #Sap Credential
  SAP_USER: "#{sapUser}#"
  SAP_PASSWORD: "#{sapPassword}#"

  #Air Credential
  AIR_CLIENT_ID: "#{airClientId}#"
  AIR_CLIENT_SECRET: "#{airClientSecret}#"

  #Icinga Credential
  ICINGA_USER: "#{icingaUser}#"
  ICINGA_PASSWORD: "#{icingaPassword}#"

  #Postgres DB Credential
  DB_USER: "#{databaseUser}#"
  DB_PASSWORD: "#{databasePassword}#"

  #Elastic APM Credential
  ELASTIC_APM_SECRET_TOKEN: "#{elasticApmSecretToken}#"
type: Opaque
