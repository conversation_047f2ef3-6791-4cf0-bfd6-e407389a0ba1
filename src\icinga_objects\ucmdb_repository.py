"""Module to handle the retrieval of UCMDB data."""

import pandas as pd
from sqlalchemy.orm import Session

from icinga_objects import statics
from olympus_common import pd as olympus_pd
from olympus_common.ucmdb import AssetListView, CIDependencies, HierarchyListView, MetricListView


class UcmdbRepository:
    """Class to handle the retrieval of UCMDB data for icinga_objects."""

    def __init__(self, db_session: Session) -> None:
        self.db_session = db_session

    def get_cis_from_ucmdb(self, ci_type: str) -> pd.DataFrame:
        """Retrieve CIs from UCMDB."""
        with self.db_session as session:
            assets = AssetListView().get_from_ci_type_df(ci_type, session)
            if assets.empty:
                return pd.DataFrame()
            return assets

    def get_all_dashboard_from_ucmdb(self, ci_type: str) -> pd.DataFrame:
        """Get all dashboards based on dashboard name."""
        with self.db_session as session:
            dashboards = HierarchyListView().get_dashboards_from_ci_type_df(ci_type, session)
            return self._clean_dashboard(dashboards)

    @staticmethod
    def _clean_dashboard(df_ucmdb: pd.DataFrame) -> pd.DataFrame:
        """Rename dashboard field."""
        if not df_ucmdb.empty:
            return olympus_pd.clean_raw_dataframe(df_ucmdb, statics.COLUMNS_HIERARCHY_RENAMING)
        return pd.DataFrame()

    def get_host_to_process_from_floc_id(self, unprocessed_hosts: list[str]) -> pd.DataFrame:
        """Get host to process from floc id."""
        with self.db_session as session:
            host_unprocessed = AssetListView().get_from_floc_id_process_check_df(unprocessed_hosts, session)
            if host_unprocessed.empty:
                return pd.DataFrame()
            return host_unprocessed

    def get_metrics(self, source: str) -> pd.DataFrame:
        """Get a metrics data from UCMDB."""
        with self.db_session as session:
            metrics = MetricListView().get_metric_data_from_source_df(source, session)
            return metrics

    def get_metrics_description(self, d_condition: list[tuple]) -> pd.DataFrame:
        """Get a metrics description data from UCMDB."""
        with self.db_session as session:
            metrics = MetricListView().get_metric_description_df(d_condition, session)
            return metrics

    def get_ci_dependencies(self) -> pd.DataFrame:
        """Select all dashboards based on dashboard_name and ci_type in dataframe."""
        with self.db_session as session:
            ci_relations = CIDependencies().get_ci_dependencies_df(session)
            return ci_relations
