kind: Secret
apiVersion: v1
metadata:
  name: a2110-mon-stonebranch-secret-#{appEnv}#
  namespace: a2110-olympus-monitoring
stringData:
  KAFKA_PASSWORD: "#{kafkaPassword}#"
  KAFKA_USER: "#{kafkaUser}#"
  DB_HOST: "#{databaseHost}#"
  DB_PORT: "#{databasePort}#"
  DB_NAME: "#{databaseName}#"
  DB_USER: "#{databaseUser}#"
  DB_PASSWORD: "#{databasePassword}#"
  ELASTIC_APM_SECRET_TOKEN: "#{elasticApmSecretToken}#"
type: Opaque
