"""Ictops module for mon-zabbix.

This correspond to the new way of working with Zabbix DDs.
"""

import datetime

import pandas as pd

from mon_zabbix import statics
from olympus_common import enums as olympus_enums


def element_monitored_name(row: pd.Series) -> str:
    """Return the element monitored name."""
    return row["metric_name"]


def metrics(row: pd.Series) -> str:
    """Return the metrics."""
    return row["metric_type"]


def ci_id(row: pd.Series) -> str:
    """Return the CI ID."""
    return row["event_ci_id"]


def severity(row: pd.Series) -> int:
    """Return the severity."""
    if _is_heartbeat(row):
        return olympus_enums.Severity.INDETERMINATE.value

    if str(row["problem"]) == "0":
        # Clear
        return olympus_enums.Severity.CLEARED.value

    match int(row["severity"]):
        case olympus_enums.Severity.WARNING.value:
            return olympus_enums.Severity.WARNING.value
        case olympus_enums.Severity.MINOR.value:
            return olympus_enums.Severity.MINOR.value
        case olympus_enums.Severity.MAJOR.value:
            return olympus_enums.Severity.MAJOR.value
        case olympus_enums.Severity.CRITICAL.value:
            return olympus_enums.Severity.CRITICAL.value
        case _:
            return olympus_enums.Severity.INDETERMINATE.value


def node_node_alias(row: pd.Series) -> pd.Series:
    """Return the node and the node alias."""
    host_: str = row["host"]
    if "." in host_:
        host_split = host_.split(".", maxsplit=1)
        node = host_split[0]
        node_alias = host_split[1]
    else:
        node = host_
        node_alias = ""
    return pd.Series((node, node_alias))


def clear_level(row: pd.Series) -> str:
    """Return if the event is a resolution or an alarm."""
    if _is_heartbeat(row):
        return olympus_enums.AlarmType.HEARTBEAT.value

    return (
        olympus_enums.AlarmType.RESOLUTION.value
        if str(row["problem"]) == "0"
        else olympus_enums.AlarmType.PROBLEM.value
    )


def raise_time_clear_time(row: pd.Series) -> pd.Series:
    """Return the raise and clear times."""
    if clear_level(row) == olympus_enums.AlarmType.RESOLUTION.value:
        clear_time = datetime.datetime.fromtimestamp(int(row["clock"]), tz=datetime.timezone.utc).replace(tzinfo=None)
    else:
        clear_time = None
    raise_time = datetime.datetime.fromtimestamp(int(row["clock"]), tz=datetime.timezone.utc).replace(tzinfo=None)
    return pd.Series((raise_time, clear_time))


def alert_group(row: pd.Series) -> str:
    """Return the alert key.

    The alert key is a concatenation of the metrics and the element_monitored_name fields.
    """
    return f"{ci_id(row)}_{metrics(row)}"


def delay(row: pd.Series) -> int:
    """Return the delay for the corresponding conditions (else 0)."""
    return 360 if element_monitored_name(row) in statics.SNOOZED_MONITORED_ELEMENTS else 0


def wake_up_time(row: pd.Series) -> datetime.datetime:
    """Return the wake up time."""
    delay_ = delay(row)
    raise_time = row["raise_time"]
    return raise_time + datetime.timedelta(0, delay_)


def actionable_alarm(row: pd.Series) -> bool | None:
    """Complete the actionable_alarm field of the event.

    Parameters
    ----------
    row : pd.Series
        The row of the event.

    Returns
    -------
    bool | None
        The status of actionable_alarm or None.
    """
    if delay(row) != 0:
        return None  # since it needs to be enriched after the snooze time.
    if (
        severity(row) == olympus_enums.Severity.INDETERMINATE.value
        and clear_level(row) != olympus_enums.AlarmType.RESOLUTION.value
    ):
        return False
    else:
        return None


def _is_heartbeat(row: pd.Series) -> bool:
    """Determine if the message is a heartbeat."""
    return row["metric_name"] == "Heartbeat"
