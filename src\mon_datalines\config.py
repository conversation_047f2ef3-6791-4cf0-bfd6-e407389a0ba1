"""Configuration module for mon-datalines."""

from dataclasses import dataclass

from olympus_common.config import DatabaseKafkaConsumerServiceConfig
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseKafkaConsumerServiceConfig, metaclass=Singleton):
    """Represent the configuration for the Datalines DD."""


config = Config()  # Create the singleton instance at import time.
