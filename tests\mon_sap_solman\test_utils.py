"""Utils module for mon-sap-solman."""

from mon_sap_solman.utils import substring_after_second, substring_before_second


def test_substring_before_second():
    assert substring_before_second("example_string_with_delimiters", "_") == "example_string"
    assert substring_before_second("one_two_three_four", "_") == "one_two"
    assert substring_before_second("single_delimiter", "_") == "single_delimiter"
    assert substring_before_second("no_delimiter", "_") == "no_delimiter"


def test_substring_after_second():
    assert substring_after_second("example_string_with_delimiters", "_") == "with_delimiters"
    assert substring_after_second("one_two_three_four", "_") == "three_four"
    assert substring_after_second("single_delimiter", "_") == "single_delimiter"
    assert substring_after_second("no_delimiter", "_") == "no_delimiter"
