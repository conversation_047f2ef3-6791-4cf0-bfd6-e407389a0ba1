"""Enums module for mon-certificates.

Contains the enums for the project.
"""

from enum import Enum


class NacadcaContactColumn(str, Enum):
    """Represent the possible mail address types."""

    RECIPIENTS = "recipients"
    MANAGER = "manager"
    HEAD_OF = "head_of"
    TEAM_LEAD = "team_lead"


class CertificateType(str, Enum):
    """Represent the possible types of certificates."""

    NACADCA = "railb.be.nacadca"
    TALOS = "talos"


class ExpirySeverities(str, Enum):
    """Represent the different severities for the left expiry days of a certificate."""

    INFO = "informational"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class TalosContacts(str, Enum):
    """Represent the contacts used for Talos certificates."""

    PROGRAM_MANAGER = "program_manager"
    PROJECT_LEADER = "project_leader"
    APPLICATION_MANAGER = "application_manager"
