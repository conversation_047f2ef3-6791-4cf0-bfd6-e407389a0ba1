# Openshift configuration for Zabbix ingestion

### Replace tokens

zabbixCC: This enables us to have one openshift config for different Zabbix variants. (value: ucc, lcc, ictops)
When configuring the release-pipeline in Azure devops, variables should be set in the correct stage.

### Secrets

Secrets are currently managed using `.openshift/00-secret.yml` and replace tokens. You can then set these tokens in your release-pipeline's variables.

Zabbix ingestion requires extra secret for Kafka certificate:  
```yaml
---
name: kafka-certificate
  namespace: a2110-olympus-monitoring
data:
  certificate: >-
    -----BEGIN CERTIFICATE-----
    My super certificate
    -----END CERTIFICATE-----
type: Opaque
```
Then upload this file
```bash
oc create -f file-with-secrets.yml
```
##### NOTE: Ensure that these secrets are NOT version controlled as this would compromise the secret. Using replace tokens is a valid alternative.
##### NOTE: The certificate secret is needed to insert the data into Kafka
