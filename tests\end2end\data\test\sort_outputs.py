import json
from pathlib import Path

SORT_KEYS = {"event_type": 0, "ci_id": 1, "metric_name": 2, "metric_type": 3}


def main():
    """Sort the output in order to more easily identify which record failed.

    Note that this file is never ran automatically, you need to run it manually.
    """
    json_files = Path(__file__).parent.glob("*.json")
    for json_file in json_files:
        content = json.loads(json_file.read_text())
        for item in content["data"]:
            item["output"] = sort_output(item["output"])
        print()
        json_file.write_text(json.dumps(content, indent=2))


def sort_output(item: dict[str, list[dict]]):
    result = dict()
    for key, value in item.items():
        result[key] = [dict(sorted(record.items(), key=lambda x: SORT_KEYS.get(x[0], 100))) for record in value]
    return result


if __name__ == "__main__":
    main()
