apiVersion: batch/v1
kind: CronJob
metadata:
  name: a2110-mon-certificates-cronjob-#{appEnv}#
  namespace: a1617-mail-monitoring-gateway
spec:
  schedule: "30 9 * * *"
  concurrencyPolicy: "Replace"
  startingDeadlineSeconds: 300
  suspend: false
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 2
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: "a2110-mon-certificates-app-#{appEnv}#"
        spec:
          volumes:
            - name: a2110-mon-certificates-volume-claim-#{appEnv}#
              persistentVolumeClaim:
                claimName: a2110-mon-certificates-volume-claim-#{appEnv}#
          containers:
            - name: a2110-mon-certificates-app-#{appEnv}#
              image: "artifactory.msnet.railb.be/a2110-docker/a2110-olympus-image:#{releaseName}#"
              volumeMounts:
                - name: a2110-mon-certificates-volume-claim-#{appEnv}#
                  mountPath: "/data"
              envFrom:
                - configMapRef:
                    name: a2110-mon-certificates-config-map-#{appEnv}#
                - secretRef:
                    name: a2110-mon-certificates-secret-#{appEnv}#
          restartPolicy: OnFailure
          imagePullSecrets:
            - name: artifactory-docker-registry
