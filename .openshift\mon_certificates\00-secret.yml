kind: Secret
apiVersion: v1
metadata:
  name: a2110-mon-certificates-secret-#{appEnv}#
  namespace: a1617-mail-monitoring-gateway
stringData:
  DEBUG: "#{debug}#"
  ELASTIC_USER: "#{certificatesUser}#"
  ELASTIC_PASSWORD: "#{certificatesPassword}#"
  CLIENT_ID: "#{clientId}#"
  CLIENT_SECRET: "#{clientSecret}#"
  TENANT_ID: "#{tenantId}#"
  EMAIL_ADDRESS: "#{emailAddress}#"
  NOTIFICATION_EMAILS: >-
    #{notificationEmails}#
  ICC_EMAILS: >-
    #{iccEmails}#
  DB_HOST: "#{databaseHost}#"
  DB_PORT: "#{databasePort}#"
  DB_NAME: "#{databaseName}#"
  DB_USER: "#{databaseUser}#"
  DB_PASSWORD: "#{databasePassword}#"
  SYSLOG_HOST: "#{syslogHost}#"
  SYSLOG_PORT: "#{syslogPort}#"
  UCMDB_USER: "#{ucmdbApiUser}#"
  UCMDB_PASSWORD: "#{ucmdbApiPassword}#"
  UCMDB_TOKEN_URL: "#{ucmdbTokenUrl}#"
  UCMDB_SCOPE: "#{ucmdbScope}#"
  UCMDB_ENDPOINT: "#{ucmdbEndpoint}#"
  UCMDB_VERSION: "#{ucmdbVersion}#"
type: Opaque
