"""Entrypoint for the application.

The tool bandit is silenced for the subprocess.run call, as the command is not user-controlled.
"""

from subprocess import run
from sys import executable

from olympus_common import defaults
from olympus_common.ucmdb_api import UcmdbSession, post_data_to_ucmdb
from sbom_tools.config import config


def main():
    """Run the CycloneDX tool and send the SBOM to UCMDB.

    Notes
    -----
    The SBOM is generated using the `cyclonedx-py` tool, which does not expose a public API, so we call it as a
    subprocess.

    References
    ----------
    https://cyclonedx-bom-tool.readthedocs.io/en/latest/usage.html#programmatic-usage
    """
    defaults.get_logger(config.debug, config.logger_config, setup=True)
    cmd = (executable, "-m", "cyclonedx_py", "poetry", "-o", config.sbom_output_path)
    run(cmd, check=True)  # noqa: S603
    send_sbom_to_ucmdb()


def send_sbom_to_ucmdb():
    """Send the SBOM to UCMDB.

    References
    ----------
    https://dev.azure.com/INFRABEL/a1878-ucmdb/_git/a1878-ucmdb-azure-devops-wiki?path=/Api/v6-sboms.md
    """
    headers = UcmdbSession().get_header(config.api_config)
    data = {
        "environmentName": config.environment_name,
        "name": config.application_name,
        "version": config.release_name,
        "isNextVersion": False,
    }
    with config.sbom_output_path.open("rb") as file:
        files = [("file", ("sbom_small.json", file, "application/json"))]
        post_data_to_ucmdb("v6/sboms", headers, config.api_config, data=data, files=files)
