{"agent_names": ["StruxureWare"], "data": [{"input": {"SNMPv2-SMI::enterprises.5528.100.11.8": "", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x33b350b3 @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x1ce12f41 @value=517856445>, @varbind_list=[#<SNMP::VarBind:0x154cb149 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_C62816B2\">, #<SNMP::VarBind:0x3cd35d9b @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0x295de667 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x244ed21d @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_5\">, #<SNMP::VarBind:0x5478e924 @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - ACin\">, #<SNMP::VarBind:0x72e149b5 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x3449f750 @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x66c505fe @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x75e7e1cb @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0xd33b223 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x763b27a2 @value=1739181444>>, #<SNMP::VarBind:0x381f366e @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x723c711f @value=1739188104>>, #<SNMP::VarBind:0x628e77d1 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x4d274a15 @value=0>>, #<SNMP::VarBind:0xccab999 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x1def9596 @value=4>>, #<SNMP::VarBind:0xcce5905 @name=[*******.4.1.5528.100.11.14], @value=\"0.0 V\">, #<SNMP::VarBind:0x7b4ecd6b @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x2291a5ea @value=0>>, #<SNMP::VarBind:0x43b3c70b @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x3dcbb809 @value=0>>], @specific_trap=3, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x30630f4e @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "event": {"kafka": {"offset": 62557, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd", "partition": 1, "key": null, "timestamp": "2025-02-10T11:48:29.311Z"}, "logstash": {"instance_name": "iictniapls015"}, "uuid": "bf20eec7-18fc-4c48-9e57-8035f77c63d9"}, "@timestamp": "2025-02-10T11:48:29.211498174Z", "snmptrap": {"sensorvalue1": "0.0 V", "severity": "4", "responsible": "nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_5", "eventName": "VOLTAGE - ACin", "errorType": "nbErrorType_toolow", "startTime": "1739181444", "hostname": "UPFOVOR02RIB (*************)", "description": "Value Too Low", "location": "nbSNMPEnc7728578E", "errorId": "nbErrorCond_C62816B2", "notifyTime": "1739188104", "resolveTime": "0"}, "host": {"ip": "*************"}, "type": "snmp_trap", "SNMPv2-SMI::enterprises.5528.100.11.15": "0", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "SNMPv2-SMI::enterprises.5528.100.11.9": ""}, "output": {"s2110_alarm": [{"identifier": "24/*************/VOLTAGE - ACin/VOLTAGE"}], "s2110_occurrence": [{"identifier": "24/*************/VOLTAGE - ACin/VOLTAGE"}]}}, {"input": {"SNMPv2-SMI::enterprises.5528.100.11.8": "", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x1f72525d @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x42244527 @value=517856478>, @varbind_list=[#<SNMP::VarBind:0xafed4e2 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_30B8EDF6\">, #<SNMP::VarBind:0x7938f5a @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toohigh\">, #<SNMP::VarBind:0x677eeff2 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too High\">, #<SNMP::VarBind:0x38daeef1 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_acinfail\">, #<SNMP::VarBind:0x447f64cc @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - INVERTER ACin FAIL\">, #<SNMP::VarBind:0x5f8dda46 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x4f1caf3a @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x20de7d4e @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x1f38349d @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x2e5682d6 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x4199d59 @value=1739181444>>, #<SNMP::VarBind:0x577ea499 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x93cee37 @value=1739188104>>, #<SNMP::VarBind:0x13e68df4 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x5bdd2984 @value=0>>, #<SNMP::VarBind:0x7925040a @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x1706104f @value=4>>, #<SNMP::VarBind:0x30a8b676 @name=[*******.4.1.5528.100.11.14], @value=\"1.0\">, #<SNMP::VarBind:0x20b28ee @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x535f79f1 @value=1>>, #<SNMP::VarBind:0x74fc9fbd @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x1928c52 @value=0>>], @specific_trap=2, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x5a8598d6 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "event": {"kafka": {"offset": 62558, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd", "partition": 1, "key": null, "timestamp": "2025-02-10T11:48:29.643Z"}, "logstash": {"instance_name": "iictniapls015"}, "uuid": "67e4f737-d096-4463-855a-cf94cca5a383"}, "@timestamp": "2025-02-10T11:48:29.542996179Z", "snmptrap": {"sensorvalue1": "1.0", "severity": "4", "responsible": "nbSNMPEnc7728578E_acinfail", "eventName": "VOLTAGE - INVERTER ACin FAIL", "errorType": "nbErrorType_toohigh", "startTime": "1739181444", "hostname": "UPFOVOR02RIB (*************)", "description": "Value Too High", "location": "nbSNMPEnc7728578E", "errorId": "nbErrorCond_30B8EDF6", "notifyTime": "1739188104", "resolveTime": "0"}, "host": {"ip": "*************"}, "type": "snmp_trap", "SNMPv2-SMI::enterprises.5528.100.11.15": "1", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "SNMPv2-SMI::enterprises.5528.100.11.9": ""}, "output": {"s2110_alarm": [{"identifier": "24/*************/VOLTAGE - INVERTER ACin FAIL/VOLTAGE"}], "s2110_occurrence": [{"identifier": "24/*************/VOLTAGE - INVERTER ACin FAIL/VOLTAGE"}]}}, {"input": {"SNMPv2-SMI::enterprises.5528.100.11.8": "", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x74d40a94 @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x53055e5 @value=517862203>, @varbind_list=[#<SNMP::VarBind:0x5db9dcfb @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_F930D518\">, #<SNMP::VarBind:0x9edfd72 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0xe750ed7 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x61bdafbc @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_3\">, #<SNMP::VarBind:0x7270e974 @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - Battery Voltage\">, #<SNMP::VarBind:0x14908c25 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x32b52bd6 @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x3e79b273 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x4bb7d781 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x5b568b76 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x40a6f051 @value=1739181806>>, #<SNMP::VarBind:0x333f381c @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x46c1ea25 @value=1739188166>>, #<SNMP::VarBind:0x2923c4b0 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x5e709dbd @value=0>>, #<SNMP::VarBind:0x1445b1b7 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x5d1cf1d8 @value=4>>, #<SNMP::VarBind:0x6a2db88f @name=[*******.4.1.5528.100.11.14], @value=\"49.7 V\">, #<SNMP::VarBind:0x306a5d02 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x91b8ab1 @value=49>>, #<SNMP::VarBind:0x3333287b @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x34dd1752 @value=700000>>], @specific_trap=3, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x56bdce86 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "event": {"kafka": {"offset": 62562, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd", "partition": 1, "key": null, "timestamp": "2025-02-10T11:49:26.892Z"}, "logstash": {"instance_name": "iictniapls015"}, "uuid": "b67629e7-708a-4fd8-acf7-d28d88d180b0"}, "@timestamp": "2025-02-10T11:49:26.791775722Z", "snmptrap": {"sensorvalue1": "49.7 V", "severity": "4", "responsible": "nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_3", "eventName": "VOLTAGE - Battery Voltage", "errorType": "nbErrorType_toolow", "startTime": "1739181806", "hostname": "UPFOVOR02RIB (*************)", "description": "Value Too Low", "location": "nbSNMPEnc7728578E", "errorId": "nbErrorCond_F930D518", "notifyTime": "1739188166", "resolveTime": "0"}, "host": {"ip": "*************"}, "type": "snmp_trap", "SNMPv2-SMI::enterprises.5528.100.11.15": "49", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.16": "700000"}, "output": {"s2110_alarm": [{"identifier": "24/*************/VOLTAGE - Battery Voltage/VOLTAGE"}], "s2110_occurrence": [{"identifier": "24/*************/VOLTAGE - Battery Voltage/VOLTAGE"}]}}, {"input": {"snmptrap": {"description": "Value Too High", "errorType": "nbErrorType_toohigh", "resolveTime": "0", "severity": "4", "startTime": "1739181444", "errorId": "nbErrorCond_30B8EDF6", "notifyTime": "1739194405", "location": "nbSNMPEnc7728578E", "sensorvalue1": "1.0", "hostname": "UPFOVOR02RIB (*************)", "responsible": "nbSNMPEnc7728578E_acinfail", "eventName": "VOLTAGE - INVERTER ACin FAIL"}, "@timestamp": "2025-02-10T13:33:25.264889796Z", "event": {"kafka": {"timestamp": "2025-02-10T13:33:25.367Z", "offset": 62799, "key": null, "partition": 0, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd"}, "uuid": "7e903611-7266-4271-93b7-c9e53df7d9a3", "logstash": {"instance_name": "iictmiapls016"}}, "message": "#<SNMP::SNMPv1_Trap:0x189c478a @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x54f64b31 @value=518486051>, @varbind_list=[#<SNMP::VarBind:0x15ffb54c @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_30B8EDF6\">, #<SNMP::VarBind:0x6f25006e @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toohigh\">, #<SNMP::VarBind:0x96c6178 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too High\">, #<SNMP::VarBind:0x282637b0 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_acinfail\">, #<SNMP::VarBind:0x409ba22d @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - INVERTER ACin FAIL\">, #<SNMP::VarBind:0x7fafc20 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x6d773a06 @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x74750baf @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x696245c6 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x39a9bf36 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x6d5a150f @value=1739181444>>, #<SNMP::VarBind:0x31ae4378 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x4a196cfe @value=1739194405>>, #<SNMP::VarBind:0x3e3d3a84 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x372a221 @value=0>>, #<SNMP::VarBind:0x400484b4 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x41fb6ed6 @value=4>>, #<SNMP::VarBind:0x16c58577 @name=[*******.4.1.5528.100.11.14], @value=\"1.0\">, #<SNMP::VarBind:0xb04e24 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x64f54ba2 @value=1>>, #<SNMP::VarBind:0x7e621434 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x44b6c8b2 @value=0>>], @specific_trap=2, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x46f56f6e @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "1", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "host": {"ip": "*************"}, "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "type": "snmp_trap", "@version": "1"}, "output": {"s2110_occurrence": [{"event_type": "problem", "identifier": "24/*************/VOLTAGE - INVERTER ACin FAIL/VOLTAGE"}]}}, {"input": {"SNMPv2-SMI::enterprises.5528.100.11.8": "", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x26730167 @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x2ade7c92 @value=518486074>, @varbind_list=[#<SNMP::VarBind:0x6aebec2 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_5EC88C08\">, #<SNMP::VarBind:0x3868ff49 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0x618b3e3a @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x51034055 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_5\">, #<SNMP::VarBind:0x2228ac19 @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - ACin\">, #<SNMP::VarBind:0x5d459da2 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x650702de @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x2ecde7c3 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x4962de67 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x38bbf264 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x65f7165f @value=1739181444>>, #<SNMP::VarBind:0x24c516dd @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x7b0b75c4 @value=1739194405>>, #<SNMP::VarBind:0x1cc49013 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x415c383a @value=0>>, #<SNMP::VarBind:0x4da3ad32 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x6da65417 @value=4>>, #<SNMP::VarBind:0x55da05b8 @name=[*******.4.1.5528.100.11.14], @value=\"0.0 V\">, #<SNMP::VarBind:0x75676ad1 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x25fd8372 @value=0>>, #<SNMP::VarBind:0x7a247d36 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x7fd6400f @value=0>>], @specific_trap=3, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x31299cc6 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "event": {"kafka": {"offset": 62607, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd", "partition": 1, "key": null, "timestamp": "2025-02-10T13:33:25.607Z"}, "logstash": {"instance_name": "iictniapls015"}, "uuid": "a5a465a9-ee97-41e4-8ace-c0a7bbc4460b"}, "@timestamp": "2025-02-10T13:33:25.502720511Z", "snmptrap": {"sensorvalue1": "0.0 V", "severity": "4", "responsible": "nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_5", "eventName": "VOLTAGE - ACin", "errorType": "nbErrorType_toolow", "startTime": "1739181444", "hostname": "UPFOVOR02RIB (*************)", "description": "Value Too Low", "location": "nbSNMPEnc7728578E", "errorId": "nbErrorCond_5EC88C08", "notifyTime": "1739194405", "resolveTime": "0"}, "host": {"ip": "*************"}, "type": "snmp_trap", "SNMPv2-SMI::enterprises.5528.100.11.15": "0", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.16": "0"}, "output": {"s2110_occurrence": [{"event_type": "problem", "identifier": "24/*************/VOLTAGE - ACin/VOLTAGE"}]}}, {"input": {"snmptrap": {"description": "Value Too Low", "errorType": "nbErrorType_toolow", "resolveTime": "0", "severity": "4", "startTime": "1739181806", "errorId": "nbErrorCond_DAECDC24", "notifyTime": "1739194406", "location": "nbSNMPEnc7728578E", "sensorvalue1": "53.4 V", "hostname": "UPFOVOR02RIB (*************)", "responsible": "nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_3", "eventName": "VOLTAGE - Battery Voltage"}, "@timestamp": "2025-02-10T13:33:27.234280181Z", "event": {"kafka": {"timestamp": "2025-02-10T13:33:27.335Z", "offset": 62800, "key": null, "partition": 0, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd"}, "uuid": "40fb5da8-98cf-4c8b-a919-87e4a51f897f", "logstash": {"instance_name": "iictmiapls016"}}, "message": "#<SNMP::SNMPv1_Trap:0x48698f9d @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x6bcf6e77 @value=518486248>, @varbind_list=[#<SNMP::VarBind:0x78a7dfa5 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_DAECDC24\">, #<SNMP::VarBind:0x7c36a24b @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0x6c9ad76c @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x7eb04e47 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_3\">, #<SNMP::VarBind:0x38acecc3 @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - Battery Voltage\">, #<SNMP::VarBind:0x71c13567 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x4bdaad84 @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x37e410ce @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x68713805 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x10634261 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0xe4b6b64 @value=1739181806>>, #<SNMP::VarBind:0x19be6803 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x786eda21 @value=1739194406>>, #<SNMP::VarBind:0x7294ff07 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x27d1aa7a @value=0>>, #<SNMP::VarBind:0x4d4520f3 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x437444a8 @value=4>>, #<SNMP::VarBind:0x16cdf706 @name=[*******.4.1.5528.100.11.14], @value=\"53.4 V\">, #<SNMP::VarBind:0x3ce50ddf @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x58c3e008 @value=53>>, #<SNMP::VarBind:0x39f3aebc @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x40ff0b43 @value=350000>>], @specific_trap=3, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x54e06d76 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "53", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "host": {"ip": "*************"}, "SNMPv2-SMI::enterprises.5528.100.11.16": "350000", "type": "snmp_trap", "@version": "1"}, "output": {"s2110_occurrence": [{"event_type": "problem", "identifier": "24/*************/VOLTAGE - Battery Voltage/VOLTAGE"}]}}, {"input": {"snmptrap": {"description": "Value Too Low", "errorType": "nbErrorType_toolow", "resolveTime": "1739194407", "severity": "4", "startTime": "1739181444", "errorId": "nbErrorCond_5EC88C08", "notifyTime": "1739194407", "location": "nbSNMPEnc7728578E", "sensorvalue1": "257.0 V", "hostname": "UPFOVOR02RIB (*************)", "responsible": "nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_5", "eventName": "VOLTAGE - ACin"}, "@timestamp": "2025-02-10T13:33:28.415500438Z", "event": {"kafka": {"timestamp": "2025-02-10T13:33:28.516Z", "offset": 62801, "key": null, "partition": 0, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd"}, "uuid": "8d2d0b4d-4e85-42f5-8f7d-384a170725de", "logstash": {"instance_name": "iictmiapls016"}}, "message": "#<SNMP::SNMPv1_Trap:0x13e40442 @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x3746decc @value=518486366>, @varbind_list=[#<SNMP::VarBind:0x58073924 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_5EC88C08\">, #<SNMP::VarBind:0x6b72f072 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0x6a43f36e @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x3e3f188c @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_5\">, #<SNMP::VarBind:0x5f10d876 @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - ACin\">, #<SNMP::VarBind:0x231d4286 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x56300d08 @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x108d843a @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x49873d35 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0xc75ff1 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x56495aac @value=1739181444>>, #<SNMP::VarBind:0x2a99a80b @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x5a0756f8 @value=1739194407>>, #<SNMP::VarBind:0x6085a29e @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x7c050818 @value=1739194407>>, #<SNMP::VarBind:0x1b3b2e63 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x7b6b1b6a @value=4>>, #<SNMP::VarBind:0x36b7d7cb @name=[*******.4.1.5528.100.11.14], @value=\"257.0 V\">, #<SNMP::VarBind:0x62a65ccd @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x20ac2ecc @value=257>>, #<SNMP::VarBind:0x33fd1f8 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0xbbe57db @value=29999>>], @specific_trap=103, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x5bae3255 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "257", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "host": {"ip": "*************"}, "SNMPv2-SMI::enterprises.5528.100.11.16": "29999", "type": "snmp_trap", "@version": "1"}, "output": {"s2110_occurrence": [{"event_type": "clear", "identifier": "24/*************/VOLTAGE - ACin/VOLTAGE"}]}}, {"input": {"snmptrap": {"description": "Value Too Low", "errorType": "nbErrorType_toolow", "resolveTime": "1739194407", "severity": "4", "startTime": "1739181444", "errorId": "nbErrorCond_C62816B2", "notifyTime": "1739194407", "location": "nbSNMPEnc7728578E", "sensorvalue1": "257.0 V", "hostname": "UPFOVOR02RIB (*************)", "responsible": "nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_5", "eventName": "VOLTAGE - ACin"}, "@timestamp": "2025-02-10T13:33:30.446173063Z", "event": {"kafka": {"timestamp": "2025-02-10T13:33:30.546Z", "offset": 62802, "key": null, "partition": 0, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd"}, "uuid": "9e8e2be3-3fde-4b18-a562-4bf0d45c885b", "logstash": {"instance_name": "iictmiapls016"}}, "message": "#<SNMP::SNMPv1_Trap:0x8938449 @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x663ef2db @value=518486569>, @varbind_list=[#<SNMP::VarBind:0x79dcd600 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_C62816B2\">, #<SNMP::VarBind:0x6df9b4be @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0x7cc03bf0 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x123e517b @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_5\">, #<SNMP::VarBind:0xd4df9ea @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - ACin\">, #<SNMP::VarBind:0x2db1f0b9 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x50535144 @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x308677bc @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x52e64aef @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x70704c32 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x3ae3fa09 @value=1739181444>>, #<SNMP::VarBind:0x395b417e @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x598ac588 @value=1739194407>>, #<SNMP::VarBind:0x2be90357 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0xe7f8791 @value=1739194407>>, #<SNMP::VarBind:0x6dcc7791 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x62655925 @value=4>>, #<SNMP::VarBind:0x1f51d85 @name=[*******.4.1.5528.100.11.14], @value=\"257.0 V\">, #<SNMP::VarBind:0x73c45125 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x7dbad033 @value=257>>, #<SNMP::VarBind:0x355e02d0 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x1cee28cb @value=29999>>], @specific_trap=103, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x4df20f0a @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "257", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "host": {"ip": "*************"}, "SNMPv2-SMI::enterprises.5528.100.11.16": "29999", "type": "snmp_trap", "@version": "1"}, "output": {}, "comment": "No output expected as the event is already cleared"}, {"input": {"SNMPv2-SMI::enterprises.5528.100.11.8": "", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x78867f82 @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x21126069 @value=518486665>, @varbind_list=[#<SNMP::VarBind:0x113bf497 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_DAECDC24\">, #<SNMP::VarBind:0x5a8af35d @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0x68a9c14d @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x48536c5e @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_3\">, #<SNMP::VarBind:0x682b68f @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - Battery Voltage\">, #<SNMP::VarBind:0xb9fbd19 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x2d1ab68e @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x7ef81eef @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x6dddf971 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x1b5ce6a6 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x630a8d85 @value=1739181806>>, #<SNMP::VarBind:0x5d3bf86d @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x5f2f84ca @value=1739194407>>, #<SNMP::VarBind:0x23ba205f @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x146be708 @value=1739194407>>, #<SNMP::VarBind:0x16919d78 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x353b52b4 @value=4>>, #<SNMP::VarBind:0x47713494 @name=[*******.4.1.5528.100.11.14], @value=\"53.4 V\">, #<SNMP::VarBind:0xce9d8d5 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x1689fe6d @value=53>>, #<SNMP::VarBind:0x79c8a0fa @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x3753fec3 @value=350000>>], @specific_trap=103, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x753eb78c @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "event": {"kafka": {"offset": 62608, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd", "partition": 1, "key": null, "timestamp": "2025-02-10T13:33:31.504Z"}, "logstash": {"instance_name": "iictniapls015"}, "uuid": "8f0a10b2-2dfb-4160-a1aa-4701a4b46f76"}, "@timestamp": "2025-02-10T13:33:31.403417218Z", "snmptrap": {"sensorvalue1": "53.4 V", "severity": "4", "responsible": "nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_3", "eventName": "VOLTAGE - Battery Voltage", "errorType": "nbErrorType_toolow", "startTime": "1739181806", "hostname": "UPFOVOR02RIB (*************)", "description": "Value Too Low", "location": "nbSNMPEnc7728578E", "errorId": "nbErrorCond_DAECDC24", "notifyTime": "1739194407", "resolveTime": "1739194407"}, "host": {"ip": "*************"}, "type": "snmp_trap", "SNMPv2-SMI::enterprises.5528.100.11.15": "53", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.16": "350000"}, "output": {"s2110_occurrence": [{"event_type": "clear", "identifier": "24/*************/VOLTAGE - Battery Voltage/VOLTAGE"}]}}, {"input": {"snmptrap": {"description": "Value Too High", "errorType": "nbErrorType_toohigh", "resolveTime": "1739194407", "severity": "4", "startTime": "1739181444", "errorId": "nbErrorCond_30B8EDF6", "notifyTime": "1739194407", "location": "nbSNMPEnc7728578E", "sensorvalue1": "0.0", "hostname": "UPFOVOR02RIB (*************)", "responsible": "nbSNMPEnc7728578E_acinfail", "eventName": "VOLTAGE - INVERTER ACin FAIL"}, "@timestamp": "2025-02-10T13:33:32.292004715Z", "event": {"kafka": {"timestamp": "2025-02-10T13:33:32.392Z", "offset": 62803, "key": null, "partition": 0, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd"}, "uuid": "836b502f-3106-4da7-8a3b-7c43a6dbb26a", "logstash": {"instance_name": "iictmiapls016"}}, "message": "#<SNMP::SNMPv1_Trap:0x1a35bda2 @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x67780f0d @value=518486753>, @varbind_list=[#<SNMP::VarBind:0x349729b9 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_30B8EDF6\">, #<SNMP::VarBind:0x3784a24 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toohigh\">, #<SNMP::VarBind:0x24df3d21 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too High\">, #<SNMP::VarBind:0x36a96130 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_acinfail\">, #<SNMP::VarBind:0x3b7e5cbd @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - INVERTER ACin FAIL\">, #<SNMP::VarBind:0x7a16189c @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x27fd14ca @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x5a2041e4 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x137275a9 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x596a5da3 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x167e5900 @value=1739181444>>, #<SNMP::VarBind:0x4e89054a @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x4ed6ee0f @value=1739194407>>, #<SNMP::VarBind:0x45da3533 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x2108d2d1 @value=1739194407>>, #<SNMP::VarBind:0x27ad20ef @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x5e721483 @value=4>>, #<SNMP::VarBind:0x4a95c1d3 @name=[*******.4.1.5528.100.11.14], @value=\"0.0\">, #<SNMP::VarBind:0x2d6c1c0a @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x6388fb10 @value=0>>, #<SNMP::VarBind:0x2d903dee @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x496ffad6 @value=0>>], @specific_trap=102, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x42e33bd @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "0", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "host": {"ip": "*************"}, "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "type": "snmp_trap", "@version": "1"}, "output": {"s2110_occurrence": [{"event_type": "clear", "identifier": "24/*************/VOLTAGE - INVERTER ACin FAIL/VOLTAGE"}]}}, {"input": {"snmptrap": {"description": "Value Too High", "errorType": "nbErrorType_toohigh", "resolveTime": "1739194407", "severity": "4", "startTime": "1739181444", "errorId": "nbErrorCond_C655DF31", "notifyTime": "1739194407", "location": "nbSNMPEnc7728578E", "sensorvalue1": "0.0", "hostname": "UPFOVOR02RIB (*************)", "responsible": "nbSNMPEnc7728578E_acinfail", "eventName": "VOLTAGE - INVERTER ACin FAIL"}, "@timestamp": "2025-02-10T13:33:32.727161377Z", "event": {"kafka": {"timestamp": "2025-02-10T13:33:32.827Z", "offset": 62804, "key": null, "partition": 0, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd"}, "uuid": "3b74569e-8329-4a78-b1cd-7eeea58f8cde", "logstash": {"instance_name": "iictmiapls016"}}, "message": "#<SNMP::SNMPv1_Trap:0x666265cf @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x23efa351 @value=518486797>, @varbind_list=[#<SNMP::VarBind:0x137db317 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_C655DF31\">, #<SNMP::VarBind:0x6644e0aa @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toohigh\">, #<SNMP::VarBind:0x386f3218 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too High\">, #<SNMP::VarBind:0x25a42455 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_acinfail\">, #<SNMP::VarBind:0x5bbfdefd @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - INVERTER ACin FAIL\">, #<SNMP::VarBind:0x1abf2490 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x5b812a2d @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x5f833b84 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x7a6e4ebf @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x19166ec6 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x4b14ff8b @value=1739181444>>, #<SNMP::VarBind:0xd0da276 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x59e1ede @value=1739194407>>, #<SNMP::VarBind:0x2f5420f5 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x43d07a33 @value=1739194407>>, #<SNMP::VarBind:0x232e6e2d @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x56b3ec78 @value=4>>, #<SNMP::VarBind:0x58ee2dd3 @name=[*******.4.1.5528.100.11.14], @value=\"0.0\">, #<SNMP::VarBind:0x18afd51f @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x48a09f21 @value=0>>, #<SNMP::VarBind:0x1397a61 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x28105f20 @value=0>>], @specific_trap=102, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x4ef17010 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "0", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "host": {"ip": "*************"}, "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "type": "snmp_trap", "@version": "1"}, "output": {}, "comment": "No output expected as the event is already cleared"}, {"input": {"SNMPv2-SMI::enterprises.5528.100.11.8": "", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x2c1d45d0 @enterprise=[*******.4.1.5528.***********], @timestamp=#<SNMP::TimeTicks:0x106eefd9 @value=518487246>, @varbind_list=[#<SNMP::VarBind:0x3861fcb6 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_F930D518\">, #<SNMP::VarBind:0x7e140daf @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toolow\">, #<SNMP::VarBind:0x19633593 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too Low\">, #<SNMP::VarBind:0x3cd28468 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_3\">, #<SNMP::VarBind:0xb614b08 @name=[*******.4.1.5528.100.11.5], @value=\"VOLTAGE - Battery Voltage\">, #<SNMP::VarBind:0x55c646be @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEnc7728578E\">, #<SNMP::VarBind:0x55093e38 @name=[*******.4.1.5528.100.11.7], @value=\"UPFOVOR02RIB (*************)\">, #<SNMP::VarBind:0x7d501135 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x7c682c9c @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x1b7c6619 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x71873b0 @value=1739181806>>, #<SNMP::VarBind:0x7a3c13df @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x470dccc8 @value=1739194407>>, #<SNMP::VarBind:0x3f39369e @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x7b78573a @value=1739194407>>, #<SNMP::VarBind:0x61c01161 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x4802fa1d @value=4>>, #<SNMP::VarBind:0x3ef9a845 @name=[*******.4.1.5528.100.11.14], @value=\"53.4 V\">, #<SNMP::VarBind:0x781f927 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x66ababaf @value=53>>, #<SNMP::VarBind:0x39a66775 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x4a6e4b84 @value=350000>>], @specific_trap=103, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x422edb2f @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "event": {"kafka": {"offset": 62609, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "topic": "a1421-edison_supervision-struxureware-events-prd", "partition": 1, "key": null, "timestamp": "2025-02-10T13:33:37.322Z"}, "logstash": {"instance_name": "iictniapls015"}, "uuid": "14e48f3f-3db9-4357-a4bc-11c049ea4f36"}, "@timestamp": "2025-02-10T13:33:37.221970471Z", "snmptrap": {"sensorvalue1": "53.4 V", "severity": "4", "responsible": "nbSNMPEnc7728578E_DC_PWR_SYS_CONTROLLER_INPUT_3", "eventName": "VOLTAGE - Battery Voltage", "errorType": "nbErrorType_toolow", "startTime": "1739181806", "hostname": "UPFOVOR02RIB (*************)", "description": "Value Too Low", "location": "nbSNMPEnc7728578E", "errorId": "nbErrorCond_F930D518", "notifyTime": "1739194407", "resolveTime": "1739194407"}, "host": {"ip": "*************"}, "type": "snmp_trap", "SNMPv2-SMI::enterprises.5528.100.11.15": "53", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.16": "350000"}, "output": {}, "comment": "No output expected as the event is already cleared"}, {"input": {"@timestamp": "2025-02-13T01:42:52.967287189Z", "message": "#<SNMP::SNMPv1_Trap:0x38a7dcba @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x32e6f89b @value=540142821>, @varbind_list=[#<SNMP::VarBind:0x640678ac @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_3FB86DFB\">, #<SNMP::VarBind:0x62c22a9a @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toohigh\">, #<SNMP::VarBind:0x67652479 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too High\">, #<SNMP::VarBind:0x19824484 @name=[*******.4.1.5528.100.11.4], @value=\"nbModbusEncA8699245_HEARTBEAT\">, #<SNMP::VarBind:0x4e968c00 @name=[*******.4.1.5528.100.11.5], @value=\"Heartbeat\">, #<SNMP::VarBind:0x3d73e9b2 @name=[*******.4.1.5528.100.11.6], @value=\"nbModbusEncA8699245\">, #<SNMP::VarBind:0x73fb4489 @name=[*******.4.1.5528.100.11.7], @value=\"PDBRICO01(***********)\">, #<SNMP::VarBind:0x78d69816 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x2daed80 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x2046ba10 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x9b06d9d @value=1734009712>>, #<SNMP::VarBind:0x456a5295 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x10891155 @value=1739410972>>, #<SNMP::VarBind:0x50aaf6e @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0xf5ce6d7 @value=0>>, #<SNMP::VarBind:0x1835e353 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x40ee4ab9 @value=0>>, #<SNMP::VarBind:0x104fec15 @name=[*******.4.1.5528.100.11.14], @value=\"235.0\">, #<SNMP::VarBind:0x78cdca95 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x4fd591a4 @value=234>>, #<SNMP::VarBind:0x7d5ca165 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x129cc59f @value=959228>>], @specific_trap=2, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x4132c3b @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "234", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "SNMPv2-SMI::enterprises.5528.100.11.16": "959228", "type": "snmp_trap", "@version": "1", "snmptrap.description": "Value Too High", "snmptrap.errorType": "nbErrorType_toohigh", "snmptrap.resolveTime": "0", "snmptrap.severity": "0", "snmptrap.startTime": "1734009712", "snmptrap.errorId": "nbErrorCond_3FB86DFB", "snmptrap.notifyTime": "1739410972", "snmptrap.location": "nbModbusEncA8699245", "snmptrap.sensorvalue1": "235.0", "snmptrap.hostname": "PDBRICO01(***********)", "snmptrap.responsible": "nbModbusEncA8699245_HEARTBEAT", "snmptrap.eventName": "Heartbeat", "event.kafka.timestamp": "2025-02-13T01:42:53.068Z", "event.kafka.offset": 64044, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.uuid": "38fe71dc-c1e9-4296-8095-46a77b1d8790", "event.logstash.instance_name": "iictmiapls016", "host.ip": "*************"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "Struxurware_hb", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "Heartbeat", "clear_time": null, "severity": 1, "raise_time": "2025-02-13T01:42:52", "additional_data": {"delay": 130, "event_name": "Heartbeat"}}]}}, {"input": {"@timestamp": "2025-02-13T02:00:46.410159646Z", "message": "#<SNMP::SNMPv1_Trap:0x163c1b28 @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x498d27f8 @value=540250165>, @varbind_list=[#<SNMP::VarBind:0x29956c19 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_C47CBC20\">, #<SNMP::VarBind:0x5f86c758 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_errorstate\">, #<SNMP::VarBind:0x7f1499b7 @name=[*******.4.1.5528.100.11.3], @value=\"Value Error\">, #<SNMP::VarBind:0x5ad3bc07 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEncBA117106_battest1\">, #<SNMP::VarBind:0x5824e6e0 @name=[*******.4.1.5528.100.11.5], @value=\"SYSTEM - MINOR ALARM Battery test\">, #<SNMP::VarBind:0x31f156bb @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEncBA117106\">, #<SNMP::VarBind:0x6af3a813 @name=[*******.4.1.5528.100.11.7], @value=\"UPBRUNO03RIB(*************)\">, #<SNMP::VarBind:0x2d0702ce @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x526bf268 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x69943210 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x1e50ac01 @value=1739412045>>, #<SNMP::VarBind:0xb54dc77 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x6a187c7 @value=1739412045>>, #<SNMP::VarBind:0x4eed21b1 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x55c6acc9 @value=0>>, #<SNMP::VarBind:0x785c6562 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x72495a11 @value=2>>, #<SNMP::VarBind:0x550e1d40 @name=[*******.4.1.5528.100.11.14], @value=\"TRUE\">, #<SNMP::VarBind:0x354e43cc @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x2e224772 @value=1>>, #<SNMP::VarBind:0x16830975 @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x33c2f2f8 @value=0>>], @specific_trap=10, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x74d5976f @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "1", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "type": "snmp_trap", "@version": "1", "snmptrap.description": "Value Error", "snmptrap.errorType": "nbErrorType_errorstate", "snmptrap.resolveTime": "0", "snmptrap.severity": "2", "snmptrap.startTime": "1739412045", "snmptrap.errorId": "nbErrorCond_C47CBC20", "snmptrap.notifyTime": "1739412045", "snmptrap.location": "nbSNMPEncBA117106", "snmptrap.sensorvalue1": "TRUE", "snmptrap.hostname": "UPBRUNO03RIB(*************)", "snmptrap.responsible": "nbSNMPEncBA117106_battest1", "snmptrap.eventName": "SYSTEM - MINOR ALARM Battery test", "event.kafka.timestamp": "2025-02-13T02:00:46.516Z", "event.kafka.offset": 64309, "event.kafka.key": null, "event.kafka.partition": 1, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.uuid": "a1c95dfd-22d5-4b16-9dd8-d96756a9616d", "event.logstash.instance_name": "iictniapls015", "host.ip": "*************"}, "output": {"s2110_alarm": [{"ci_id": "UPBRUNO03RIB", "metric_name": "SYSTEM - MINOR", "metric_type": "/HardwareEvent/", "event_id": "nbErrorCond_C47CBC20", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "UPBRUNO03RIB", "metric_name": "SYSTEM - MINOR", "metric_type": "/HardwareEvent/", "summary": "SYSTEM - MINOR ALARM Battery test: TRUE.", "clear_time": null, "severity": 3, "raise_time": "2025-02-13T02:00:45", "additional_data": {"delay": 130, "event_name": "SYSTEM - MINOR ALARM Battery test"}}]}}, {"input": {"@timestamp": "2025-02-13T04:37:52.473719751Z", "message": "#<SNMP::SNMPv1_Trap:0x4bd3638b @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x4fec5c82 @value=541192772>, @varbind_list=[#<SNMP::VarBind:0x1001544e @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_A7108EE9\">, #<SNMP::VarBind:0x409eb2fe @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toohigh\">, #<SNMP::VarBind:0x6d3bee23 @name=[*******.4.1.5528.100.11.3], @value=\"Value Too High\">, #<SNMP::VarBind:0x724048c7 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEncFC0B9286_sysmaj\">, #<SNMP::VarBind:0x6879d1e5 @name=[*******.4.1.5528.100.11.5], @value=\"SYSTEM - MAJOR ALARM\">, #<SNMP::VarBind:0x5d459756 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEncFC0B9286\">, #<SNMP::VarBind:0x6fbd202b @name=[*******.4.1.5528.100.11.7], @value=\"UPLLOUS03RIA (*************)\">, #<SNMP::VarBind:0x1f4b4b6b @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x391b479e @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x489588cc @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x4e2f558d @value=1739421411>>, #<SNMP::VarBind:0xb7a4f30 @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x683ece76 @value=1739421471>>, #<SNMP::VarBind:0xc89e855 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x38be4774 @value=0>>, #<SNMP::VarBind:0x61e48c9c @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x7fb7b3a8 @value=3>>, #<SNMP::VarBind:0x3cbf374b @name=[*******.4.1.5528.100.11.14], @value=\"1.0\">, #<SNMP::VarBind:0x76f0424f @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x3335a061 @value=1>>, #<SNMP::VarBind:0x71dd7f5c @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x349d0b43 @value=0>>], @specific_trap=2, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x408bc5a5 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "1", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "type": "snmp_trap", "@version": "1", "snmptrap.description": "Value Too High", "snmptrap.errorType": "nbErrorType_toohigh", "snmptrap.resolveTime": "0", "snmptrap.severity": "3", "snmptrap.startTime": "1739421411", "snmptrap.errorId": "nbErrorCond_A7108EE9", "snmptrap.notifyTime": "1739421471", "snmptrap.location": "nbSNMPEncFC0B9286", "snmptrap.sensorvalue1": "1.0", "snmptrap.hostname": "UPLLOUS03RIA (*************)", "snmptrap.responsible": "nbSNMPEncFC0B9286_sysmaj", "snmptrap.eventName": "SYSTEM - MAJOR ALARM", "event.kafka.timestamp": "2025-02-13T04:37:52.574Z", "event.kafka.offset": 64078, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.uuid": "a40b7232-839a-453f-a192-ea1ff76a4f9a", "event.logstash.instance_name": "iictmiapls016", "host.ip": "*************"}, "output": {"s2110_alarm": [{"ci_id": "UPLLOUS03RIA", "metric_name": "SYSTEM - MAJOR", "metric_type": "/HardwareEvent/", "event_id": "nbErrorCond_A7108EE9", "actionable": null}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "UPLLOUS03RIA", "metric_name": "SYSTEM - MAJOR", "metric_type": "/HardwareEvent/", "summary": "SYSTEM - MAJOR ALARM: 1.0.", "clear_time": null, "severity": 4, "raise_time": "2025-02-13T04:37:51", "additional_data": {"delay": 0, "event_name": "SYSTEM - MAJOR ALARM"}}]}}, {"input": {"@timestamp": "2025-02-13T04:42:54.534251677Z", "message": "#<SNMP::SNMPv1_Trap:0x4d74a711 @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0xd84e648 @value=541222978>, @varbind_list=[#<SNMP::VarBind:0x69b65bdf @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_A7108EE9\">, #<SNMP::VarBind:0x19235d7 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_toohigh\">, #<SNMP::VarBind:0x2b8e250b @name=[*******.4.1.5528.100.11.3], @value=\"Value Too High\">, #<SNMP::VarBind:0x7db44ade @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEncFC0B9286_sysmaj\">, #<SNMP::VarBind:0x26079e14 @name=[*******.4.1.5528.100.11.5], @value=\"SYSTEM - MAJOR ALARM\">, #<SNMP::VarBind:0x36ab2b8e @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEncFC0B9286\">, #<SNMP::VarBind:0x6cd7e62d @name=[*******.4.1.5528.100.11.7], @value=\"UPLLOUS03RIA (*************)\">, #<SNMP::VarBind:0x336ab3d3 @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x29877c94 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x469fd571 @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x15aaa501 @value=1739421411>>, #<SNMP::VarBind:0x7338784c @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x6149c963 @value=1739421772>>, #<SNMP::VarBind:0x5945ac66 @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0x198788e2 @value=1739421773>>, #<SNMP::VarBind:0x29cd6986 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x6e0af0d @value=3>>, #<SNMP::VarBind:0x72b2052b @name=[*******.4.1.5528.100.11.14], @value=\"0.0\">, #<SNMP::VarBind:0x57574307 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x15148132 @value=0>>, #<SNMP::VarBind:0x160fdc5a @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x74f608ed @value=0>>], @specific_trap=102, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x19d4220f @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "0", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "type": "snmp_trap", "@version": "1", "snmptrap.description": "Value Too High", "snmptrap.errorType": "nbErrorType_toohigh", "snmptrap.resolveTime": "1739421773", "snmptrap.severity": "3", "snmptrap.startTime": "1739421411", "snmptrap.errorId": "nbErrorCond_A7108EE9", "snmptrap.notifyTime": "1739421772", "snmptrap.location": "nbSNMPEncFC0B9286", "snmptrap.sensorvalue1": "0.0", "snmptrap.hostname": "UPLLOUS03RIA (*************)", "snmptrap.responsible": "nbSNMPEncFC0B9286_sysmaj", "snmptrap.eventName": "SYSTEM - MAJOR ALARM", "event.kafka.timestamp": "2025-02-13T04:42:54.634Z", "event.kafka.offset": 64083, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.uuid": "02ea06e7-6d13-4860-8ef5-072c72497edc", "event.logstash.instance_name": "iictmiapls016", "host.ip": "*************"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "UPLLOUS03RIA", "metric_name": "SYSTEM - MAJOR", "metric_type": "/HardwareEvent/", "summary": "SYSTEM - MAJOR ALARM: 0.0.", "clear_time": "2025-02-13T04:42:53", "severity": 0, "raise_time": "2025-02-13T04:42:53", "additional_data": {"delay": 130, "event_name": "SYSTEM - MAJOR ALARM"}}]}}, {"input": {"@timestamp": "2025-02-13T06:00:40.313887015Z", "message": "#<SNMP::SNMPv1_Trap:0x3727d1de @enterprise=[*******.4.1.5528.100.10.2], @timestamp=#<SNMP::TimeTicks:0x1d3249ba @value=541689556>, @varbind_list=[#<SNMP::VarBind:0x56d5ae82 @name=[*******.4.1.5528.100.11.1], @value=\"nbErrorCond_C47CBC20\">, #<SNMP::VarBind:0x5f753ca7 @name=[*******.4.1.5528.100.11.2], @value=\"nbErrorType_errorstate\">, #<SNMP::VarBind:0x5085c16a @name=[*******.4.1.5528.100.11.3], @value=\"Value Error\">, #<SNMP::VarBind:0x21e5d2b5 @name=[*******.4.1.5528.100.11.4], @value=\"nbSNMPEncBA117106_battest1\">, #<SNMP::VarBind:0x5a3fd9b5 @name=[*******.4.1.5528.100.11.5], @value=\"SYSTEM - MINOR ALARM Battery test\">, #<SNMP::VarBind:0x2cd95651 @name=[*******.4.1.5528.100.11.6], @value=\"nbSNMPEncBA117106\">, #<SNMP::VarBind:0x52295c31 @name=[*******.4.1.5528.100.11.7], @value=\"UPBRUNO03RIB(*************)\">, #<SNMP::VarBind:0x5dd64cbb @name=[*******.4.1.5528.100.11.8], @value=\"\">, #<SNMP::VarBind:0x7900c4c5 @name=[*******.4.1.5528.100.11.9], @value=\"\">, #<SNMP::VarBind:0x1f0d7e6a @name=[*******.4.1.5528.100.11.10], @value=#<SNMP::Integer:0x75d616e @value=1739412045>>, #<SNMP::VarBind:0x680156aa @name=[*******.4.1.5528.100.11.11], @value=#<SNMP::Integer:0x17589183 @value=1739426439>>, #<SNMP::VarBind:0x63d2432c @name=[*******.4.1.5528.100.11.12], @value=#<SNMP::Integer:0xfe80829 @value=1739426439>>, #<SNMP::VarBind:0x6d3ebb78 @name=[*******.4.1.5528.100.11.13], @value=#<SNMP::Integer:0x3e39ebd5 @value=2>>, #<SNMP::VarBind:0x4f03141d @name=[*******.4.1.5528.100.11.14], @value=\"FALSE\">, #<SNMP::VarBind:0xe5f494 @name=[*******.4.1.5528.100.11.15], @value=#<SNMP::Integer:0x18986a8b @value=2>>, #<SNMP::VarBind:0x698e5d0e @name=[*******.4.1.5528.100.11.16], @value=#<SNMP::Integer:0x3e7a1fde @value=0>>], @specific_trap=110, @source_ip=\"*************\", @agent_addr=#<SNMP::IpAddress:0x11141dd1 @value=\"\\n\\xFC\\v\\xDD\">, @generic_trap=6>", "SNMPv2-SMI::enterprises.5528.100.11.9": "", "SNMPv2-SMI::enterprises.5528.100.11.15": "2", "SNMPv2-SMI::enterprises.5528.100.11.8": "", "SNMPv2-SMI::enterprises.5528.100.11.16": "0", "type": "snmp_trap", "@version": "1", "snmptrap.description": "Value Error", "snmptrap.errorType": "nbErrorType_errorstate", "snmptrap.resolveTime": "1739426439", "snmptrap.severity": "2", "snmptrap.startTime": "1739412045", "snmptrap.errorId": "nbErrorCond_C47CBC20", "snmptrap.notifyTime": "1739426439", "snmptrap.location": "nbSNMPEncBA117106", "snmptrap.sensorvalue1": "FALSE", "snmptrap.hostname": "UPBRUNO03RIB(*************)", "snmptrap.responsible": "nbSNMPEncBA117106_battest1", "snmptrap.eventName": "SYSTEM - MINOR ALARM Battery test", "event.kafka.timestamp": "2025-02-13T06:00:40.414Z", "event.kafka.offset": 64114, "event.kafka.key": null, "event.kafka.partition": 0, "event.kafka.consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd", "event.kafka.topic": "a1421-edison_supervision-struxureware-events-prd", "event.uuid": "b19709f6-8b00-47d2-9c57-b165251ade44", "event.logstash.instance_name": "iictmiapls016", "host.ip": "*************"}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "UPBRUNO03RIB", "metric_name": "SYSTEM - MINOR", "metric_type": "/HardwareEvent/", "summary": "SYSTEM - MINOR ALARM Battery test: FALSE.", "clear_time": "2025-02-13T06:00:39", "severity": 0, "raise_time": "2025-02-13T06:00:39", "additional_data": {"delay": 130, "event_name": "SYSTEM - MINOR ALARM Battery test"}}]}}, {"input": {"snmptrap": {"location": "nbSNMPEnc31C70ED0", "errorType": "nbErrorType_toohigh", "startTime": 1745072243, "responsible": "nbSNMPEnc31C70ED0_sysmin", "eventName": "SYSTEM - MINOR ALARM", "errorId": "nbErrorCond_1AC11AE9", "resolveTime": 0, "notifyTime": 1745568624, "hostname": "UPMONSX01RI (**************)", "sensorvalue1": "1.0", "severity": 2, "description": "Value Too High"}, "SNMPv2-SMI::enterprises.5528.100.11.8": "", "host": {"ip": "*************"}, "SNMPv2-SMI::enterprises.5528.100.11.9": "", "type": "snmp_trap", "message": "{\"agent_addr\":\"*************\",\"generic_trap\":6,\"specific_trap\":2,\"enterprise\":\"*******.4.1.5528.100.10.2\",\"variable_bindings\":{\"*******.4.1.5528.100.11.4\":\"nbSNMPEnc31C70ED0_sysmin\",\"*******.4.1.5528.100.11.3\":\"Value Too High\",\"*******.4.1.5528.100.11.2\":\"nbErrorType_toohigh\",\"*******.4.1.5528.100.11.1\":\"nbErrorCond_1AC11AE9\",\"*******.4.1.5528.100.11.11\":1745568624,\"*******.4.1.5528.100.11.10\":1745072243,\"*******.4.1.5528.100.11.13\":2,\"*******.4.1.5528.100.11.9\":\"\",\"*******.4.1.5528.100.11.12\":0,\"*******.4.1.5528.100.11.8\":\"\",\"*******.4.1.5528.100.11.7\":\"UPMONSX01RI (**************)\",\"*******.4.1.5528.100.11.6\":\"nbSNMPEnc31C70ED0\",\"*******.4.1.5528.100.11.5\":\"SYSTEM - MINOR ALARM\",\"*******.4.1.5528.100.11.15\":1,\"*******.4.1.5528.100.11.14\":\"1.0\",\"*******.4.1.5528.100.11.16\":0},\"type\":\"V1TRAP\",\"community\":\"public\",\"version\":\"1\",\"timestamp\":371300367}", "SNMPv2-SMI::enterprises.5528.100.11.15": 1, "@version": "1", "@timestamp": "2025-04-25T08:10:25.425770349Z", "event": {"logstash": {"instance_name": "iictniapls016"}, "kafka": {"timestamp": "2025-04-25T08:10:25.526Z", "key": null, "offset": 98360, "topic": "a1421-edison_supervision-struxureware-events-prd", "partition": 2, "consumer_group": "a1559-logstash-a1421-edison_supervision-struxureware-events-prd"}, "uuid": "9d7ca79d-e042-4b95-af3d-b33d0e8b4683"}, "SNMPv2-SMI::enterprises.5528.100.11.16": 0}, "output": {"s2110_alarm": [{}], "s2110_occurrence": [{}]}}]}