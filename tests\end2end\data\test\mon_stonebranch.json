{"agent_names": ["Stonebranch"], "data": [{"input": {"@timestamp": "2025-02-13T11:30:35.217909219Z", "tags": ["opsTaskObjects"], "host": "***********", "type": "snmp_trap", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x721c3563 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x16474652 @value=614467140>, @varbind_list=[#<SNMP::VarBind:0x4a55fa9 @name=[*******.4.1.88898.*******.300.1.1], @value=\"2\">, #<SNMP::VarBind:0x1701c573 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JPM500D603\">, #<SNMP::VarBind:0x3b586417 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Universal\">, #<SNMP::VarBind:0x145c8d2e @name=[*******.4.1.88898.*******.300.1.4], @value=\"1739443587065577052LLMEECJI7U2FV\">, #<SNMP::VarBind:0x3a2ac01b @name=[*******.4.1.88898.*******.300.1.5], @value=\"RUNNING\">, #<SNMP::VarBind:0x2319b070 @name=[*******.4.1.88898.*******.300.1.6], @value=\"hrrlyiaplv007-a1445\">], @specific_trap=1, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x5b9910d8 @value=\"\\n\\xFEJ2\">, @generic_trap=6>", "snmptrap.opsTaskSeverity": "2", "snmptrap.opsTaskStatusCode": "RUNNING", "snmptrap.opsTaskName": "JPM500D603", "snmptrap.opsTaskExecId": "1739443587065577052LLMEECJI7U2FV", "snmptrap.opsTaskAgent": "hrrlyiaplv007-a1445", "snmptrap.opsTaskType": "Universal", "event.kafka.offset": 10497219, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "event.kafka.topic": "a2005-stonebranch_monitoring-events-prd", "event.kafka.partition": 1, "event.kafka.key": null, "event.kafka.timestamp": "2025-02-13T11:30:35.318Z", "event.logstash.instance_name": "iictniapls015", "event.uuid": "af7e98da-b908-45b6-8cf3-e85b7a107444", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorType": null, "snmptrap.opsConnectorName": null}, "output": {"s2110_alarm": [{"ci_id": "JPM500D603", "metric_name": "Universal", "metric_type": "/Scheduling/", "event_id": null, "actionable": false}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "JPM500D603", "metric_name": "Universal", "metric_type": "/Scheduling/", "summary": "JPM500D603 type Universal\nRUNNING on hrrlyiaplv007-a1445", "clear_time": null, "severity": 2, "raise_time": "2025-02-13T11:30:35.217909", "additional_data": {"exec_id": "1739443587065577052LLMEECJI7U2FV"}}]}}, {"input": {"@timestamp": "2025-02-13T12:53:30.267779586Z", "tags": ["opsTaskObjects"], "host": "***********", "type": "snmp_trap", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x76458006 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x48232535 @value=619442190>, @varbind_list=[#<SNMP::VarBind:0x558b8614 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0xf146913 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JPM500D603\">, #<SNMP::VarBind:0x5e4fef49 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Universal\">, #<SNMP::VarBind:0x4b4b14ac @name=[*******.4.1.88898.*******.300.1.4], @value=\"1739443587065577052LLMEECJI7U2FV\">, #<SNMP::VarBind:0x427990f7 @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x6e89355a @name=[*******.4.1.88898.*******.300.1.6], @value=\"hrrlyiaplv007-a1445\">], @specific_trap=1, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x4f9dba65 @value=\"\\n\\xFEJ2\">, @generic_trap=6>", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskName": "JPM500D603", "snmptrap.opsTaskExecId": "1739443587065577052LLMEECJI7U2FV", "snmptrap.opsTaskAgent": "hrrlyiaplv007-a1445", "snmptrap.opsTaskType": "Universal", "event.kafka.offset": 10520595, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "event.kafka.topic": "a2005-stonebranch_monitoring-events-prd", "event.kafka.partition": 0, "event.kafka.key": null, "event.kafka.timestamp": "2025-02-13T12:53:30.368Z", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "c4cfa159-fc2d-4a11-a586-b0d6bd240c4a", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorType": null, "snmptrap.opsConnectorName": null}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "JPM500D603", "metric_name": "Universal", "metric_type": "/Scheduling/", "summary": "JPM500D603 type Universal\nSUCCESS on hrrlyiaplv007-a1445", "clear_time": "2025-02-13T12:53:30.267779", "severity": 1, "raise_time": "2025-02-13T12:53:30.267779", "additional_data": {"exec_id": "1739443587065577052LLMEECJI7U2FV"}}]}}, {"input": {"@timestamp": "2025-02-14T00:33:25.569563574Z", "tags": ["opsTaskObjects"], "host": "***********", "type": "snmp_trap", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x64367c4a @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x31fb7350 @value=661437491>, @varbind_list=[#<SNMP::VarBind:0x65682305 @name=[*******.4.1.88898.*******.300.1.1], @value=\"1\">, #<SNMP::VarBind:0x326bc7ce @name=[*******.4.1.88898.*******.300.1.2], @value=\"JPM500D603\">, #<SNMP::VarBind:0x58605320 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Universal\">, #<SNMP::VarBind:0x1da63b78 @name=[*******.4.1.88898.*******.300.1.4], @value=\"1739491930430022052B8IZBP2SSFTJM\">, #<SNMP::VarBind:0x397253c2 @name=[*******.4.1.88898.*******.300.1.5], @value=\"SUCCESS\">, #<SNMP::VarBind:0x554bb695 @name=[*******.4.1.88898.*******.300.1.6], @value=\"hrrlyiaplv007-a1445\">], @specific_trap=1, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0xfe8164f @value=\"\\n\\xFEJ2\">, @generic_trap=6>", "snmptrap.opsTaskSeverity": "1", "snmptrap.opsTaskStatusCode": "SUCCESS", "snmptrap.opsTaskName": "JPM500D603", "snmptrap.opsTaskExecId": "1739491930430022052B8IZBP2SSFTJM", "snmptrap.opsTaskAgent": "hrrlyiaplv007-a1445", "snmptrap.opsTaskType": "Universal", "event.kafka.offset": 10537284, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "event.kafka.topic": "a2005-stonebranch_monitoring-events-prd", "event.kafka.partition": 2, "event.kafka.key": null, "event.kafka.timestamp": "2025-02-14T00:33:25.670Z", "event.logstash.instance_name": "iictniapls016", "event.uuid": "23b99ff6-2fbd-49b4-9892-9a6c10848f27", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorType": null, "snmptrap.opsConnectorName": null}, "output": {"s2110_occurrence": [{"event_type": "clear", "ci_id": "JPM500D603", "metric_name": "Universal", "metric_type": "/Scheduling/", "summary": "JPM500D603 type Universal\nSUCCESS on hrrlyiaplv007-a1445", "clear_time": "2025-02-14T00:33:25.569563", "severity": 1, "raise_time": "2025-02-14T00:33:25.569563", "additional_data": {"exec_id": "1739491930430022052B8IZBP2SSFTJM"}}]}}, {"input": {"@timestamp": "2025-02-14T00:11:59.289435940Z", "tags": ["opsConnectorObjects"], "host": "***********", "type": "snmp_trap", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x53179121 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x30a36ee4 @value=660151210>, @varbind_list=[#<SNMP::VarBind:0x6921544 @name=[*******.4.1.88898.*******.300.2.1], @value=\"5\">, #<SNMP::VarBind:0x69e3122e @name=[*******.4.1.88898.*******.300.2.2], @value=\"iictyiaplv183:7878,iictziaplv191:7878\">, #<SNMP::VarBind:0x429c95b8 @name=[*******.4.1.88898.*******.300.2.3], @value=\"OMS Server\">, #<SNMP::VarBind:0x5bdeca79 @name=[*******.4.1.88898.*******.300.2.4], @value=\"Disconnected\">], @specific_trap=2, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x4d70dc73 @value=\"\\n\\xFEJ2\">, @generic_trap=6>", "snmptrap.opsTaskSeverity": null, "snmptrap.opsTaskStatusCode": null, "snmptrap.opsTaskName": null, "snmptrap.opsTaskExecId": null, "snmptrap.opsTaskAgent": null, "snmptrap.opsTaskType": null, "event.kafka.offset": 10535029, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "event.kafka.topic": "a2005-stonebranch_monitoring-events-prd", "event.kafka.partition": 1, "event.kafka.key": null, "event.kafka.timestamp": "2025-02-14T00:11:59.389Z", "event.logstash.instance_name": "iictniapls015", "event.uuid": "4c75610c-cd79-4b41-9b85-8c53c8487cf8", "snmptrap.opsConnectorSeverity": "5", "snmptrap.opsConnectorMode": "Disconnected", "snmptrap.opsConnectorType": "OMS Server", "snmptrap.opsConnectorName": "iictyiaplv183:7878,iictziaplv191:7878"}, "output": {"s2110_alarm": [{"ci_id": "iictyiaplv183", "metric_name": null, "metric_type": "/Scheduling/", "event_id": null, "actionable": true}], "s2110_occurrence": [{"event_type": "problem", "ci_id": "iictyiaplv183", "metric_name": null, "metric_type": "/Scheduling/", "summary": "iictyiaplv183:7878,iictziaplv191:7878 type OMS Server Disconnected", "clear_time": null, "severity": 1, "additional_data": {"exec_id": "Not use"}}]}}, {"input": {"@timestamp": "2025-02-14T00:12:04.333866988Z", "tags": ["opsConnectorObjects"], "host": "***********", "type": "snmp_trap", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x151406f6 @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x730fcd04 @value=660156256>, @varbind_list=[#<SNMP::VarBind:0x33e5e855 @name=[*******.4.1.88898.*******.300.2.1], @value=\"1\">, #<SNMP::VarBind:0x6f36b69 @name=[*******.4.1.88898.*******.300.2.2], @value=\"iictyiaplv183:7878,iictziaplv191:7878\">, #<SNMP::VarBind:0xb19f15e @name=[*******.4.1.88898.*******.300.2.3], @value=\"OMS Server\">, #<SNMP::VarBind:0x76e1b15a @name=[*******.4.1.88898.*******.300.2.4], @value=\"Connected\">], @specific_trap=2, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x6ada4893 @value=\"\\n\\xFEJ2\">, @generic_trap=6>", "snmptrap.opsTaskSeverity": null, "snmptrap.opsTaskStatusCode": null, "snmptrap.opsTaskName": null, "snmptrap.opsTaskExecId": null, "snmptrap.opsTaskAgent": null, "snmptrap.opsTaskType": null, "event.kafka.offset": 10535033, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "event.kafka.topic": "a2005-stonebranch_monitoring-events-prd", "event.kafka.partition": 1, "event.kafka.key": null, "event.kafka.timestamp": "2025-02-14T00:12:04.434Z", "event.logstash.instance_name": "iictniapls015", "event.uuid": "411b3cf1-d4ae-422e-9b72-3424fd41aff5", "snmptrap.opsConnectorSeverity": "1", "snmptrap.opsConnectorMode": "Connected", "snmptrap.opsConnectorType": "OMS Server", "snmptrap.opsConnectorName": "iictyiaplv183:7878,iictziaplv191:7878"}, "output": {"s2110_occurrence": [{"event_type": "problem", "ci_id": "iictyiaplv183", "metric_name": null, "metric_type": "/Scheduling/", "summary": "iictyiaplv183:7878,iictziaplv191:7878 type OMS Server Connected", "clear_time": null, "severity": 1, "additional_data": {"exec_id": "Not use"}}]}, "comment": "Should this not be a clear?"}, {"input": {"@timestamp": "2025-02-14T00:12:17.702271445Z", "tags": ["opsTaskObjects"], "host": "***********", "type": "snmp_trap", "@version": "1", "message": "#<SNMP::SNMPv1_Trap:0x73c8b78d @enterprise=[*******.4.1.88898.*******.301], @timestamp=#<SNMP::TimeTicks:0x71685aeb @value=660169059>, @varbind_list=[#<SNMP::VarBind:0x5c2cd184 @name=[*******.4.1.88898.*******.300.1.1], @value=\"4\">, #<SNMP::VarBind:0x6a6e6153 @name=[*******.4.1.88898.*******.300.1.2], @value=\"JPM500D603\">, #<SNMP::VarBind:0x59e2c1f7 @name=[*******.4.1.88898.*******.300.1.3], @value=\"Universal\">, #<SNMP::VarBind:0xc382c3f @name=[*******.4.1.88898.*******.300.1.4], @value=\"1739491930430022052B8IZBP2SSFTJM\">, #<SNMP::VarBind:0x36227ccd @name=[*******.4.1.88898.*******.300.1.5], @value=\"UNDELIVERABLE\">, #<SNMP::VarBind:0x2fdff7a3 @name=[*******.4.1.88898.*******.300.1.6], @value=\"hrrlbiaplv001-a1445\">], @specific_trap=1, @source_ip=\"***********\", @agent_addr=#<SNMP::IpAddress:0x60fd66d6 @value=\"\\n\\xFEJ2\">, @generic_trap=6>", "snmptrap.opsTaskSeverity": "4", "snmptrap.opsTaskStatusCode": "UNDELIVERABLE", "snmptrap.opsTaskName": "JPM500D603", "snmptrap.opsTaskExecId": "1739491930430022052B8IZBP2SSFTJM", "snmptrap.opsTaskAgent": "hrrlbiaplv001-a1445", "snmptrap.opsTaskType": "Universal", "event.kafka.offset": 10555344, "event.kafka.consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "event.kafka.topic": "a2005-stonebranch_monitoring-events-prd", "event.kafka.partition": 0, "event.kafka.key": null, "event.kafka.timestamp": "2025-02-14T00:12:17.802Z", "event.logstash.instance_name": "iictmiapls016", "event.uuid": "bf6dc0ad-cd87-473b-a39d-b7f7741d4912", "snmptrap.opsConnectorSeverity": null, "snmptrap.opsConnectorMode": null, "snmptrap.opsConnectorType": null, "snmptrap.opsConnectorName": null}, "output": {"s2110_occurrence": [{"event_type": "problem", "ci_id": "JPM500D603", "metric_name": "Universal", "metric_type": "/Scheduling/", "summary": "JPM500D603 type Universal\nUNDELIVERABLE on hrrlbiaplv001-a1445", "clear_time": null, "severity": 4, "additional_data": {"exec_id": "1739491930430022052B8IZBP2SSFTJM"}}]}}, {"input": {"snmptrap": {"opsTaskAgent": "n/a", "opsTaskStatusCode": "SUCCESS", "opsTaskExecId": "1743141303284596544O0506N4AJTDVC", "opsTaskType": "Timer", "opsTaskSeverity": "1", "opsTaskName": "A2005_C0000_HeartbeatUAC"}, "tags": ["opsTaskObjects"], "host": "***********", "type": "snmp_trap", "event": {"uuid": "d47f89dd-2419-4706-9eae-28894f040f39", "kafka": {"timestamp": "2025-03-28T09:05:21.311Z", "offset": 13563938, "partition": 0, "topic": "a2005-stonebranch_monitoring-events-prd", "key": null, "consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd"}, "logstash": {"instance_name": "iictmiapls016"}}, "@timestamp": "2025-03-28T09:05:21.211085908Z", "message": "{\"agent_addr\":\"************\",\"generic_trap\":6,\"specific_trap\":1,\"enterprise\":\"*******.4.1.88898.*******.301\",\"variable_bindings\":{\"*******.4.1.88898.*******.300.1.5\":\"SUCCESS\",\"*******.4.1.88898.*******.300.1.6\":\"n/a\",\"*******.4.1.88898.*******.300.1.3\":\"Timer\",\"*******.4.1.88898.*******.300.1.4\":\"1743141303284596544O0506N4AJTDVC\",\"*******.4.1.88898.*******.300.1.1\":\"1\",\"*******.4.1.88898.*******.300.1.2\":\"A2005_C0000_HeartbeatUAC\"},\"type\":\"V1TRAP\",\"community\":\"public\",\"version\":\"1\",\"timestamp\":79289026}", "@version": "1"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "A2005_C0000_HeartbeatUAC_PRD-BUILD", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "A2005_C0000_HeartbeatUAC type Timer SUCCESS", "clear_time": null, "severity": 1, "additional_data": {"exec_id": "1743141303284596544O0506N4AJTDVC"}}]}, "comment": "Heartbeat for A2005_C0000_HeartbeatUAC_PRD-BUILD"}, {"input": {"snmptrap": {"opsTaskAgent": "n/a", "opsTaskStatusCode": "SUCCESS", "opsTaskExecId": "1743152726886571524IMYB6UMK2WDTB", "opsTaskType": "Timer", "opsTaskSeverity": "1", "opsTaskName": "A2005_C0000_HeartbeatUAC"}, "tags": ["opsTaskObjects"], "host": "***********", "type": "snmp_trap", "event": {"uuid": "f9139af4-26f0-4a1a-9ab7-d0b52f6638c4", "kafka": {"timestamp": "2025-03-28T09:05:41.421Z", "offset": 13563977, "partition": 0, "topic": "a2005-stonebranch_monitoring-events-prd", "key": null, "consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd"}, "logstash": {"instance_name": "iictmiapls016"}}, "@timestamp": "2025-03-28T09:05:41.316826968Z", "message": "{\"agent_addr\":\"************\",\"generic_trap\":6,\"specific_trap\":1,\"enterprise\":\"*******.4.1.88898.*******.301\",\"variable_bindings\":{\"*******.4.1.88898.*******.300.1.5\":\"SUCCESS\",\"*******.4.1.88898.*******.300.1.6\":\"n/a\",\"*******.4.1.88898.*******.300.1.3\":\"Timer\",\"*******.4.1.88898.*******.300.1.4\":\"1743152726886571524IMYB6UMK2WDTB\",\"*******.4.1.88898.*******.300.1.1\":\"1\",\"*******.4.1.88898.*******.300.1.2\":\"A2005_C0000_HeartbeatUAC\"},\"type\":\"V1TRAP\",\"community\":\"public\",\"version\":\"1\",\"timestamp\":79323594}", "@version": "1"}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "ci_id": "A2005_C0000_HeartbeatUAC_PRD-RUN", "metric_name": "Heartbeat", "metric_type": "/ApplicationEvent/", "summary": "A2005_C0000_HeartbeatUAC type Timer SUCCESS", "severity": 1, "clear_time": null, "additional_data": {"exec_id": "1743152726886571524IMYB6UMK2WDTB"}}]}, "comment": "Heartbeat for A2005_C0000_HeartbeatUAC_PRD-RUN"}, {"input": {"@timestamp": "2025-03-28T09:09:35.321576442Z", "type": "snmp_trap", "event": {"logstash": {"instance_name": "iictniapls015"}, "uuid": "ee970804-5e26-445c-af3f-18ea2515a338", "kafka": {"topic": "a2005-stonebranch_monitoring-events-prd", "consumer_group": "a1559-logstash-a2005-stonebranch_monitoring-events-prd", "timestamp": "2025-03-28T09:09:35.423Z", "offset": 13537358, "key": null, "partition": 1}}, "host": "***********", "tags": ["opsTaskObjects"], "@version": "1", "message": "{\"agent_addr\":\"************\",\"generic_trap\":6,\"specific_trap\":1,\"enterprise\":\"*******.4.1.88898.*******.301\",\"variable_bindings\":{\"*******.4.1.88898.*******.300.1.5\":\"SUCCESS\",\"*******.4.1.88898.*******.300.1.6\":\"n/a\",\"*******.4.1.88898.*******.300.1.3\":\"Timer\",\"*******.4.1.88898.*******.300.1.4\":\"1743152880166589052DNBW42X9T1J4Z\",\"*******.4.1.88898.*******.300.1.1\":\"1\",\"*******.4.1.88898.*******.300.1.2\":\"DPL2005D000\"},\"type\":\"V1TRAP\",\"community\":\"public\",\"version\":\"1\",\"timestamp\":79573963}", "snmptrap": {"opsTaskAgent": "n/a", "opsTaskSeverity": "1", "opsTaskStatusCode": "SUCCESS", "opsTaskType": "Timer", "opsTaskExecId": "1743152880166589052DNBW42X9T1J4Z", "opsTaskName": "DPL2005D000"}}, "output": {"s2110_occurrence": [{"event_type": "heartbeat", "metric_type": "/ApplicationEvent/", "metric_name": "Heartbeat", "ci_id": "DPL2005D000", "summary": "StoneBranch HeartBeat Alarm", "severity": 1, "clear_time": null, "additional_data": {"exec_id": "1743152880166589052DNBW42X9T1J4Z"}}]}, "comment": "Heartbeat for DPL2005D000"}]}