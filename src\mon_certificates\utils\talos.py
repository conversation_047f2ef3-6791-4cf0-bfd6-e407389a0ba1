"""Talos module for mon-certificates.

This file contains all the helper functions for talos certificates.
"""


def extract_email(app_details: dict, a_code: str, col_name: str) -> str:
    """Extract the project leader from the uCMDB response containing the applications details."""
    for app in app_details:
        if app["identification"] == a_code:
            if app[col_name]:
                contact = app[col_name][0]["email"]
            else:
                contact = "N/A"
            break
    else:
        return "N/A"
    return contact if contact else "N/A"
