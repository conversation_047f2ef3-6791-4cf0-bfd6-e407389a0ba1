"""Configuration module for mon-zabbix."""

from dataclasses import dataclass

from olympus_common.config import DatabaseKafkaConsumerServiceConfig
from olympus_common.dataclass import env_field
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class Config(DatabaseKafkaConsumerServiceConfig, metaclass=Singleton):
    """Represent the configuration for the Zabbix DDs."""

    zabbix_cc: str = env_field("ZABBIX_CC")


config = Config()  # Create the singleton instance at import time.
