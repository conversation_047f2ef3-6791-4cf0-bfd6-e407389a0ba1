"""Entrypoint for heartbeat_jobs."""

import logging
from datetime import datetime

from sqlalchemy import and_, not_

from heartbeat_job.config import config
from olympus_common import db, defaults, enums, utils
from olympus_common.core import Application
from olympus_common.datareaders import DatabaseReader
from olympus_common.datawriters import DatabaseWriter
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType


@trace_scan(MeasureType.CUSTOM.value)
def _init_application():
    session = db.create_session(config.service_name)
    datareader = DatabaseReader(session, "agent_heartbeat")
    datawriter = DatabaseWriter(session, db.Occurrence)
    logger = defaults.get_logger(config.debug, config.logger_config)
    return Application(datareader=datareader, datawriter=datawriter, logger=logger, backend_db_session=session)


application = _init_application()


@application.run_forever(sleep_time=config.sleep_time)
@trace_scan(MeasureType.CUSTOM.value)
def main(data: list[dict]) -> list[dict]:
    """Process all heartbeats."""
    processed_data = []
    for heartbeat in data:
        occurrence = process(heartbeat)
        if occurrence:
            processed_data.append(occurrence)
    return processed_data


@trace_scan(MeasureType.CUSTOM.value)
def process(heartbeat: dict) -> dict | None:
    """Process a single heartbeat dict.

    For heartbeats with type "period" we get the last heartbeat and raise an alarm if we didn't receive a heartbeat for
    the configured period.
    When the type is "idle_time" we get the last occurrence and raise an alarm if we didn't receive any occurrences for
    the configured period.
    """
    agentheartbeat = db.AgentHeartbeat.from_dict(heartbeat)
    resultdata: dict | None = None

    active_alarm = get_active_heartbeat_alarm(agentheartbeat)

    match agentheartbeat.heartbeat_type:
        case enums.HeartbeatType.PERIOD.value:
            # 1. A heartbeat is sent periodically by the EMS. So, if no heartbeat is received since the usual period, an
            # alarm should be raised.
            if last_raise_time := is_missing_heartbeat(agentheartbeat):
                if not active_alarm:
                    resultdata = from_agentheartbeat(agentheartbeat, enums.AlarmType.PROBLEM.value, last_raise_time)
            else:
                if active_alarm:
                    resultdata = from_agentheartbeat(agentheartbeat, enums.AlarmType.RESOLUTION.value)

        case enums.HeartbeatType.IDLE_TIME.value:
            # idle_time means that we configure an amount of seconds in which we allow the EMS to not send alarms. If
            # this idle_time is exceeded, an alarm is raised.
            if last_raise_time := is_missing_alarms(agentheartbeat):
                if not active_alarm:
                    resultdata = from_agentheartbeat(agentheartbeat, enums.AlarmType.PROBLEM.value, last_raise_time)
            else:
                if active_alarm and (active_alarm.last_occurrence_id != active_alarm.last_clear_id):
                    # We only send a clear if the alarm is still active and uncleared.
                    resultdata = from_agentheartbeat(agentheartbeat, enums.AlarmType.RESOLUTION.value)
        case _:
            logging.warning(f"Unknown heartbeat type: {agentheartbeat.heartbeat_type}")

    return resultdata


@trace_scan(MeasureType.CUSTOM.value)
def get_last_occurrence(agentheartbeat: db.AgentHeartbeat) -> db.Occurrence | None:
    """Get the last occurrence from the backend_db.

    We exclude the alarms raised by the heartbeat job itself.
    """
    return (
        application.backend_db_session.query(db.Occurrence)
        .filter(db.Occurrence.agent_id == agentheartbeat.agent_id)
        .filter(
            and_(
                not_(
                    and_(
                        db.Occurrence.metric_name == agentheartbeat.metric_name,
                        db.Occurrence.metric_type == agentheartbeat.metric_type,
                        db.Occurrence.ci_id == agentheartbeat.ci_id,
                    )
                ),
                db.Occurrence.event_type.in_([enums.AlarmType.PROBLEM.value, enums.AlarmType.RESOLUTION.value]),
            )
        )
        .order_by(db.Occurrence.raise_time.desc())
        .first()
    )


@trace_scan(MeasureType.CUSTOM.value)
def get_active_heartbeat_alarm(agentheartbeat: db.AgentHeartbeat) -> db.Alarm | None:
    """Get the active alarm on a specific agent heartbeat."""
    return (
        application.backend_db_session.query(db.Alarm)
        .filter_by(
            is_active=True,
            agent_id=agentheartbeat.agent_id,
            ci_id=agentheartbeat.ci_id,
            metric_name=agentheartbeat.metric_name,
            metric_type=agentheartbeat.metric_type,
        )
        .first()
    )


@trace_scan(MeasureType.CUSTOM.value)
def get_last_heartbeat(agentheartbeat: db.AgentHeartbeat) -> db.Occurrence | None:
    """Get the last heartbeat from the backend_db for the specific agentheartbeat."""
    return (
        application.backend_db_session.query(db.Occurrence)
        .filter_by(
            agent_id=agentheartbeat.agent_id,
            ci_id=agentheartbeat.ci_id,
            event_type=enums.AlarmType.HEARTBEAT.value,
            metric_type=agentheartbeat.metric_type,
            metric_name=agentheartbeat.metric_name,
        )
        .order_by(db.Occurrence.raise_time.desc())
        .first()
    )


def is_missing_heartbeat(agentheartbeat: db.AgentHeartbeat) -> datetime | None:
    """Determine if there is a missing heartbeat and give the last heartbeat time."""
    last_hearbeat_occurrence = get_last_heartbeat(agentheartbeat)
    if last_hearbeat_occurrence is None or not last_hearbeat_occurrence.raise_time:
        # We do nothing if no last_occurrence or if no raise_time was found.
        return None
    seconds_since_last_heartbeat = abs((utils.now_naive() - last_hearbeat_occurrence.raise_time).total_seconds())
    if agentheartbeat.period_seconds and seconds_since_last_heartbeat > agentheartbeat.period_seconds:
        return last_hearbeat_occurrence.raise_time

    return None


def is_missing_alarms(agentheartbeat: db.AgentHeartbeat) -> datetime | None:
    """Determine if there have been no occurrence for some time and give the last occurrence time."""
    last_occurrence = get_last_occurrence(agentheartbeat)
    if not last_occurrence or not last_occurrence.raise_time:
        # We do nothing if no last_occurrence or if no raise_time was found.
        return None
    seconds_since_last_occurrence = abs((utils.now_naive() - last_occurrence.raise_time).total_seconds())
    if agentheartbeat.idle_time_seconds and seconds_since_last_occurrence > agentheartbeat.idle_time_seconds:
        # Problem as our last_occurrence was sent longer ago than the configured idle_time.
        return last_occurrence.raise_time

    return None


def find_agent_heartbeat(agent_id: int, ci_id: str, agentheartbeats: list[dict]) -> db.AgentHeartbeat | None:
    """Find the agent heartbeat corresponding to an agent and CI from a list."""
    for heartbeat in agentheartbeats:
        if heartbeat["agent_id"] == agent_id and heartbeat["ci_id"] == ci_id:
            return db.AgentHeartbeat.from_dict(heartbeat)

    return None


def from_agentheartbeat(
    agent_heartbeat: db.AgentHeartbeat, event_type: str, last_heartbeat_time: datetime | None = None
) -> dict:
    """Construct a dictionary that can be passed to datawriters."""
    if event_type == enums.AlarmType.PROBLEM.value:
        if not last_heartbeat_time:
            raise ValueError("A last_heartbeat_time is expected to generate an alarm.")

        summary = (
            f"{agent_heartbeat.agent.name} - {agent_heartbeat.ci_id} : No heartbeat since "
            + f"{datetime.strftime(last_heartbeat_time, '%d-%m-%Y %H:%M:%S')}"
        )
    else:
        summary = f"{agent_heartbeat.agent.name} - {agent_heartbeat.ci_id} : Heartbeat received"

    current_time = utils.now_naive()
    return {
        "summary": summary,
        "severity": (
            enums.Severity.CLEARED.value
            if event_type == enums.AlarmType.RESOLUTION.value
            else enums.Severity.CRITICAL.value
        ),
        "event_type": event_type,
        "raise_time": current_time,
        "wake_up_time": current_time,
        "handle_time": current_time,
        "clear_time": current_time if event_type == enums.AlarmType.RESOLUTION.value else None,
        "ci_id": agent_heartbeat.ci_id,
        "agent_id": agent_heartbeat.agent_id,
        "metric_type": agent_heartbeat.metric_type,
        "metric_name": agent_heartbeat.metric_name,
        "actionable": True,
        "action_class": agent_heartbeat.agent.action_class,
        "manager": "heartbeat-job",
        "clear_type": enums.ClearType.AUTOMATICALLY.value,
    }
