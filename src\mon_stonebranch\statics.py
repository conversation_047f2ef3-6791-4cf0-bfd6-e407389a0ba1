"""Columns renaming for mon_stonebranch."""

COLUMNS_RENAMING = {
    "snmptrap.opsTaskAgent": "ops_task_agent",
    "snmptrap.opsTaskExecId": "ops_task_exec_id",
    "snmptrap.opsTaskName": "ops_task_name",
    "snmptrap.opsTaskSeverity": "ops_task_severity",
    "snmptrap.opsTaskStatusCode": "ops_task_status_code",
    "snmptrap.opsTaskType": "ops_task_type",
    "tags": "tags",
    "type": "type",
    "@timestamp": "timestamp",
    "snmptrap.opsConnectorSeverity": "ops_connector_severity",
    "snmptrap.opsConnectorName": "ops_connector_name",
    "snmptrap.opsConnectorMode": "ops_connector_mode",
    "snmptrap.opsConnectorType": "ops_connector_type",
    "message": "message",
}
COLUMNS_TO_KEEP = list(COLUMNS_RENAMING.keys())
DROP_IF_ALL_NAN = ["ops_task_name", "ops_connector_name", "ops_task_type"]

node_actionable = {
    "10.254.74.36": "No",
    "10.254.74.37": "No",
    "10.254.74.38": "No",
    "10.254.74.39": "No",
    "10.254.74.40": "Yes",
    "10.254.74.41": "Yes",
    "10.254.74.42": "Yes",
    "10.254.74.46": "No",
    "10.254.74.47": "Yes",
    "10.254.74.50": "Yes",
    "10.254.74.53": "No",
}

node_platform = {
    "10.254.74.36": "TST-RUN",
    "10.254.74.37": "ACC-BUILD",
    "10.254.74.38": "ACC-RUN",
    "10.254.74.39": "ACC-RUN",
    "10.254.74.40": "PRD-BUILD",
    "10.254.74.41": "PRD-RUN",
    "10.254.74.42": "PRD-RUN",
    "10.254.74.46": "ACC",
    "10.254.74.47": "PRD",
    "10.254.74.50": "PRD",
    "10.254.74.53": "ACC",
}
