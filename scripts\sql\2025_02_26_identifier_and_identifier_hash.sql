-- Column: #{Schema}#.s2110_alarm.identifier

-- ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm DROP COLUMN IF EXISTS identifier;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    ADD COLUMN identifier text;

COMMENT ON COLUMN #{Schema}#.s2110_alarm.identifier
    IS 'Unique identifier of an alarm calculated using the technical details of a specific alarm.';

-- Column: #{Schema}#.s2110_alarm.identifier_hash

-- ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm DROP COLUMN IF EXISTS identifier_hash;

ALTER TABLE IF EXISTS #{Schema}#.s2110_alarm
    ADD COLUMN identifier_hash text GENERATED ALWAYS AS (md5(identifier)) STORED;

-- Column: #{Schema}#.s2110_occurrence.identifier

-- ALTER TABLE IF EXISTS #{Schema}#.s2110_occurrence DROP COLUMN IF EXISTS identifier;

ALTER TABLE IF EXISTS #{Schema}#.s2110_occurrence
    ADD COLUMN identifier text;

-- Constraint: s2110_occurrence_uk

ALTER TABLE IF EXISTS #{Schema}#.s2110_occurrence DROP CONSTRAINT IF EXISTS s2110_occurrence_1_uk;
ALTER TABLE IF EXISTS #{Schema}#.s2110_occurrence DROP CONSTRAINT IF EXISTS s2110_occurrence_uk;

ALTER TABLE IF EXISTS #{Schema}#.s2110_occurrence
    ADD CONSTRAINT s2110_occurrence_uk UNIQUE (event_type, raise_time, ci_id, agent_id, metric_type, metric_name, identifier)
    USING INDEX TABLESPACE s2110_index;
