<?xml version="1.0" encoding="utf-8"?><edmx:Edmx Version="1.0" xmlns:edmx="http://schemas.microsoft.com/ado/2007/06/edmx" xmlns:m="http://schemas.microsoft.com/ado/2007/08/dataservices/metadata" xmlns:sap="http://www.sap.com/Protocols/SAPData"><edmx:DataServices m:DataServiceVersion="2.0"><Schema Namespace="ZIM2_PM_SPRINT_INCIDENTS_SRV" xml:lang="fr" sap:schema-version="1" xmlns="http://schemas.microsoft.com/ado/2008/09/edm"><Annotation Term="Core.SchemaVersion" String="1.0.0" xmlns="http://docs.oasis-open.org/odata/ns/edm"/><EntityType Name="Conversation" sap:content-version="1"><Key><PropertyRef Name="Qmnum"/><PropertyRef Name="Tdid"/><PropertyRef Name="Tdobject"/><PropertyRef Name="Sender"/><PropertyRef Name="CreatedAt"/><PropertyRef Name="Icon"/></Key><Property Name="Qmnum" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Tdid" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="TextID" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Tdobject" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="Type de texte" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Sender" Type="Edm.String" Nullable="false" MaxLength="80" sap:unicode="false" sap:label="Expéditeur" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CreatedAt" Type="Edm.DateTime" Nullable="false" Precision="0" sap:unicode="false" sap:label="Créér à" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Icon" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Icône" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Text" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="Message" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="Partners" sap:content-version="1"><Key><PropertyRef Name="Qmnum"/><PropertyRef Name="PartnerFct"/><PropertyRef Name="PartnerId"/></Key><Property Name="IsChanged" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmnum" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="TelephoneNumber" Type="Edm.String" MaxLength="30" sap:unicode="false" sap:label="Téléphone" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="TelephoneCountry" Type="Edm.String" MaxLength="30" sap:unicode="false" sap:label="Téléphone" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="MobileCountry" Type="Edm.String" MaxLength="3" sap:unicode="false" sap:label="Pays" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="PartnerFct" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="MobileNumber" Type="Edm.String" MaxLength="30" sap:unicode="false" sap:label="Téléphone" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="PartnerId" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="PartnerFctName" Type="Edm.String" Nullable="false" MaxLength="20" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="PartnerLongName" Type="Edm.String" MaxLength="35" sap:unicode="false" sap:label="Nom de liste" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Telephone" Type="Edm.String" MaxLength="30" sap:unicode="false" sap:label="Nº de téléphone" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Email" Type="Edm.String" MaxLength="241" sap:unicode="false" sap:label="Adresse e-mail" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="SubOrder" sap:content-version="1"><Key><PropertyRef Name="Qmnum"/><PropertyRef Name="SubOrderId"/></Key><Property Name="Qmnum" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SubOrderId" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SubOrderDescription" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SubOrderStatus" Type="Edm.String" Nullable="false" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SubOrderStart" Type="Edm.DateTime" Nullable="false" Precision="0" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SubOrderEnd" Type="Edm.DateTime" Nullable="false" Precision="0" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SubOrderWc" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Poste de trav." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SubOrderWcTxt" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="Totlog" sap:content-version="1"><Key><PropertyRef Name="Qmnum"/><PropertyRef Name="Header"/><PropertyRef Name="Counter"/></Key><Property Name="Qmnum" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Type" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Header" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Body" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Counter" Type="Edm.Int32" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="MainOrderId" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SuborderId" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="OriginatedBy" sap:content-version="1"><Key><PropertyRef Name="Id"/><PropertyRef Name="Category"/><PropertyRef Name="Type"/><PropertyRef Name="Docnumber"/></Key><Property Name="Id" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Category" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="Type d'objet" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CategoryStxt" Type="Edm.String" Nullable="false" MaxLength="20" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CategoryLtxt" Type="Edm.String" Nullable="false" MaxLength="80" sap:unicode="false" sap:label="Descript.Synth." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Type" Type="Edm.String" Nullable="false" MaxLength="5" sap:unicode="false" sap:label="TABLE R/2" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="TypeText" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Description synth." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Docnumber" Type="Edm.String" Nullable="false" MaxLength="30" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Description" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Description synth." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Linktyp" Type="Edm.String" Nullable="false" MaxLength="20" sap:unicode="false" sap:label="Dés. type renv." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="BaseUrl" Type="Edm.String" Nullable="false" MaxLength="255" sap:unicode="false" sap:label="URL" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="UserStatusTxt04" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Statut" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="UserStatusTxt30" Type="Edm.String" Nullable="false" MaxLength="30" sap:unicode="false" sap:label="Statut" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="Priority" sap:content-version="1"><Key><PropertyRef Name="Id"/></Key><Property Name="Id" Type="Edm.String" Nullable="false" MaxLength="1" sap:unicode="false" sap:label="Priorité" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Description" Type="Edm.String" Nullable="false" MaxLength="20" sap:unicode="false" sap:label="Priorité" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="ActivityCode" sap:content-version="1"><Key><PropertyRef Name="ActCode"/></Key><Property Name="ActCode" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Code activité" sap:creatable="false" sap:updatable="false"/><Property Name="ActNeeded" Type="Edm.String" MaxLength="1" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="Acttext" Type="Edm.String" MaxLength="40" sap:unicode="false" sap:label="Désign.activité" sap:creatable="false" sap:updatable="false"/></EntityType><EntityType Name="History" sap:content-version="1"><Key><PropertyRef Name="Type"/><PropertyRef Name="KeyId"/><PropertyRef Name="ItemId"/><PropertyRef Name="NotificationId"/></Key><Property Name="Type" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Objnr" Type="Edm.String" Nullable="false" MaxLength="22" sap:unicode="false" sap:label="Numéro d'objet" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="KeyId" Type="Edm.Guid" Nullable="false" sap:unicode="false" sap:label="GUID" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="OrderId" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Ordre" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ItemId" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Opération" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Datum" Type="Edm.DateTime" Nullable="false" Precision="7" sap:unicode="false" sap:label="Date" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="NotificationId" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Description" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Description" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ViewMode" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="NotificationType" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Type d'avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SuborderId" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Ordre" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SuborderType" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Type d'ordre" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CauseCodeText" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignat.code" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CauseShortText" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Texte cause" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CloseCodeText" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignat.code" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CloseShortText" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Texte" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="WorkCenter" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Poste de trav." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="WorkCenterText" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="ChangeIndicator" sap:content-version="1"><Key><PropertyRef Name="Id"/></Key><Property Name="Id" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="toSubOrders" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="header" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="toLongText" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="toOriginatedBy" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="toPartners" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="UserStatusNotif" sap:content-version="1"><Key><PropertyRef Name="Status"/></Key><Property Name="Status" Type="Edm.String" Nullable="false" MaxLength="5" sap:unicode="false" sap:label="Statut" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="StatusText" Type="Edm.String" Nullable="false" MaxLength="30" sap:unicode="false" sap:label="Statut" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="ValidTypes" sap:content-version="1"><Key><PropertyRef Name="Qmart"/></Key><Property Name="Qmart" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Type d'avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmartx" Type="Edm.String" Nullable="false" MaxLength="20" sap:unicode="false" sap:label="Type d'avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="IncidentReference" sap:content-version="1"><Key><PropertyRef Name="Qmnum"/></Key><Property Name="Qmnum" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmtxt" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Description" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmart" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Type d'avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="CauseCode" sap:content-version="1"><Key><PropertyRef Name="CauseCodeId"/><PropertyRef Name="CauseGroupe"/></Key><Property Name="CauseCodeId" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Code cause" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CauseGroupe" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Groupe de codes" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Description" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="CloseCode" sap:content-version="1"><Key><PropertyRef Name="CloseCodeId"/></Key><Property Name="CloseCodeId" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Code panne" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Description" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="CauseGroupe" sap:content-version="1"><Key><PropertyRef Name="CauseGroupeId"/></Key><Property Name="CauseGroupeId" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Groupe de codes" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Description" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="UserStatus" sap:content-version="1"><Key><PropertyRef Name="SubStatus"/></Key><Property Name="SubStatus" Type="Edm.String" Nullable="false" MaxLength="5" sap:unicode="false" sap:label="Statut" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SubStatusText" Type="Edm.String" Nullable="false" MaxLength="30" sap:unicode="false" sap:label="Statut" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="SuborderWC" sap:content-version="1"><Key><PropertyRef Name="ObjectId"/></Key><Property Name="ObjectId" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="ID d'objet" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="WorkCenterId" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Poste de trav." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Plant" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Division" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Description" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SelectionMode" Type="Edm.String" Nullable="false" MaxLength="15" sap:unicode="false" sap:label="char15" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="Equipment" sap:content-version="1"><Key><PropertyRef Name="Equnr"/></Key><Property Name="Equnr" Type="Edm.String" Nullable="false" MaxLength="18" sap:unicode="false" sap:label="Equipement" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Eqktx" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="Attachment" m:HasStream="true" sap:content-version="1"><Key><PropertyRef Name="Id"/><PropertyRef Name="Qmnum"/></Key><Property Name="Id" Type="Edm.Guid" Nullable="false" sap:unicode="false" sap:label="GUID" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmnum" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CreationUser" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Créé par" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CreationDate" Type="Edm.DateTime" Nullable="false" Precision="0" sap:unicode="false" sap:label="Horodatage" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ChangedUser" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Modifié par" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ChangedDate" Type="Edm.DateTime" Nullable="false" Precision="0" sap:unicode="false" sap:label="Horodatage" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Filesize" Type="Edm.Int32" Nullable="false" sap:unicode="false" sap:label="FILESIZE" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Filename" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="Chaîne" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Mimetype" Type="Edm.String" Nullable="false" MaxLength="128" sap:unicode="false" sap:label="Type MIME" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Content" Type="Edm.Binary" Nullable="false" sap:unicode="false" sap:label="Chaîne binaire" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Synchronized" Type="Edm.String" Nullable="false" MaxLength="1" sap:unicode="false" sap:label="Booléen" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="Incident" sap:content-version="1"><Key><PropertyRef Name="Qmnum"/></Key><Property Name="Activity" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Activité" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ActivityCode" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Code activité" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="ActivityCodeTxt" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désign.activité" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="ActivityTxt" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désign. opér." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ActivityType" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Type d'activité" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ActivityTypeTxt" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Descript. synth." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="AffectedUser" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Partenaires" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="AffectedUserName" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="ZU Nom" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ApiOrigin" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CauseCode" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Code cause" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CauseGroupe" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Groupe de codes" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CauseLongText" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="Chaîne" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CauseShortDescription" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Color" Type="Edm.String" Nullable="false" MaxLength="100" sap:unicode="false" sap:label="Couleur" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Completed" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="Incidents clôturés" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="CostCenter" Type="Edm.String" MaxLength="10" sap:unicode="false" sap:label="Centre de coûts" sap:filterable="false"/><Property Name="CostcenterTxt" Type="Edm.String" MaxLength="40" sap:unicode="false" sap:label="Description" sap:filterable="false"/><Property Name="CreatedById" Type="Edm.String" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="CreatedByName" Type="Edm.String" MaxLength="80" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="Customer" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="Client" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="CustomerName" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Char" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Cyb" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="EquipmentDescription" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="EquipmentId" Type="Edm.String" Nullable="false" MaxLength="18" sap:unicode="false" sap:label="Equipement" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ExternalCustomer" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Partenaires" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ExternalCustomerName" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="ZK Nom 1" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="FlagMainOrderCreate" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="FlagMainOrderRelease" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="FlocDescription" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="FlocId" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Poste technique" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="FollowUp" Type="Edm.String" Nullable="false" MaxLength="1" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Icon" Type="Edm.String" Nullable="false" MaxLength="100" sap:unicode="false" sap:label="Icon name" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Id" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Char" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="IncidentRef" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="IncidentRefTxt" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Description" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="IsTemplate" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="LastModifiedById" Type="Edm.String" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="LastModifiedByName" Type="Edm.String" MaxLength="80" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="LongText" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="Chaîne" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="LongTextNew" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="Chaîne" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="MainOrderNumber" Type="Edm.String" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="Mzeit" Type="Edm.Time" Precision="0" sap:unicode="false" sap:label="Heure de l'avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Network" Type="Edm.String" MaxLength="12" sap:unicode="false" sap:label="Réseau" sap:filterable="false"/><Property Name="NetworkTxt" Type="Edm.String" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:filterable="false"/><Property Name="NotifDate" Type="Edm.DateTime" Precision="0" sap:unicode="false" sap:label="Horodatage" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Objnr" Type="Edm.String" Nullable="false" MaxLength="50" sap:unicode="false" sap:label="Objecte" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Open" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="Notifications ouvertes" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="PartnerNumber" Type="Edm.String" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="PartnerType" Type="Edm.String" MaxLength="2" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="Priok" Type="Edm.String" Nullable="false" MaxLength="1" sap:unicode="false" sap:label="Priorité" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Priokx" Type="Edm.String" Nullable="false" MaxLength="20" sap:unicode="false" sap:label="Priorité" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmart" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Type d'avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmcod" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Codage" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="QmcodTxt" Type="Edm.String" Nullable="false" MaxLength="80" sap:unicode="false" sap:label="Description" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmdat" Type="Edm.DateTime" Precision="7" sap:unicode="false" sap:label="Date de l'avis" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmgrp" Type="Edm.String" Nullable="false" MaxLength="22" sap:unicode="false" sap:label="Numéro d'objet" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmnum" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Avis" sap:creatable="false"/><Property Name="Qmtxt" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Description" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Raise" Type="Edm.String" Nullable="false" MaxLength="1" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="RaiseSo" Type="Edm.String" Nullable="false" MaxLength="1" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ReferenceSolman" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="RequiredEnd" Type="Edm.DateTime" Precision="0" sap:unicode="false" sap:label="Horodatage" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="RequiredEndDate" Type="Edm.DateTime" Precision="7" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="RequiredEndTime" Type="Edm.Time" Precision="0" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="RequiredStart" Type="Edm.DateTime" Precision="0" sap:unicode="false" sap:label="Horodatage" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="RequiredStartDate" Type="Edm.DateTime" Precision="7" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="RequiredStartTime" Type="Edm.Time" Precision="0" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="Search" Type="Edm.String" Nullable="false" MaxLength="50" sap:unicode="false" sap:label="Rechercher" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="ServiceTxt" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Severity" Type="Edm.String" Nullable="false" MaxLength="1" sap:unicode="false" sap:label="Gravité" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SeverityTxt" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Descript. synth." sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SodRequest" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="SOD requests information" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="StatNotSys" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="StatNotSysTxt" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="StatNotUsrNs" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="StatNotUsrNsTxt" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="StatOrdSys" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="StatOrdSysTxt" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="StatOrdUsrNs" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="StatOrdUsrNsTxt" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="StatOrdUsrSeq" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="StatOrdUsrSeqTxt" Type="Edm.String" MaxLength="100" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false"/><Property Name="TransferSolman" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Urgency" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Urgence" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="UrgencyTxt" Type="Edm.String" Nullable="false" MaxLength="60" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="UserStatus" Type="Edm.String" Nullable="false" MaxLength="5" sap:unicode="false" sap:label="Statut" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="UserStatusTxt" Type="Edm.String" Nullable="false" MaxLength="100" sap:unicode="false" sap:label="Statut" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Wbs" Type="Edm.String" MaxLength="24" sap:unicode="false" sap:label="Elément d'OTP" sap:filterable="false"/><Property Name="WbsTxt" Type="Edm.String" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:filterable="false"/><Property Name="WorkCenter" Type="Edm.String" MaxLength="8" sap:unicode="false" sap:label="Poste de trav." sap:creatable="false" sap:updatable="false"/><Property Name="WorkCenterOrder" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><NavigationProperty Name="ToConversation" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToConversation" FromRole="FromRole_IncidentToConversation" ToRole="ToRole_IncidentToConversation"/><NavigationProperty Name="toPartners" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToPartners" FromRole="FromRole_IncidentToPartners" ToRole="ToRole_IncidentToPartners"/><NavigationProperty Name="toSubOrders" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToSubOrders" FromRole="FromRole_IncidentToSubOrders" ToRole="ToRole_IncidentToSubOrders"/><NavigationProperty Name="toTotLog" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToTotlog" FromRole="FromRole_IncidentToTotlog" ToRole="ToRole_IncidentToTotlog"/><NavigationProperty Name="toOriginatedBy" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToOriginatedBy" FromRole="FromRole_IncidentToOriginatedBy" ToRole="ToRole_IncidentToOriginatedBy"/><NavigationProperty Name="toHistory" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToHistory" FromRole="FromRole_IncidentToHistory" ToRole="ToRole_IncidentToHistory"/><NavigationProperty Name="toChangeIndicator" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToChangeIndicator" FromRole="FromRole_IncidentToChangeIndicator" ToRole="ToRole_IncidentToChangeIndicator"/><NavigationProperty Name="toAttachments" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentsToAttachments" FromRole="FromRole_IncidentsToAttachments" ToRole="ToRole_IncidentsToAttachments"/><NavigationProperty Name="toIncidentPartners" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentsToIncidentPartners" FromRole="FromRole_IncidentsToIncidentPartners" ToRole="ToRole_IncidentsToIncidentPartners"/><NavigationProperty Name="toLongText" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentsToLongText" FromRole="FromRole_IncidentsToLongText" ToRole="ToRole_IncidentsToLongText"/></EntityType><EntityType Name="LongText" sap:content-version="1"><Key><PropertyRef Name="Tdobject"/><PropertyRef Name="Tdname"/><PropertyRef Name="Tdid"/><PropertyRef Name="Tdspras"/></Key><Property Name="Tdobject" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Tdname" Type="Edm.String" Nullable="false" MaxLength="70" sap:unicode="false" sap:label="Nom de texte" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Tdid" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="ID texte" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Tdspras" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Langue" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Ltxt" Type="Edm.String" Nullable="false" sap:unicode="false" sap:label="Chaîne" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="Urgency" sap:content-version="1"><Key><PropertyRef Name="Zzurgency"/></Key><Property Name="Zzurgency" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Urgence" sap:creatable="false" sap:updatable="false"/><Property Name="ZzurgencyDescr" Type="Edm.String" MaxLength="60" sap:unicode="false" sap:label="Urgency description" sap:creatable="false" sap:updatable="false"/></EntityType><EntityType Name="PartnerFunction" sap:content-version="1"><Key><PropertyRef Name="Parvw"/></Key><Property Name="Parvw" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Rôle partenaire" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Vtext" Type="Edm.String" Nullable="false" MaxLength="20" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><NavigationProperty Name="toPartnerData" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.PartnerFunctionToPartnerData" FromRole="FromRole_PartnerFunctionToPartnerData" ToRole="ToRole_PartnerFunctionToPartnerData"/></EntityType><EntityType Name="IncidentPartner" sap:content-version="1"><Key><PropertyRef Name="Qmnum"/><PropertyRef Name="Parvw"/><PropertyRef Name="Parnr"/></Key><Property Name="Qmnum" Type="Edm.String" Nullable="false" MaxLength="12" sap:unicode="false" sap:label="Avis" sap:creatable="false"/><Property Name="Telephone" Type="Edm.String" Nullable="false" MaxLength="30" sap:unicode="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Parvw" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Rôle partenaire" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Email" Type="Edm.String" Nullable="false" MaxLength="241" sap:unicode="false" sap:label="Adresse e-mail" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Parnr" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="Personne de contact" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Vtext" Type="Edm.String" Nullable="false" MaxLength="20" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Name" Type="Edm.String" Nullable="false" MaxLength="35" sap:unicode="false" sap:label="Nom de liste" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="PartnerData" sap:content-version="1"><Key><PropertyRef Name="Parnr"/><PropertyRef Name="Parvw"/></Key><Property Name="Parnr" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="Personne de contact" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Parvw" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Rôle partenaire" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Name" Type="Edm.String" Nullable="false" MaxLength="35" sap:unicode="false" sap:label="Nom de liste" sap:creatable="false" sap:updatable="false" sap:sortable="false"/></EntityType><EntityType Name="SubService" sap:content-version="1"><Key><PropertyRef Name="Subservice"/><PropertyRef Name="Serviceid"/></Key><Property Name="Subservice" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Codage" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Serviceid" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Groupe de codes" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="SubserviceDescr" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Désignat.code" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="Service" sap:content-version="1"><Key><PropertyRef Name="ServiceId"/></Key><Property Name="ServiceDescr" Type="Edm.String" MaxLength="40" sap:unicode="false" sap:label="Désignation" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="ServiceId" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Groupe de codes" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><NavigationProperty Name="toSubService" Relationship="ZIM2_PM_SPRINT_INCIDENTS_SRV.ServiceToSubService" FromRole="FromRole_ServiceToSubService" ToRole="ToRole_ServiceToSubService"/></EntityType><EntityType Name="TechObjectParam" sap:content-version="1"><Key><PropertyRef Name="Serviceid"/><PropertyRef Name="Subservice"/><PropertyRef Name="Techobjtype"/></Key><Property Name="IsDescription" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="Show description field" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Serviceid" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Groupe de codes" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Subservice" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Codage" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Techobjlabel" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Descr. zne long" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Techobjtype" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Tech Obj Type" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="IsSerge" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="Char01" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="TechObjectHelp" sap:content-version="1"><Key><PropertyRef Name="Serviceid"/><PropertyRef Name="Subservice"/><PropertyRef Name="Techobjtype"/><PropertyRef Name="Techobjkey"/></Key><Property Name="MyAssets" Type="Edm.Boolean" Nullable="false" sap:unicode="false" sap:label="Mes assets" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="Serviceid" Type="Edm.String" Nullable="false" MaxLength="8" sap:unicode="false" sap:label="Groupe de codes" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="ZuPartner" Type="Edm.String" Nullable="false" MaxLength="10" sap:unicode="false" sap:label="Personne de contact" sap:creatable="false" sap:updatable="false" sap:sortable="false"/><Property Name="Subservice" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Codage" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Techobjtype" Type="Edm.String" Nullable="false" MaxLength="2" sap:unicode="false" sap:label="Tech Obj Type" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Sort" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Descr. zne long" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Description" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Descr. zne long" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Serge" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Descr. zne long" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Techobjkey" Type="Edm.String" Nullable="false" MaxLength="40" sap:unicode="false" sap:label="Descr. zne long" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><EntityType Name="LongTextType" sap:content-version="1"><Key><PropertyRef Name="TypeId"/></Key><Property Name="TypeId" Type="Edm.String" Nullable="false" MaxLength="4" sap:unicode="false" sap:label="Type de texte" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="TypeText" Type="Edm.String" Nullable="false" MaxLength="30" sap:unicode="false" sap:label="Description type de texte" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></EntityType><ComplexType Name="ActionResult"><Property Name="Success" Type="Edm.Boolean" Nullable="false" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Message" Type="Edm.String" Nullable="false" MaxLength="220" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/><Property Name="Qmnum" Type="Edm.String" Nullable="false" MaxLength="12" sap:label="" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></ComplexType><ComplexType Name="Impact"><Property Name="ImpactId" Type="Edm.String" Nullable="false" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></ComplexType><ComplexType Name="ReturnStatus"><Property Name="Return" Type="Edm.String" Nullable="false" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></ComplexType><ComplexType Name="UserAuthorization"><Property Name="CanCreateIncident" Type="Edm.Boolean" Nullable="false" sap:label="Peut créer un incident" sap:creatable="false" sap:updatable="false" sap:sortable="false" sap:filterable="false"/></ComplexType><Association Name="PartnerFunctionToPartnerData" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.PartnerFunction" Multiplicity="1" Role="FromRole_PartnerFunctionToPartnerData"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.PartnerData" Multiplicity="*" Role="ToRole_PartnerFunctionToPartnerData"/><ReferentialConstraint><Principal Role="FromRole_PartnerFunctionToPartnerData"><PropertyRef Name="Parvw"/></Principal><Dependent Role="ToRole_PartnerFunctionToPartnerData"><PropertyRef Name="Parvw"/></Dependent></ReferentialConstraint></Association><Association Name="ServiceToSubService" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Service" Multiplicity="1" Role="FromRole_ServiceToSubService"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.SubService" Multiplicity="*" Role="ToRole_ServiceToSubService"/><ReferentialConstraint><Principal Role="FromRole_ServiceToSubService"><PropertyRef Name="ServiceId"/></Principal><Dependent Role="ToRole_ServiceToSubService"><PropertyRef Name="Serviceid"/></Dependent></ReferentialConstraint></Association><Association Name="IncidentToPartners" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentToPartners"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Partners" Multiplicity="*" Role="ToRole_IncidentToPartners"/><ReferentialConstraint><Principal Role="FromRole_IncidentToPartners"><PropertyRef Name="Qmnum"/></Principal><Dependent Role="ToRole_IncidentToPartners"><PropertyRef Name="Qmnum"/></Dependent></ReferentialConstraint></Association><Association Name="IncidentToSubOrders" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentToSubOrders"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.SubOrder" Multiplicity="*" Role="ToRole_IncidentToSubOrders"/><ReferentialConstraint><Principal Role="FromRole_IncidentToSubOrders"><PropertyRef Name="Qmnum"/></Principal><Dependent Role="ToRole_IncidentToSubOrders"><PropertyRef Name="Qmnum"/></Dependent></ReferentialConstraint></Association><Association Name="IncidentToTotlog" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentToTotlog"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Totlog" Multiplicity="*" Role="ToRole_IncidentToTotlog"/><ReferentialConstraint><Principal Role="FromRole_IncidentToTotlog"><PropertyRef Name="Qmnum"/></Principal><Dependent Role="ToRole_IncidentToTotlog"><PropertyRef Name="Qmnum"/></Dependent></ReferentialConstraint></Association><Association Name="IncidentToOriginatedBy" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentToOriginatedBy"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.OriginatedBy" Multiplicity="*" Role="ToRole_IncidentToOriginatedBy"/><ReferentialConstraint><Principal Role="FromRole_IncidentToOriginatedBy"><PropertyRef Name="Qmnum"/></Principal><Dependent Role="ToRole_IncidentToOriginatedBy"><PropertyRef Name="Id"/></Dependent></ReferentialConstraint></Association><Association Name="IncidentToHistory" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentToHistory"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.History" Multiplicity="*" Role="ToRole_IncidentToHistory"/></Association><Association Name="IncidentToChangeIndicator" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentToChangeIndicator"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.ChangeIndicator" Multiplicity="0..1" Role="ToRole_IncidentToChangeIndicator"/><ReferentialConstraint><Principal Role="FromRole_IncidentToChangeIndicator"><PropertyRef Name="Qmnum"/></Principal><Dependent Role="ToRole_IncidentToChangeIndicator"><PropertyRef Name="Id"/></Dependent></ReferentialConstraint></Association><Association Name="IncidentsToAttachments" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentsToAttachments"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Attachment" Multiplicity="*" Role="ToRole_IncidentsToAttachments"/><ReferentialConstraint><Principal Role="FromRole_IncidentsToAttachments"><PropertyRef Name="Qmnum"/></Principal><Dependent Role="ToRole_IncidentsToAttachments"><PropertyRef Name="Qmnum"/></Dependent></ReferentialConstraint></Association><Association Name="IncidentsToIncidentPartners" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentsToIncidentPartners"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentPartner" Multiplicity="*" Role="ToRole_IncidentsToIncidentPartners"/><ReferentialConstraint><Principal Role="FromRole_IncidentsToIncidentPartners"><PropertyRef Name="Qmnum"/></Principal><Dependent Role="ToRole_IncidentsToIncidentPartners"><PropertyRef Name="Qmnum"/></Dependent></ReferentialConstraint></Association><Association Name="IncidentsToLongText" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentsToLongText"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.LongText" Multiplicity="*" Role="ToRole_IncidentsToLongText"/><ReferentialConstraint><Principal Role="FromRole_IncidentsToLongText"><PropertyRef Name="Qmnum"/></Principal><Dependent Role="ToRole_IncidentsToLongText"><PropertyRef Name="Tdname"/></Dependent></ReferentialConstraint></Association><Association Name="IncidentToConversation" sap:content-version="1"><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" Multiplicity="1" Role="FromRole_IncidentToConversation"/><End Type="ZIM2_PM_SPRINT_INCIDENTS_SRV.Conversation" Multiplicity="*" Role="ToRole_IncidentToConversation"/><ReferentialConstraint><Principal Role="FromRole_IncidentToConversation"><PropertyRef Name="Qmnum"/></Principal><Dependent Role="ToRole_IncidentToConversation"><PropertyRef Name="Qmnum"/></Dependent></ReferentialConstraint></Association><EntityContainer Name="ZIM2_PM_SPRINT_INCIDENTS_SRV_Entities" m:IsDefaultEntityContainer="true" sap:supported-formats="atom json xlsx"><EntitySet Name="ConversationSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Conversation" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="PartnersSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Partners" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="SubOrderSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.SubOrder" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="TotlogSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Totlog" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="OriginatedBySet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.OriginatedBy" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="PrioritySet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Priority" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="ActivityCodeSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ActivityCode" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="HistorySet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.History" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="ChangeIndicatorSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ChangeIndicator" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="UserStatusNotifSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.UserStatusNotif" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="ValidTypesSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ValidTypes" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="IncidentReferenceSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentReference" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="CauseCodeSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.CauseCode" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="CloseCodeSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.CloseCode" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="CauseGroupeSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.CauseGroupe" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="UserStatusSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.UserStatus" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="SuborderWCSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.SuborderWC" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="EquipmentSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Equipment" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="AttachmentSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Attachment" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="IncidentSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="LongTextSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.LongText" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="UrgencySet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Urgency" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="PartnerFunctionSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.PartnerFunction" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="IncidentPartnerSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentPartner" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="PartnerDataSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.PartnerData" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="SubServiceSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.SubService" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="ServiceSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Service" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="TechObjectParamSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.TechObjectParam" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:addressable="false" sap:content-version="1"/><EntitySet Name="TechObjectHelpSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.TechObjectHelp" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><EntitySet Name="LongTextTypeSet" EntityType="ZIM2_PM_SPRINT_INCIDENTS_SRV.LongTextType" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:pageable="false" sap:content-version="1"/><AssociationSet Name="IncidentToPartnersSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToPartners" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentToPartners"/><End EntitySet="PartnersSet" Role="ToRole_IncidentToPartners"/></AssociationSet><AssociationSet Name="IncidentToChangeIndicatorSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToChangeIndicator" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentToChangeIndicator"/><End EntitySet="ChangeIndicatorSet" Role="ToRole_IncidentToChangeIndicator"/></AssociationSet><AssociationSet Name="IncidentToHistory_AssocSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToHistory" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentToHistory"/><End EntitySet="HistorySet" Role="ToRole_IncidentToHistory"/></AssociationSet><AssociationSet Name="ServiceToSubServiceSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.ServiceToSubService" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="ServiceSet" Role="FromRole_ServiceToSubService"/><End EntitySet="SubServiceSet" Role="ToRole_ServiceToSubService"/></AssociationSet><AssociationSet Name="IncidentsToLongTextSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentsToLongText" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentsToLongText"/><End EntitySet="LongTextSet" Role="ToRole_IncidentsToLongText"/></AssociationSet><AssociationSet Name="PartnerFunctionToPartnerDataSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.PartnerFunctionToPartnerData" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="PartnerFunctionSet" Role="FromRole_PartnerFunctionToPartnerData"/><End EntitySet="PartnerDataSet" Role="ToRole_PartnerFunctionToPartnerData"/></AssociationSet><AssociationSet Name="IncidentsToIncidentPartnersSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentsToIncidentPartners" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentsToIncidentPartners"/><End EntitySet="IncidentPartnerSet" Role="ToRole_IncidentsToIncidentPartners"/></AssociationSet><AssociationSet Name="IncidentToTotlogSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToTotlog" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentToTotlog"/><End EntitySet="TotlogSet" Role="ToRole_IncidentToTotlog"/></AssociationSet><AssociationSet Name="IncidentsToAttachmentsSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentsToAttachments" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentsToAttachments"/><End EntitySet="AttachmentSet" Role="ToRole_IncidentsToAttachments"/></AssociationSet><AssociationSet Name="IncidentToConversationSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToConversation" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentToConversation"/><End EntitySet="ConversationSet" Role="ToRole_IncidentToConversation"/></AssociationSet><AssociationSet Name="IncidentToOriginatedBySet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToOriginatedBy" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentToOriginatedBy"/><End EntitySet="OriginatedBySet" Role="ToRole_IncidentToOriginatedBy"/></AssociationSet><AssociationSet Name="IncidentToSubOrdersSet" Association="ZIM2_PM_SPRINT_INCIDENTS_SRV.IncidentToSubOrders" sap:creatable="false" sap:updatable="false" sap:deletable="false" sap:content-version="1"><End EntitySet="IncidentSet" Role="FromRole_IncidentToSubOrders"/><End EntitySet="SubOrderSet" Role="ToRole_IncidentToSubOrders"/></AssociationSet><FunctionImport Name="ProvideFeedback" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ActionResult" m:HttpMethod="POST"><Parameter Name="ConfirmationText" Type="Edm.String" Mode="In" MaxLength="40"/><Parameter Name="Qmnum" Type="Edm.String" Mode="In" MaxLength="12"/><Parameter Name="LongText" Type="Edm.String" Mode="In"/><Parameter Name="Reason" Type="Edm.String" Mode="In" MaxLength="4"/></FunctionImport><FunctionImport Name="TransferWorkCenter" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ActionResult" m:HttpMethod="POST"><Parameter Name="Qmnum" Type="Edm.String" Mode="In" MaxLength="12"/><Parameter Name="WorkCenter" Type="Edm.String" Mode="In" MaxLength="8"/></FunctionImport><FunctionImport Name="SetSecondaryStatus" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ActionResult" m:HttpMethod="POST"><Parameter Name="Qmnum" Type="Edm.String" Mode="In" MaxLength="12"/><Parameter Name="Status" Type="Edm.String" Mode="In" MaxLength="4"/><Parameter Name="UnSet" Type="Edm.Boolean" Mode="In" Nullable="true"/></FunctionImport><FunctionImport Name="CloseMainOrder" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ActionResult" m:HttpMethod="POST"><Parameter Name="Qmnum" Type="Edm.String" Mode="In" MaxLength="12"/><Parameter Name="Status" Type="Edm.String" Mode="In" MaxLength="4"/><Parameter Name="CauseGroup" Type="Edm.String" Mode="In"/><Parameter Name="CauseCode" Type="Edm.String" Mode="In"/><Parameter Name="CauseText" Type="Edm.String" Mode="In"/><Parameter Name="CloseGroup" Type="Edm.String" Mode="In" Nullable="true"/><Parameter Name="CloseCode" Type="Edm.String" Mode="In" Nullable="true"/><Parameter Name="CloseText" Type="Edm.String" Mode="In" Nullable="true"/></FunctionImport><FunctionImport Name="GetPartnersForFloc" ReturnType="Collection(ZIM2_PM_SPRINT_INCIDENTS_SRV.Partners)" EntitySet="PartnersSet" m:HttpMethod="GET"><Parameter Name="Floc" Type="Edm.String" Mode="In" MaxLength="40"/></FunctionImport><FunctionImport Name="GetPartnersForEquipment" ReturnType="Collection(ZIM2_PM_SPRINT_INCIDENTS_SRV.Partners)" EntitySet="PartnersSet" m:HttpMethod="GET"><Parameter Name="Equi" Type="Edm.String" Mode="In" MaxLength="18"/></FunctionImport><FunctionImport Name="GetHistoryForFloc" ReturnType="Collection(ZIM2_PM_SPRINT_INCIDENTS_SRV.History)" EntitySet="HistorySet" m:HttpMethod="GET"><Parameter Name="Floc" Type="Edm.String" Mode="In" MaxLength="40"/></FunctionImport><FunctionImport Name="GetHistoryForEquipment" ReturnType="Collection(ZIM2_PM_SPRINT_INCIDENTS_SRV.History)" EntitySet="HistorySet" m:HttpMethod="GET"><Parameter Name="Equi" Type="Edm.String" Mode="In" MaxLength="18"/></FunctionImport><FunctionImport Name="GetImpact" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.Impact" m:HttpMethod="GET"><Parameter Name="subService" Type="Edm.String" Mode="In" MaxLength="4"/><Parameter Name="service" Type="Edm.String" Mode="In" MaxLength="22"/></FunctionImport><FunctionImport Name="GetIncidentReference" ReturnType="Collection(ZIM2_PM_SPRINT_INCIDENTS_SRV.Incident)" EntitySet="IncidentSet" m:HttpMethod="GET"><Parameter Name="Qmnum" Type="Edm.String" Mode="In" MaxLength="12"/><Parameter Name="Qmart" Type="Edm.String" Mode="In" MaxLength="2"/><Parameter Name="Qmtxt" Type="Edm.String" Mode="In"/></FunctionImport><FunctionImport Name="Resolve" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ActionResult" m:HttpMethod="POST"><Parameter Name="CloseCode" Type="Edm.String" Mode="In" MaxLength="4"/><Parameter Name="Id" Type="Edm.String" Mode="In" MaxLength="12"/><Parameter Name="LongText" Type="Edm.String" Mode="In"/><Parameter Name="Motivation" Type="Edm.String" Mode="In" MaxLength="40"/><Parameter Name="CauseCode" Type="Edm.String" Mode="In" MaxLength="4"/><Parameter Name="LongTextCause" Type="Edm.String" Mode="In"/><Parameter Name="MotivationCause" Type="Edm.String" Mode="In" MaxLength="40"/><Parameter Name="CauseGroupe" Type="Edm.String" Mode="In" MaxLength="8"/></FunctionImport><FunctionImport Name="SolutionAccepted" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ActionResult" m:HttpMethod="POST"><Parameter Name="Qmnum" Type="Edm.String" Mode="In" MaxLength="12"/></FunctionImport><FunctionImport Name="SubOrderCreateMultiple" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ActionResult" m:HttpMethod="POST"><Parameter Name="StartDate" Type="Edm.DateTime" Mode="In" Precision="0"/><Parameter Name="EndDate" Type="Edm.DateTime" Mode="In" Precision="0"/><Parameter Name="WorkCenter" Type="Edm.String" Mode="In" MaxLength="8"/><Parameter Name="Id" Type="Edm.String" Mode="In" MaxLength="12"/><Parameter Name="Description" Type="Edm.String" Mode="In" MaxLength="40"/></FunctionImport><FunctionImport Name="CanICreateIncidents" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.UserAuthorization" m:HttpMethod="GET"/><FunctionImport Name="SendMailToSOD" ReturnType="ZIM2_PM_SPRINT_INCIDENTS_SRV.ReturnStatus" m:HttpMethod="POST"><Parameter Name="Qmnum" Type="Edm.String" Mode="In" MaxLength="12"/><Parameter Name="Body" Type="Edm.String" Mode="In"/></FunctionImport></EntityContainer><atom:link rel="self" href="https://ACC-SAPFIORI.INFRABEL.BE:443/sap/opu/odata/sap/ZIM2_PM_SPRINT_INCIDENTS_SRV/$metadata" xmlns:atom="http://www.w3.org/2005/Atom"/><atom:link rel="latest-version" href="https://ACC-SAPFIORI.INFRABEL.BE:443/sap/opu/odata/sap/ZIM2_PM_SPRINT_INCIDENTS_SRV/$metadata" xmlns:atom="http://www.w3.org/2005/Atom"/></Schema></edmx:DataServices></edmx:Edmx>