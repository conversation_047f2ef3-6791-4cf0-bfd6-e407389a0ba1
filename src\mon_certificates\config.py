"""Configuration module for mon-certificates."""

import json
from dataclasses import dataclass
from pathlib import Path

from olympus_common.config import BaseServiceConfig, DatabaseConfig, ElasticConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.exceptions import ConfigurationError
from olympus_common.utils import Singleton


@dataclass(frozen=True)
class UcmdbConfig(metaclass=Singleton):
    """Represent the configuration for uCMDB Client."""

    client_id: str = env_field("UCMDB_USER")
    client_secret: str = env_field("UCMDB_PASSWORD")
    token_url: str = env_field("UCMDB_TOKEN_URL")
    grant_type: str = env_field("UCMDB_GRANT_TYPE", default="client_credentials")
    scope: str = env_field("UCMDB_SCOPE")
    endpoint: str = env_field("UCMDB_ENDPOINT")
    version: str = env_field("UCMDB_VERSION")

    def to_dict(self):
        """Return the class as a dict."""
        return self.__dict__


@dataclass(frozen=True)
class Config(BaseServiceConfig, metaclass=Singleton):
    """Represent the configuration for mon-certificates."""

    # E-mails
    client_id: str = env_field("CLIENT_ID")
    client_secret: str = env_field("CLIENT_SECRET")
    tenant_id: str = env_field("TENANT_ID")
    email_address: str = env_field("EMAIL_ADDRESS")
    _default_cc_receivers: list[str] = env_field(
        "DEFAULT_CC_RECEIVERS", astype=json.loads, default='["<EMAIL>"]'
    )
    _notification_emails: list[str] = env_field("NOTIFICATION_EMAILS", astype=json.loads)
    _icc_emails: list[str] = env_field("ICC_EMAILS", astype=json.loads)

    # Global
    environment: str = env_field("ENVIRONMENT")

    # Syslog
    syslog_host: str = env_field("SYSLOG_HOST")
    syslog_port: int = env_field("SYSLOG_PORT", astype=int)
    syslog_facility: str = "local6"

    # Folders
    checkpoints_folder: Path = env_field(
        "CHECKPOINTS_FOLDER", astype=Path, default=str(Path(__file__).parent / "checkpoints")
    )
    exclusion_folder: Path = env_field(
        "EXCLUSION_FOLDER", astype=Path, default=str(Path(__file__).parent / "exclusion")
    )

    # Files
    run_number_file: str = "run_number.txt"

    # Database
    database_config: DatabaseConfig = dataclass_field(DatabaseConfig)

    # uCMDB
    ucmdb_config: UcmdbConfig = dataclass_field(UcmdbConfig)

    # Elastic
    elastic_config: ElasticConfig = dataclass_field(ElasticConfig)

    @property
    def default_cc_receivers(self):
        """Return the default_cc_receivers as a set."""
        return set(self._default_cc_receivers)

    @property
    def notification_emails(self):
        """Return the notification_emails as a set."""
        return set(self._notification_emails)

    @property
    def icc_receivers(self):
        """Return the email addresses for the ICC (and potentials coordinators)."""
        return set(self._icc_emails)

    @property
    def save_to_sent(self):
        """Return the boolean used by MS Graph (formatted as expected by MS Graph) to save the sent e-mail.

        This will be true if debug=False else true
        """
        return "false" if self.debug else "true"

    @property
    def syslog_tag(self):
        """Return the syslog tag depending on the given environment."""
        return f"A1617-{self.environment}-CERTIFICATES-HB"

    @property
    def exclusion_path(self):
        """Return the full path to the Excel file containing the certificates without monitoring."""
        return self.exclusion_folder / "CertificatesWithoutMonitoring.xlsx"

    @property
    def templates_folder(self):
        """Return the folder containing the templates used in the e-mail formatting."""
        return Path(__file__).parent / "templates"

    @property
    def image_path(self):
        """Return the full path to the image used in the e-mail formatting."""
        return Path(__file__).parent / "images" / "header_image.png"

    def ensure_existing_folders(self) -> None:
        """Ensure that the folders from the configuration exist."""
        # Ensure that the used folders exist.
        self.logger_config.logs_folder.mkdir(exist_ok=True)
        self.checkpoints_folder.mkdir(exist_ok=True)
        self.exclusion_folder.mkdir(exist_ok=True)


config = Config()

# Check environment is correct
if config.environment not in ["DEV", "ACC", "PROD"]:
    raise ConfigurationError("The ENVIRONMENT env variable is not in the correct values which are (DEV, ACC, PROD)")

# Ensure folders exist
config.ensure_existing_folders()
