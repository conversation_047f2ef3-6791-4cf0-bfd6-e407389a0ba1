kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-mon-certificates-config-map-#{appEnv}#
  namespace: a1617-mail-monitoring-gateway
data:
  APP_FILE: src/a2110_olympus/run.py
  ENVIRONMENT: "#{upper(appEnv)}#"
  ELASTIC_SERVERS: >-
    #{esServers}#
  ELASTIC_INDEXES: >-
    #{esIndexes}#
  DB_SCHEMA: "#{databaseSchema}#"
  CHECKPOINTS_FOLDER: "/data/checkpoints"
  EXCLUSION_FOLDER: "/data/exclusion"
  LOGS_FOLDER: "/data/logs"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  LOGS_MAX_SIZE: "990"
