"""Helper module concerning nacadca certificates for mon-certificates."""

import base64
import json
import logging
import re

import pandas as pd
from jinja2 import Environment, FileSystemLoader

from mon_certificates.config import config
from mon_certificates.enums import NacadcaContactColumn
from mon_certificates.static import NACADCA_GROUP_COLUMN
from mon_certificates.utils.mailing import send_graph_email
from olympus_common.utils import read_file_content, str_to_set, write_in_file

# ================================================
# CLASS
# ================================================


class WithoutMails:
    """Represent an Helper class for contact column for which organizational units doesn't have email address.

    Parameters
    ----------
    mail_type : NacadcaContactColumn
        The corresponding contact column.
    org_units : list[str]
        The list of organizational units that haven't email address for the given contact column.
    """

    def __init__(self, mail_type: NacadcaContactColumn, org_units: list[str]) -> None:
        self.number = len(org_units)
        self.mail_type = mail_type
        self.org_units = org_units


# ================================================
# FUNCTIONS
# ================================================


def manage_contact(df: pd.DataFrame) -> pd.DataFrame:
    """Extract the e-mail address from the contact columns of NACADCA certificates.

    Parameters
    ----------
    df : pd.DataFrame
        The dataframe containing the NACADCA certificates.

    Returns
    -------
    pd.DataFrame
        The dataframe containing the NACADCA certificates for which the contact columns contains the e-mail addresses.
    """
    df["manager"] = df.apply(lambda row: _extract_email(row["manager"]), axis=1)
    df["head_of"] = df.apply(lambda row: _extract_email(row["head_of"]), axis=1)
    df["team_lead"] = df.apply(lambda row: _extract_email(row["team_lead"]), axis=1)
    return df


def reporting_for_missing_emails(df: pd.DataFrame) -> None:
    """Send an email containing the organizational units for each contact column that doesn't have e-mail address.

    Notes
    -----
    The email is sent to the config.default_cc_receivers and config.notification_emails.
    A checkpoint is done for each organizational unit to avoid sending the same multiple times.

    Parameters
    ----------
    df : pd.DataFrame
        The dataframe containing the NACADCA certificates.
    """
    # Filter to keep only certificates with organizational unit
    df_org_units_notna = df[df[NACADCA_GROUP_COLUMN].notnull()]
    df_org_units_notna = df_org_units_notna[df_org_units_notna[NACADCA_GROUP_COLUMN] != "N/A"]

    # Create the list of missing organizational units for each type of contacts
    units_without_recipients = _missing_lists(df_org_units_notna, NacadcaContactColumn.RECIPIENTS)
    units_without_team_lead = _missing_lists(df_org_units_notna, NacadcaContactColumn.TEAM_LEAD)
    units_without_manager = _missing_lists(df_org_units_notna, NacadcaContactColumn.MANAGER)
    units_without_head_of = _missing_lists(df_org_units_notna, NacadcaContactColumn.HEAD_OF)

    if units_without_recipients or units_without_head_of or units_without_manager or units_without_team_lead:
        without_mail = WithoutMails(NacadcaContactColumn.RECIPIENTS, units_without_recipients)
        without_manager = WithoutMails(NacadcaContactColumn.MANAGER, units_without_manager)
        without_head_of = WithoutMails(NacadcaContactColumn.HEAD_OF, units_without_head_of)
        without_team_lead = WithoutMails(NacadcaContactColumn.TEAM_LEAD, units_without_team_lead)

        # Mail body construction
        mail_body = _render_html_org_units(without_mail, without_manager, without_head_of, without_team_lead)
        if config.debug:
            html_debug_file = config.logger_config.logs_folder / "without-emails.html"
            html_debug_file.write_text(mail_body)

        # Mailing
        recipients = set()
        recipients.update(config.notification_emails)
        if not config.debug:
            recipients.update(config.default_cc_receivers)

        subject = "Organizational units without email addresses"
        send_graph_email(recipients_=recipients, mail_text=mail_body, subject=subject, sender=config.email_address)

        # Checkpoint
        _org_units_checkpoint(without_mail, without_manager, without_head_of, without_team_lead)


def _org_units_checkpoint(
    without_recipients: WithoutMails,
    without_team_lead: WithoutMails,
    without_manager: WithoutMails,
    without_head_of: WithoutMails,
) -> None:
    for without in [without_recipients, without_manager, without_head_of, without_team_lead]:
        if without.number > 0:
            cache_file = f"without-{without.mail_type.value}.txt"
            already_cached_without = str_to_set(str(read_file_content(config.checkpoints_folder, cache_file)))
            already_cached_without.update(list(without.org_units))
            write_in_file(
                config.checkpoints_folder,
                cache_file,
                json.dumps(list(already_cached_without)),
                inplace=True,
            )


def _render_html_org_units(without_mail, without_manager, without_head_of, without_team_lead):
    file_loader = FileSystemLoader(config.templates_folder)
    env = Environment(loader=file_loader, autoescape=True)
    template = env.get_template("mail_org_unit_without_mails.html.j2")
    image_string = base64.b64encode(config.image_path.read_bytes()).decode()
    output = template.render(
        header_image=image_string,
        without_mail=without_mail,
        without_manager=without_manager,
        without_head_of=without_head_of,
        without_team_lead=without_team_lead,
    )
    return output


def _missing_lists(df_org_unit: pd.DataFrame, contact_column: NacadcaContactColumn) -> list:
    org_units = set(df_org_unit[NACADCA_GROUP_COLUMN].tolist())
    without_email_list = []
    for unit in org_units:
        df_temp: pd.DataFrame = df_org_unit[df_org_unit[NACADCA_GROUP_COLUMN] == unit]
        if "N/A" in df_temp[contact_column].values.tolist():
            without_email_list.append(unit)
    without_email_list = _remove_already_sent_missing_org_units(without_email_list, contact_column)
    return without_email_list


def _remove_already_sent_missing_org_units(org_units: list[str], contact_column: NacadcaContactColumn) -> list[str]:
    """Filter the provided org_units list and return only organizational units that haven't already been sent."""
    file_cache = f"without-{contact_column.value}.txt"
    already_sent = str_to_set(str(read_file_content(config.checkpoints_folder, file_cache)))
    return [unit for unit in org_units if unit not in already_sent]


def _extract_email(field: str) -> str:
    """Extract the email address from the given string.

    Note
    ----
    The email address must be surrounded by < >.

    Parameters
    ----------
    field : str
        The string from which the email address will be extracted.

    Returns
    -------
    str
        The found email address or empty string if no address was found or the given string was empty.
    """
    if not field or field == "N/A":
        return "N/A"
    elif email := re.findall(r"(?!\<)[A-Za-z]+\.[A-Za-z]+\@[A-Za-z]+\.[A-Za-z]{2,3}(?<!\>)", field):
        return email[0]
    else:
        logging.error(re.error("No email was found in the field"))
        return "N/A"
