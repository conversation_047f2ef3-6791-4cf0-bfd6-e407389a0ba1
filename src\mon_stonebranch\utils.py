"""Utils module for mon-stonebranch."""

from mon_stonebranch import patterns


def is_task_object(row) -> bool:
    """Return whether or not the row is a task object.

    If this returns False, the row is a connector object.
    """
    return row["tags"][0] == "opsTaskObjects"


def convert_hex_to_ip(hex_str: str) -> str:
    """Convert a hex string into an IP address."""
    if len(hex_str) != 4:
        raise ValueError("hex must be of length 4 in other to convert it into an IP")

    octets: list[int] = [ord(char) for char in hex_str]

    for octet in octets:
        if octet < 0 or octet > 255:
            raise ValueError("octet must be between 0 and 255")

    ip_address = ".".join(map(str, octets))
    return ip_address


def get_hex_ip_from_message(message: str) -> str:
    """Extract the IP address from the message field."""
    # regex pattern to extract the IP address from the message field.

    if match := patterns.ip_addr_regex.search(message):
        extracted_element: str = match.group(1)
        value_side = extracted_element.split(" ")[1]
        hex_ip = value_side.replace("@value=", "").replace("\\\\", "\\")
        decoded_hex = hex_ip.encode().decode("unicode_escape")
        result = decoded_hex.split()[1].replace('"', "")
        return f"\n{result}"
    return ""
