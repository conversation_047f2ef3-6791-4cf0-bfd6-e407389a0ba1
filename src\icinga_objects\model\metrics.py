"""Module to provide the UCMDB objects."""

import logging
from typing import Any

import pandas as pd

from icinga_objects.config import config
from icinga_objects.ucmdb_repository import UcmdbRepository
from icinga_objects.utils import format_service_data, to_add
from olympus_common import enums, icinga
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import MeasureType
from olympus_common.ucmdb import create_ucmdb_db_session
from olympus_common.utils import parallelize_process

client = icinga.IcingaClient()


class GroupMetric:
    """Represent a GroupMetric to manage Icinga Services."""

    service_pending_state = 16
    max_pending_service = 1000

    def __init__(self, source: str) -> None:
        self.source: str = source
        self.existing_services: list = []
        self.icinga_objectType = enums.IcingaObjectType.SERVICE.value
        self.to_process_check: list[dict] = []
        self.session = create_ucmdb_db_session()
        self.ucmdb_repository = UcmdbRepository(self.session)

    @CaptureSpan(MeasureType.CUSTOM.value)
    def get_metrics_to_check(self) -> pd.DataFrame:
        """Get metrics to process check."""
        d_condition = self.get_services_to_check()
        if not d_condition:
            return pd.DataFrame()
        data = self.ucmdb_repository.get_metrics_description(d_condition)
        return data

    @CaptureSpan(MeasureType.CUSTOM.value)
    def sync_with_icinga(self, purge_base: bool = config.purge_icinga_base) -> None:
        """Sync metrics with icinga."""
        if purge_base:
            self.delete_service_from_icinga()
        if config.process_check_missing:
            self.process_check_missing()
        self.add_to_icinga()

    @CaptureSpan(MeasureType.CUSTOM.value)
    def add_to_icinga(self) -> None:
        """Process and add metrics in icinga."""
        df_metrics = self.ucmdb_repository.get_metrics(self.source)
        if not df_metrics.empty:
            self.existing_services = self.get_services()
            df_metrics["merge"] = df_metrics[["floc_id", "metric_type", "metric_name"]].apply(
                lambda x: f"{x['floc_id']}!{x['metric_type']}{x['metric_name']}", axis=1
            )
            df_icinga = pd.DataFrame(self.existing_services).rename(columns={0: "merge"})
            data_to_add = to_add(df_metrics, df_icinga, merge_columns=["merge"])
            data_to_add = data_to_add.drop(columns=["merge"])
            data_to_add = format_service_data(data_to_add)
            data = data_to_add.to_dict(orient="records")
            parallelize_process(data, self.add_metric, config.thread_number)
            parallelize_process(self.to_process_check, self.pc_metric, config.thread_number)
        client.show_counter_values()
        client.reset_counter()

    @CaptureSpan(MeasureType.CUSTOM.value)
    def process_check_missing(self) -> None:
        """Process metrics missing during synchronisation."""
        df_metrics = self.get_metrics_to_check()
        if not df_metrics.empty:
            df_metrics = format_service_data(df_metrics)
            data = df_metrics.to_dict(orient="records")
            parallelize_process(data, self.pc_metric, config.thread_number)
            client.show_counter_values()
            client.reset_counter()

    @CaptureSpan(MeasureType.CUSTOM.value)
    def add_metric(self, record: dict) -> None:
        """Add metrics in icinga."""
        try:
            encoded_servicename = icinga.get_encoded_servicename(row=record)
            client.add(object_type=self.icinga_objectType, name=encoded_servicename, attrs=record)
            self.to_process_check.append(record)
        except Exception as exc:
            logging.error(f"Error adding metric to metric to icinga : {record} - {exc}")

    @CaptureSpan(MeasureType.CUSTOM.value)
    def pc_metric(self, record: dict) -> None:
        """Process check metrics in icinga."""
        try:
            servicename = icinga.get_servicename(row=record)
            client.process_check_result(
                object_type=self.icinga_objectType,
                name=servicename,
                plugin_output=record["vars.metric_description"],
                exit_status=enums.IcingaServiceStatus.OK.value,
                check_command="passive",
            )
        except Exception as exc:
            logging.exception(f"Error processing check metric in icinga : {record} - {exc}")

    @CaptureSpan(MeasureType.CUSTOM.value)
    def get_services_from_icinga(self) -> list[dict]:
        """Get all services from icinga."""
        return client.get_all(object_type=self.icinga_objectType, filters=f'"{self.source}" in service.groups')

    @CaptureSpan(MeasureType.CUSTOM.value)
    def get_services(self) -> list[str]:
        """Get names of service according to a service group."""
        services = self.get_services_from_icinga()
        if services:
            return self._get_service_name_icinga(services)
        client.create_group(name=self.source, display_name=self.source, group_type=enums.IcingaObjectType.SERVICE_GROUP)
        return []

    @staticmethod
    @CaptureSpan(MeasureType.CUSTOM.value)
    def _get_service_name_icinga(services: list | Any) -> list[str]:
        """Retrieve the service name from the icinga call."""
        service_names = [service["name"] for service in services]
        return service_names

    @CaptureSpan(MeasureType.CUSTOM.value)
    def get_services_to_check(self) -> list[tuple]:
        """Get services to process check from icinga."""
        services = client.get_all(
            object_type=self.icinga_objectType,
            filters=f'match({self.service_pending_state}, service.severity) && ("{self.source}" in service.groups)',
        )
        d_condition: list[tuple] = []
        if services and len(services) <= self.max_pending_service:
            # If we have more than 500 pending, it is preferable to make a purge
            # base and retry all synchronisation for this groups
            for service in services:
                d_condition.append(
                    (
                        service["attrs"]["host_name"],
                        service["attrs"]["vars"]["metric_type"],
                        service["attrs"]["vars"]["metric_name"],
                    )
                )
        else:
            self.delete_service_from_icinga(services)
        return d_condition

    @CaptureSpan(MeasureType.CUSTOM.value)
    def delete_service_from_icinga(self, services_to_delete: list[dict] | None = None) -> None:
        """Delete all services group from icinga with parallelization enabled."""
        if isinstance(services_to_delete, list):
            services = services_to_delete
        else:
            services = self.get_services_from_icinga()
        services_name = [
            icinga.encode_string(service["name"])
            for service in services
            if service["attrs"]["state"] in [enums.IcingaServiceStatus.OK, enums.IcingaServiceStatus.UNKNOWN]
        ]
        parallelize_process(services_name, self.delete_service, config.thread_number)
        client.show_counter_values()
        client.reset_counter()

    @CaptureSpan(MeasureType.CUSTOM.value)
    def delete_service(self, name: str) -> None:
        """Delete a service from icinga."""
        client.delete(object_type=self.icinga_objectType, name=name)

    @CaptureSpan(MeasureType.CUSTOM.value)
    def delete_pending_service(self) -> None:
        """Allow to delete pending services if whe don't have description from UCMDB to make process check."""
        services = client.get_all(
            object_type=self.icinga_objectType,
            filters=f'match({self.service_pending_state}, service.severity) && ("{self.source}" in service.groups)',
        )
        self.delete_service_from_icinga(services)
